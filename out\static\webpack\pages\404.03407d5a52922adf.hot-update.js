"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/404",{

/***/ "(pages-dir-browser)/./src/utils/storeApp.js":
/*!*******************************!*\
  !*** ./src/utils/storeApp.js ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(pages-dir-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(pages-dir-browser)/./node_modules/zustand/esm/middleware.mjs\");\n\n\nconst userStore = (set)=>({\n        isUserLogedIn: false,\n        formURL: \"\",\n        appPool: \"\",\n        currentPageURL: \"\",\n        userForm: {},\n        backPageButton: [],\n        collegeObj: [],\n        moneyObj: [],\n        userObject: {},\n        pageIndex: \"\",\n        backButtonMode: false,\n        addCollegeFlag: false,\n        dollarFormat: false,\n        hideLogin: false,\n        SSNValue: \"\",\n        AppReviewNr: 0,\n        setAppReviewNr: (val)=>{\n            set((state)=>({\n                    AppReviewNr: val\n                }));\n        },\n        setHideLogin: (val)=>{\n            set((state)=>({\n                    hideLogin: val\n                }));\n        },\n        setSSNValue: (val)=>{\n            set((state)=>({\n                    SSNValue: val\n                }));\n        },\n        setDollarFormat: (flag)=>{\n            set((state)=>({\n                    dollarFormat: flag\n                }));\n        },\n        setMoneyObj: (obj)=>{\n            set((state)=>({\n                    moneyObj: obj.length === 0 ? [] : [\n                        ...state.moneyObj,\n                        obj\n                    ]\n                }));\n        },\n        setCollegeObj: (obj)=>{\n            set((state)=>({\n                    collegeObj: obj\n                }));\n        },\n        setAaddCollegeFlag: (flg)=>{\n            set((state)=>({\n                    addCollegeFlag: flg\n                }));\n        },\n        setAppPool: (site)=>{\n            set((state)=>({\n                    appPool: site\n                }));\n        },\n        setBackButtonMode: (mode)=>{\n            set((state)=>({\n                    backButtonMode: mode\n                }));\n        },\n        setPageIndex: (idx)=>{\n            set((state)=>({\n                    pageIndex: idx\n                }));\n        },\n        setBackPageButton: (obj)=>{\n            set((state)=>({\n                    backPageButton: obj.length === 0 ? [] : obj\n                }));\n        },\n        setUserLogedIn: (flag)=>{\n            set((state)=>({\n                    isUserLogedIn: flag\n                }));\n        },\n        setpageUrl: (page)=>{\n            set((state)=>({\n                    currentPageURL: page\n                }));\n        },\n        setpageFormUrl: (page)=>{\n            set((state)=>({\n                    formURL: page\n                }));\n        },\n        setUserForm: (form)=>{\n            set((state)=>({\n                    userForm: form\n                }));\n        },\n        updateUserForm: (key, val)=>{\n            set((state)=>({\n                    userForm: {\n                        ...state.userForm,\n                        [key]: val\n                    }\n                }));\n        }\n    });\nconst useUserStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.devtools)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)(userStore, {\n    name: \"user\"\n})));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useUserStore);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/utils/storeApp.js\n"));

/***/ })

});