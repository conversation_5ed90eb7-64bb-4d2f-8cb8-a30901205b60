import React from "react"
import { useStaticQuery, graphql } from "gatsby"
import ButtonLink from "./buttonLink"

const SectionLink = ({
  href,
  children,
  answeredQuestionsCount,
  requiredQuestionsCount,
}) => {
  // We have optional questions, so the number of answered questions might be
  // greater than the number of required questions.
  const answeredAllTheRequiredQuestions =
    answeredQuestionsCount >= requiredQuestionsCount
  const buttonVariant = answeredAllTheRequiredQuestions ? "green" : "white"
  function SectionDescription({
    isCompleted,
    answeredQuestionsCount,
    requiredQuestionsCount,
  }) {
    if (isCompleted) return <>This section is complete</>
    if (requiredQuestionsCount === 1)
      return (
        <>
          <b>1</b> question
        </>
      )

    return (
      <>
        <b>
          {answeredQuestionsCount}-{requiredQuestionsCount}
        </b>{" "}
        sets of questions
      </>
    )
  }
  function CompletedIcon() {
    return (
      <img
        src={data.checkmark.publicURL}
        alt="Completed"
        height={18}
        width={23}
        className="inline pr-2"
      />
    )
  }
  const data = useStaticQuery(graphql`
    query {
      checkmark: file(relativePath: { eq: "checkmark.svg" }) {
        name
        publicURL
      }
    }
  `)
  return (
    <div className="w-full space-y-3">
      <ButtonLink
        variant={buttonVariant}
        href={href}
        className="flex w-full gap-2.5"
      >
        {answeredAllTheRequiredQuestions && <CompletedIcon />}
        {children}
      </ButtonLink>

      <p className="pl-6 text-base text-gray-600">
        <SectionDescription
          isCompleted={answeredAllTheRequiredQuestions}
          answeredQuestionsCount={answeredQuestionsCount}
          requiredQuestionsCount={requiredQuestionsCount}
        />
      </p>
    </div>
  )
}
export default SectionLink
