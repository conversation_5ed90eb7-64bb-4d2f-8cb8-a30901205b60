import React from "react";
import ReactTooltip from "react-tooltip";

const DocTable = ({ CollegeList }) => {
  const [tooltipThree, setTooltipThree] = React.useState(false);
  const infoIcon = (
    <img
      className="text-black inline "
      src={`${process.env.IMG_URL}/info-icon.svg`}
      alt="Information Icon"
      height={16}
      width={16}
    />
  );

  return (
    <div className="overflow-auto">
      <table class="table w-full text-md md:text-sm">
        <thead class="table-header-group bg-sky-50 ">
          <tr class="table-row">
            <th class="table-cell text-left p-2">
              Documents-Required by Institution(s){" "}
              <a
                aria-label="Documents-Required by Institution(s) more information"
                aria-expanded={tooltipThree}
                aria-describedby={"InstitutionDoc"}
                aria-controls="InstitutionDoc"
                data-tip="These are the documents your institutions requested that you
                submit. Please check your institution's information (website,
                etc.) to ensure that you provide all required documentation."
                data-for="InstitutionDoc"
                currentitem="false"
                href="javascript:void(0);"
              >
                {infoIcon}
              </a>
              <ReactTooltip
                globalEventOff="click"
                id="InstitutionDoc"
                place="top"
                effect="solid"
                className="max-w-md"
                event="click"
                role="tooltip"
                afterShow={() => {
                  setTooltipThree(true);
                }}
                afterHide={() => {
                  setTooltipThree(false);
                }}
                closeOnEsc={() => {
                  setTooltipThree(false);
                }}
              />
            </th>
            <th class="table-cell text-left p-2">Owner</th>
            <th class="table-cell text-left p-2">Institution Requesting</th>
          </tr>
        </thead>
        <tbody class="table-row-group bg-yellow-50 ">
          {CollegeList?.map((el, idx) => (
            <tr key={idx} className={`table-row `}>
              <td class="table-cell text-left p-2">{el.DocumentName}</td>
              <td class="table-cell text-left p-2">{el.DocumentOwner}</td>
              <td class="table-cell text-left p-2">
                {el.CollegesRequestingDocList.map((ex) => (
                  <span>{`${ex.CSSCode} `}</span>
                ))}{" "}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
export default DocTable;
