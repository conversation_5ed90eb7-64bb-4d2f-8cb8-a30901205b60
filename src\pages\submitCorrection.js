import React, { useState } from "react";
import { navigate } from "gatsby";
import { Button } from "../components/Button";
import Layout from "../components/layout";
import Helmet from "react-helmet";
import { ValidationError } from "../components/ValidationError";
import { ProcessingModal } from "../components/processingModal";
import axios from "axios";
import useUserStore from "../utils/storeApp";
import WithLocation from "../components/withLocation";

const SubmitCorrection = () => {
  const [msg, SetMsg] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [disableBtn, SetdisableBtn] = useState(false);
  const { isUserLogedIn, setpageFormUrl, userForm, setUserForm, appPool } =
    useUserStore((state) => ({
      userForm: state.userForm,
      isUserLogedIn: state.isUserLogedIn,
      setpageFormUrl: state.setpageFormUrl,
      setUserForm: state.setUserForm,
      appPool: state.appPool,
    }));

  const termsFormSubmit = async (event) => {
    event.preventDefault();
    SetdisableBtn(true);
    setShowModal(true);
    const formUser = {
      Username: userForm.Username,
      UserId: userForm.UserId,
      AwardYear: userForm.AwardYear,
      ApplicationId: userForm.ApplicationId,
      OrgFormId: userForm.OrgFormId,
      FormId: userForm.FormId,
      PageName: "SubmitCorrection",
      Token: userForm.Token,
    };
    const bodyFormData = new FormData();
    bodyFormData.append("sbr", JSON.stringify(formUser));
    await axios({
      method: "post",
      url: process.env.API_URL + "correction/PostSubmitCorrection",
      data: formUser,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userForm.Token}`,
      },
    })
      .then((res) => {
        setShowModal(false);
        if (res.data.ReturnMessage.toUpperCase() === "SUCCESS") {
          SetdisableBtn(false);
          setUserForm({ ...res.data, Token: userForm.Token });
          setpageFormUrl(res.data.PageName);
          navigate(`/dashboard`);
        } else if (
          res.data.ReturnMessage.toUpperCase() === "FAILEDVALIDATION"
        ) {
          SetdisableBtn(false);
          const errApi = res.data.QuestionErrorList.map((err, idx) => (
            <p key={idx}>{err.ErrorMsg}</p>
          ));
          SetMsg(errApi);
          setShowModal(false);
          return;
        }
      })
      .catch((err) => {
        SetdisableBtn(false);
        SetMsg("Server error has occured.");
        return;
      });
  };
  React.useEffect(() => {
    const fetchData = async () => {
      if (!isUserLogedIn) {
        navigate("https://account.collegeboard.org");
      }
    };
    fetchData();
  }, []);
  const goBackToPage = async () => {
    setpageFormUrl("Correction");
    navigate("/payment");
  };
  return (
    <Layout>
      <Helmet
        htmlAttributes={{
          lang: "en",
        }}
        title={"Terms and Conditions"}
      />
      <div className="w-xl space-y-5 bg-white p-7 md:p-11 md:max-w-lg md:shadow-md md:shadow-light-purple mt-12 md:mt-6">
        {showModal ? <ProcessingModal /> : null}
        <form
          onSubmit={(event) => termsFormSubmit(event)}
          className="space-y-5"
        >
          <h1 className="md:text-xl text-lg font-bold">
            <a href="#nolinkID" id="GoBackBtn1" className="cursor-text">
              Submit Correction
            </a>
          </h1>
          <p>
            If you are ready to submit your Correction click Submit.  Please
            note that you can only correct your CSS Profile one time
          </p>
          <Button disabled={disableBtn} className="w-full md:w-fit">
            Submit
          </Button>
        </form>
        <hr />

        <a
          href="#nolinkID"
          className="aStyle md:text-sm text-md grid justify-items-end"
          onClick={() => goBackToPage()}
        >
          ←Go Back
        </a>

        {msg ? <ValidationError message={msg} /> : null}
      </div>
    </Layout>
  );
};
export default WithLocation(SubmitCorrection);
