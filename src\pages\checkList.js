import React, { useContext, useEffect, useState } from "react";
import { navigate } from "gatsby";
import { checklistPageFromJSON, saveFormReview } from "../components/NewComp";
import useUserStore from "../utils/storeApp";
import Layout from "../components/layout";
import axios from "axios";
import { Heading } from "../components/Heading";
import Helmet from "react-helmet";
import { Button } from "../components/Button";
import Card from "../components/Card";
import { ValidationError } from "../components/ValidationError";
import { ProcessingModal } from "../components/processingModal";
import WithLocation from "../components/withLocation";

const SecReviewPage = () => {
  const {
    isUserLogedIn,
    userForm,
    formURL,
    setUserForm,
    setpageFormUrl,
    currentPageURL,
    setpageUrl,
    backPageButton,
    setBackPageButton,
    pageIndex,
    setPageIndex,
    backButtonMode,
    setBackButtonMode,
  } = useUserStore((state) => ({
    isUserLogedIn: state.isUserLogedIn,
    userForm: state.userForm,
    formURL: state.setUserformURLLogedIn,
    setUserForm: state.setUserForm,
    setpageFormUrl: state.setpageFormUrl,
    currentPageURL: state.currentPageURL,
    setpageUrl: state.setpageUrl,
    backPageButton: state.backPageButton,
    setBackPageButton: state.setBackPageButton,
    pageIndex: state.pageIndex,
    setPageIndex: state.setPageIndex,
    backButtonMode: state.backButtonMode,
    setBackButtonMode: state.setBackButtonMode,
  }));

  const [flag, SetFlag] = useState(false);
  const [msg, SetMsg] = useState("Error trying to fetch data.");
  const [formData, SetFormData] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [showBackBtn, setShowBackBtn] = useState(true);
  const [disableBtn, SetdisableBtn] = useState(false);
  const [index, SetIndex] = useState(0);

  const submitReview = async (event) => {
    SetdisableBtn(true);
    const formReview = {
      Username: userForm.Username,
      UserId: userForm.UserId,
      AwardYear: userForm.AwardYear,
      ApplicationId: userForm.ApplicationId,
      OrgFormId: userForm.OrgFormId,
      FormId: userForm.FormId,
      Token: userForm.Token,
    };
    setBackButtonMode(false);
    const res = await saveFormReview(formReview);
    if (res.status === "SUCCESS") {
      SetdisableBtn(false);
      SetFlag(false);
      setUserForm(res.data);
      setpageFormUrl(res.data.PageName);
      if (res.data.PageName === "MissingInformation") {
        navigate("/missingInfo");
      } else if (res.data.PageName === "DataChecks") {
        navigate("/dataChecks");
      } else if (res.data.PageName === "ApplicationReviewIntro") {
        navigate("/appReviewIntro");
      }
    } else if (res.status === "FAILED") {
      SetdisableBtn(false);
      if (res.data.ErrorMessage) {
        SetFlag(true);
        SetMsg(res.data.ErrorMessage);
      }
    } else if (res.status === "SERVERERROR") {
      SetdisableBtn(false);
      SetFlag(true);
      SetMsg(res.data);
    }
  };
  useEffect(() => {
    const fetchData = async () => {
      setShowModal(true);
      const formUser = {
        ...userForm,
        MultipleParentName: "",
      };
      const newForm = {
        Username: userForm.Username,
        UserId: userForm.UserId,
        AwardYear: userForm.AwardYear,
        ApplicationId: userForm.ApplicationId,
        OrgFormId: userForm.OrgFormId,
        FormId: userForm.FormId,
        PageName: "AppCheckList",
        Token: userForm.Token,
      };
      setUserForm(newForm);
      setBackPageButton([...backPageButton, newForm]);
      if (!backButtonMode) {
        setPageIndex(backPageButton.length - 1);
      }
      await axios({
        method: "post",
        url: process.env.API_URL + "navigation/GetApplicationChecklist",
        data: formUser,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userForm.Token}`,
        },
      })
        .then((res) => {
          setShowModal(false);
          if (
            res.status === 200 ||
            res.data.ReturnMessage.toUpperCase() === "SUCCESS"
          ) {
            const js = checklistPageFromJSON(res.data);
            console.log(res);
            SetFormData(js);
            SetFlag(false);
          } else if (res.data.returnMessage === "SERVERERROR") {
            SetFlag(true);
            SetMsg("Server error has occured.");
            return;
          } else if (res.data.ReturnMessage === "FAILED") {
            SetFlag(true);
            return;
          }
        })
        .catch((err) => {
          SetMsg("Server error has occured.");
          setShowModal(false);
          return;
        });
      // }
    };
    fetchData();
    document.getElementById("scrollToTop").scrollIntoView();
    document.getElementById("scrollToTop").focus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const goToNextPage = (url, menuName, multiple) => {
    const formUser = {
      ...userForm,
      MenuName: menuName,
      PageName: url,
      Multiple: multiple,
    };
    setBackButtonMode(false);
    setUserForm(formUser);
    setpageFormUrl(url);
    if (multiple === "Y") {
      navigate("/multiple");
    } else if (url === "AcademicInfo4") {
      navigate("/program");
    } else {
      navigate("/formpage");
    }
  };
  const goBackToPage = () => {
    setBackButtonMode(true);
    const goBack = backPageButton[pageIndex];
    setPageIndex(pageIndex - 1);
    setUserForm(goBack);
    setpageFormUrl(goBack.PageName);
    if (
      // goBack.PageName === "AcademicInfo3" ||
      goBack.PageName === "AcademicInfo3a"
    ) {
      navigate("/school");
    } else if (goBack.PageName === "AcademicInfo4") {
      navigate("/program");
    } else if (
      goBack.PageName === "Correction" ||
      goBack.PageName === "ApplicationCertification"
    ) {
      navigate("/payment");
    } else if (goBack.Multiple === "Y") {
      navigate("/multiple");
    } else if (goBack.PageName === "ApplicationReviewIntro") {
      navigate("/appReviewIntro");
    } else {
      navigate("/formpage");
    }
  };
  const handleIncrement = () => {
    SetIndex((index) => index + 1);
    return index + 1;
  };
  return (
    <Layout>
      <Helmet
        htmlAttributes={{
          lang: "en",
        }}
        title={formData.title ? formData.title : "Application Checklist"}
      />
      <div className="mx-auto space-y-5 bg-white p-7 md:max-w-lg md:p-11 md:shadow-md md:shadow-gray-500 mt-12 md:mt-6">
        {showModal ? <ProcessingModal /> : null}
        {flag && <ValidationError message={msg} />}
        {formData.title ? (
          <h1
            className="md:text-xl text-lg font-bold"
            tabindex="-1"
            id="GoBackBtn1"
          >
            {formData.title}
          </h1>
        ) : null}
        {formData.text ? <p>{formData.text}</p> : null}

        {formData?.questions?.map((elem, currentPosition) => {
          return (
            <Card
              key={elem.TextContent}
              section={elem.Section}
              completed={elem.Completed}
              disabled={elem.Disabled}
              textContent={elem.TextContent}
              pageTo={elem.PageTo}
              length={formData?.questions.length}
              currentPosition={currentPosition}
              onClick={(value) => {
                goToNextPage(value, elem.MenuName, elem.Multiple);
              }}
            />
          );
        })}
        {formData.isCompleted === "Y" ? (
          <div className="border-gray-3  space-y-5 rounded-[32px] bg-sky-200 p-7">
            {formData.footHeading && (
              <h2 className="text-2xl font-bold">{formData.footHeading}</h2>
            )}
            {formData.review && <p>{formData.review}</p>}
            <Button
              disabled={disableBtn}
              className="w-full md:w-fit"
              onClick={() => submitReview()}
            >
              Review My Application
            </Button>
          </div>
        ) : (
          <div className="border-gray-3 space-y-5 rounded-[32px] bg-gray-200 p-7">
            {formData.footHeading && (
              <h2 className="text-2xl font-bold">{formData.footHeading}</h2>
            )}
            {formData.footText && <p>{formData.footText}</p>}
          </div>
        )}
        <hr />
        {currentPageURL &&
        currentPageURL !== "dashboard" &&
        pageIndex > 0 &&
        showBackBtn ? (
          <p className="font-bold md:text-sm text-md grid justify-items-end">
            <a
              href="#nolinkID"
              className="aStyle"
              onClick={() => goBackToPage("ApplicationChecklist")}
            >
              ←Go Back
            </a>
          </p>
        ) : null}
      </div>
    </Layout>
  );
};

export default WithLocation(SecReviewPage);
