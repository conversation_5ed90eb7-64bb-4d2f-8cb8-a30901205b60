import React, { useEffect, useState } from "react";
import { navigate } from "gatsby";
import { saveAppReview } from "../components/NewComp";
import parse from "html-react-parser";
import Layout from "../components/layout";
import Helmet from "react-helmet";
import { Button } from "../components/Button";
import { ReviewCard } from "../components/ReviewCard";
import { ValidationError } from "../components/ValidationError";
import { ProcessingModal } from "../components/processingModal";
import useUserStore from "../utils/storeApp";
import WithLocation from "../components/withLocation";

const SecReviewPage = () => {
  const {
    setpageFormUrl,
    userForm,
    isUserLogedIn,
    setUserForm,
    setpageUrl,
    backPageButton,
    setBackPageButton,
    pageIndex,
    setPageIndex,
    backButtonMode,
    setBackButtonMode,
    setAppReviewNr,
    AppReviewNr,
  } = useUserStore((state) => ({
    setpageFormUrl: state.setpageFormUrl,
    setUserForm: state.setUserForm,
    userForm: state.userForm,
    isUserLogedIn: state.isUserLogedIn,
    setpageUrl: state.setpageUrl,
    backPageButton: state.backPageButton,
    setBackPageButton: state.setBackPageButton,
    pageIndex: state.pageIndex,
    setPageIndex: state.setPageIndex,
    backButtonMode: state.backButtonMode,
    setBackButtonMode: state.setBackButtonMode,
    setAppReviewNr: state.setAppReviewNr,
    AppReviewNr: state.AppReviewNr,
  }));

  const [flag, SetFlag] = useState(false);
  const [urlPage, SetUrlPage] = useState(Date.now());
  const [msg, SetMsg] = useState("");
  const [formData, SetFormData] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [disableBtn, SetdisableBtn] = useState(false);

  const onFormSubmition = async (event) => {
    SetdisableBtn(true);
    event.preventDefault();
    setBackButtonMode(false);
    const formReview = {
      Username: userForm.Username,
      UserId: userForm.UserId,
      AwardYear: userForm.AwardYear,
      ApplicationId: userForm.ApplicationId,
      OrgFormId: userForm.OrgFormId,
      FormId: userForm.FormId,
      PageName: "ApplicationReview",
      SectionName: userForm.SectionName,
      Token: userForm.Token,
    };
    const res = await saveAppReview(formReview);
    if (res.status === "SUCCESS") {
      SetFlag(false);
      SetdisableBtn(false);
      setUserForm({ ...res.data, Token: userForm.Token });
      if (res.data.SectionName === "" && res.data.FWSectionList.length === 0) {
        navigate("/payment");
      } else {
        SetUrlPage(Date.now());
      }
    } else if (res.status === "FAILED") {
      SetFlag(true);
      SetMsg(res.data.ErrorMessage);
      SetdisableBtn(false);
    } else if (res.status === "SERVERERROR") {
      SetFlag(true);
      SetMsg(res.data);
      SetdisableBtn(false);
    }
  };
  useEffect(() => {
    const fetchData = async () => {
      if (!isUserLogedIn) {
        navigate("https://account.collegeboard.org");
      }
      setpageUrl("appReview");
      setBackPageButton([...backPageButton, userForm]);
      if (!backButtonMode) {
        setPageIndex(backPageButton.length - 1);
      }
      let nr = 0;
      userForm?.FWSectionList?.map((card, ix) => {
        if (card.DisplayLinkForEdit) nr++;
      });
      setAppReviewNr(nr);
      setShowModal(true);
      SetFormData(userForm);
      document.getElementById("scrollToTop").scrollIntoView();
      document.getElementById("scrollToTop").focus();
      setShowModal(false);
    };
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [urlPage]);
  const goToNextPage = async (page, menu, multiple, multipleParentName) => {
    const frm = {
      Username: userForm.Username,
      UserId: userForm.UserId,
      AwardYear: userForm.AwardYear,
      ApplicationId: userForm.ApplicationId,
      OrgFormId: userForm.OrgFormId,
      FormId: userForm.FormId,
      PageName: page,
      MenuName: menu,
      Multiple: multiple,
      MultipleParentName: multipleParentName,
      Token: userForm.Token,
    };
    setUserForm(frm);
    setpageFormUrl(page);
    if (page === "ApplicationReviewIntro") {
      navigate("/appReviewIntro");
    } else if (
      // page === "AcademicInfo3" ||
      page === "AcademicInfo3a"
    ) {
      navigate("/school");
    } else if (page === "AcademicInfo4") {
      navigate("/program");
    } else if (page === "ApplicationChecklist") {
      navigate("/checkList");
    } else if (multiple === "Y") {
      navigate("/multiple");
    } else {
      navigate("/formpage");
    }
  };
  const goBackToPage = async () => {
    setBackButtonMode(true);
    const goBack = backPageButton[backPageButton.length - 2];
    setPageIndex(pageIndex - 1);
    setUserForm(goBack);
    setpageFormUrl(goBack.PageName);
    if (goBack.PageName === "ApplicationReviewIntro") {
      navigate("/appReviewIntro");
    } else if (goBack.PageName === "ApplicationReview") {
      SetUrlPage(Date.now());
    }
    // else if (
    //   goBack.PageName === "AcademicInfo3" ||
    //   goBack.PageName === "AcademicInfo3a"
    // ) {
    //   navigate("/school");
    // }
    else if (goBack.PageName === "AcademicInfo4") {
      navigate("/program");
    } else if (
      goBack.PageName === "Correction" ||
      goBack.PageName === "Certification"
    ) {
      navigate("/payment");
    } else if (goBack.PageName === "AppCheckList") {
      navigate("/checkList");
    } else if (goBack.PageName === "FinalCharge") {
      navigate("/finalCharge");
    } else if (goBack.PageName === "ApplicationReview") {
      navigate("/appReview");
    } else if (goBack.Multiple === "Y") {
      navigate("/multiple");
    } else {
      navigate("/formpage");
    }
  };
  return (
    <Layout>
      <Helmet
        htmlAttributes={{
          lang: "en",
        }}
        title={formData.PageHeading}
      />
      <div className="mx-auto space-y-5 bg-white p-7 md:max-w-lg md:p-11 md:shadow-md md:shadow-gray-500 mt-12 md:mt-6">
        {showModal ? <ProcessingModal /> : null}
        {flag && <ValidationError message={msg} />}
        <hr />
        {formData.PageHeading ? (
          <h1
            className="md:text-xl text-lg font-bold"
            tabindex="-1"
            id="GoBackBtn1"
          >
            {formData.PageHeading}
          </h1>
        ) : null}
        {formData?.PageText && (
          <div className=" md:text-xl text-lg">{parse(formData.PageText)}</div>
        )}
        <form
          onSubmit={(event) => onFormSubmition(event)}
          className="space-y-5"
        >
          {formData?.FWSectionList?.map((card, ix) => {
            //if (card.DisplayLinkForEdit) setAppReviewNr(ix);
            return (
              <ReviewCard
                key={card.PageName}
                currentPosition={card.DisplayLinkForEdit ? ix + 1 : ""}
                pageTo={card.PageName}
                heading={card.PageHeading}
                showLink={card.DisplayLinkForEdit}
                list={card.FWFormItemList}
                multiList={card.MultipleItemList}
                onClick={() =>
                  goToNextPage(
                    card.PageName,
                    card.MenuName,
                    card.Multiple,
                    card.MultipleParentName
                  )
                }
              />
            );
          })}
          <Button disabled={disableBtn} className="w-full md:w-fit">
            Save and Continue
          </Button>
        </form>
        <hr />
        {pageIndex > 0 ? (
          <a
            href="javascript:void(0);"
            className="aStyle md:text-sm text-md grid justify-items-end"
            onClick={() => goBackToPage()}
          >
            ←Go Back
          </a>
        ) : null}
      </div>
    </Layout>
  );
};

export default WithLocation(SecReviewPage);
