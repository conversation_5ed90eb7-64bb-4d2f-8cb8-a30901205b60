import { create } from "zustand";

import { devtools, persist } from "zustand/middleware";

const userStore = (set) => ({
  isUserLogedIn: false,
  formURL: "",
  appPool: "",
  currentPageURL: "",
  userForm: {},
  backPageButton: [],
  collegeObj: [],
  moneyObj: new Set(),
  userObject: {},
  pageIndex: "",
  backButtonMode: false,
  addCollegeFlag: false,
  dollarFormat: false,
  hideLogin: false,
  SSNValue: "",
  AppReviewNr: 0,
  setAppReviewNr: (val) => {
    set((state) => ({
      AppReviewNr: (state.AppReviewNr = val === 0 ? 0 : state.AppReviewNr + 1),
    }));
  },
  setHideLogin: (val) => {
    set((state) => ({
      hideLogin: (state.hideLogin = val),
    }));
  },
  setSSNValue: (val) => {
    set((state) => ({
      SSNValue: (state.SSNValue = val),
    }));
  },
  setDollarFormat: (flag) => {
    set((state) => ({
      dollarFormat: (state.dollarFormat = flag),
    }));
  },
  setMoneyObj: (obj) => {
    set((state) => ({
      moneyObj: (state.moneyObj =
        obj.length === 0 ? [] : [...state.moneyObj, obj]),
    }));
  },
  setCollegeObj: (obj) => {
    set((state) => ({
      collegeObj: (state.collegeObj = obj),
    }));
  },
  setAaddCollegeFlag: (flg) => {
    set((state) => ({
      addCollegeFlag: (state.addCollegeFlag = flg),
    }));
  },
  setAppPool: (site) => {
    set((state) => ({
      appPool: (state.appPool = site),
    }));
  },
  setBackButtonMode: (mode) => {
    set((state) => ({
      backButtonMode: (state.backButtonMode = mode),
    }));
  },
  setPageIndex: (idx) => {
    set((state) => ({
      pageIndex: (state.pageIndex = idx),
    }));
  },
  setBackPageButton: (obj) => {
    set((state) => ({
      backPageButton: (state.backPageButton = obj.length === 0 ? [] : []),
    }));
  },
  setUserLogedIn: (flag) => {
    set((state) => ({
      isUserLogedIn: (state.isUserLogedIn = flag),
    }));
  },
  setpageUrl: (page) => {
    set((state) => ({
      currentPageURL: (state.currentPageURL = page),
    }));
  },
  setpageFormUrl: (page) => {
    set((state) => ({
      formURL: (state.formURL = page),
    }));
  },
  setUserForm: (form) => {
    set((state) => ({
      userForm: (state.userForm = form),
    }));
  },
  updateUserForm: (key, val) => {
    set((state) => ({
      userForm: (state.userForm = { ...state.userForm, key: val }),
    }));
  },
});

const useUserStore = create(
  devtools(
    persist(userStore, {
      name: "user",
    })
  )
);

export default useUserStore;
