{"name": "profile-css-app", "private": true, "description": "Profile CSS Site build with Gatsby", "author": "Alket Hoxha", "version": "0.1.0", "license": "0BSD", "scripts": {"build": "gatsby build --prefix-paths", "develop": "gatsby develop", "start": "gatsby develop", "serve": "gatsby serve", "clean": "gatsby clean", "test": "echo \"Write tests! -> https://gatsby.dev/unit-testing\" && exit 1"}, "dependencies": {"@adobe/adobe-client-data-layer": "^2.0.2", "@mui/x-date-pickers": "^5.0.18", "autoprefixer": "^10.4.20", "axios": "^1.7.7", "gatsby": "^5.14.5", "gatsby-plugin-image": "^3.14.0", "gatsby-plugin-manifest": "^5.14.0", "gatsby-plugin-postcss": "^6.14.0", "gatsby-plugin-react-helmet": "^6.14.0", "gatsby-plugin-sharp": "^5.14.0", "gatsby-plugin-use-query-params": "^1.0.1", "gatsby-source-filesystem": "^5.14.0", "gatsby-source-graphql": "^5.14.0", "gatsby-transformer-sharp": "^5.14.0", "html-react-parser": "^5.0.16", "moment": "^2.30.1", "postcss": "^8.4.47", "prop-types": "^15.8.1", "query-string": "^6.14.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-icons": "^5.3.0", "react-idle-timer": "^5.7.2", "react-tooltip": "^5.28.0", "tailwindcss": "^3.4.13", "use-query-params": "^1.2.3", "zustand": "^5.0.1"}, "devDependencies": {"prettier": "^3.3.3"}, "resolutions": {"query-string": "^6.14.1"}}