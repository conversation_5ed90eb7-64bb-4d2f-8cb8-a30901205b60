{"name": "css-profile-app", "private": true, "description": "CSSAPIProfile Site build with Gatsby", "author": "Alket Hoxha", "version": "0.1.0", "license": "0BSD", "scripts": {"build": "gatsby build --prefix-paths", "develop": "gatsby develop", "start": "gatsby develop", "serve": "gatsby serve", "clean": "gatsby clean", "test": "echo \"Write tests! -> https://gatsby.dev/unit-testing\" && exit 1"}, "dependencies": {"@adobe/adobe-client-data-layer": "^2.0.2", "@mui/x-date-pickers": "^5.0.18", "autoprefixer": "^10.4.13", "axios": "^1.3.3", "gatsby": "^5.12.5", "gatsby-plugin-image": "^3.6.0", "gatsby-plugin-manifest": "^5.6.0", "gatsby-plugin-postcss": "^6.6.0", "gatsby-plugin-react-helmet": "^6.6.0", "gatsby-plugin-sharp": "^5.6.0", "gatsby-source-filesystem": "^5.6.0", "gatsby-source-graphql": "^5.6.0", "gatsby-transformer-sharp": "^5.6.0", "html-react-parser": "^3.0.9", "moment": "^2.29.4", "postcss": "^8.4.21", "prop-types": "^15.8.1", "query-string": "^7.1.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-icons": "^4.7.1", "react-idle-timer": "^5.5.2", "react-tooltip": "^4.2.21", "tailwindcss": "^3.2.6", "use-query-params": "^2.2.1", "zustand": "^4.3.3"}, "devDependencies": {"prettier": "^2.8.4"}, "resolutions": {"query-string": "^7.1.3"}}