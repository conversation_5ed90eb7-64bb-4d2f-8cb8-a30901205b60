export const dtjson = {
  ReturnMessage: "",
  Username: "<EMAIL>",
  UserId: "PdcbBmhDEcMlmz$/dgwmaKGu3TYhnRecLq1D1ju6lK85VMIz6/wWWA==",
  AwardYear: "2024",
  ApplicationId: "n5qz02ASpQ$T3AgVFPNeJX1g//BmggvM829FvrjIsNJqIkNZ6em$7A==",
  OrgFormId: "lv35$iih2vBQU3GjoLob9mK7GGP2XJOhnRfIauWMICXp1LIUI15J2g==",
  PageName: "Certification",
  FormId: "CB000008141",
  CBFinAidID: "",
  PageHeading: "Certification",
  FWFormItemList: [
    {
      itemType: "text",
      id: "Text0",
      questionValue: "",
      required: "",
      requiredErrMsg: "",
      fieldType: "",
      size: "",
      minLength: "",
      maxLength: "",
      format: "",
      displayFormat: "",
      rangeHigh: "",
      rangeLow: "",
      dict: "",
      checkboxCheckedValue: "",
      questionMsg: "",
      condDsblChild: "",
      condDsblParFld: "",
      condDsblParVal: "",
      condDsblParValNOTEQ: "",
      condDisableQuestion: "",
      condReqChild: "",
      condReqParFld: "",
      condReqParVal: "",
      condReqOperator: "",
      condReqParValNOTEQ: "",
      validateFldCompare: "",
      validateOperator: "",
      validateVal: "",
      validateRGEX: "",
      validateErrMsg: "",
      helpContent: "",
      helpStyle: "",
      helpTitle: "",
      textContent:
        "All the information on this application is true and complete to the best om my knowledge. All the information on this application is true and complete to the best om my knowledge. I agree..............",
    },
    {
      itemType: "question",
      id: "f_stu_ward_court_cur",
      questionValue: "",
      required: "Y",
      requiredErrMsg: "",
      fieldType: "checkbox",
      size: "",
      minLength: "",
      maxLength: "",
      format: "",
      displayFormat: "",
      rangeHigh: "",
      rangeLow: "",
      dict: "",
      checkboxCheckedValue: "",
      questionMsg: "f_stu_ward_court_curMsg",
      conditionalRequireQuestions: [],
      conditionalDisableQuestions: [],
      validateOperator: "",
      validateVal: "",
      validateRGEX: "",
      validateErrMsg: "",
      helpContent: "",
      helpStyle: "",
      helpTitle: "",
      textContent: "I have read, understand, and agree to the statement above",
    },
  ],
  FWDictsList: {
    Dicts: [],
    Count: 0,
  },
}
