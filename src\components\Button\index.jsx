import React from "react"
import { buttonClasses } from "../shared/buttons.js"
import { focusWithinStyles } from "../shared/inputs"

export function Button({
  children,
  className = "",
  disabled = false,
  variant = "primary",
  ...props
}) {
  return (
    <button
      disabled={disabled}
      className={`${buttonClasses({
        variant,
      })} ${focusWithinStyles} ${className}`}
      {...props}
    >
      {children}
    </button>
  )
}
