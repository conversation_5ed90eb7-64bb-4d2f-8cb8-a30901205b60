import Link from "next/link"
import { buttonClasses } from "../shared/buttons.js"
import { focusStyles } from "../shared/inputs"

export function ButtonLink({
  children,
  href,
  className = "",
  variant = "primary",
  ...props
}) {
  return (
    <Link href={href}>
      <a
        className={`${buttonClasses({ variant })} ${focusStyles} ${className}`}
        {...props}
      >
        {children}
      </a>
    </Link>
  )
}
