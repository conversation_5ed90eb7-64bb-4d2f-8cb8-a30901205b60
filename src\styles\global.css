@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell,
    Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  background-color: rgb(250,250,250);
  line-height: 1.5rem;
  font-size: 1.125rem;
}

a {
  color: inherit;
  text-decoration: none;
}

.aStyle {
  color: rgb(3 105 161 / var(--tw-bg-opacity));;
  text-decoration: underline;
  font-weight:bold;
  filter: contrast(100%);
  filter: brightness(100%);
  cursor:pointer
}
* {
  box-sizing: border-box;
}

details > summary {
  list-style: none;
}
details > summary::-webkit-details-marker {
  display: none;
}

select {
  background-image: url("../../static/expand.svg");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 11px 9px;
}
ol {
  list-style-type: decimal;
}