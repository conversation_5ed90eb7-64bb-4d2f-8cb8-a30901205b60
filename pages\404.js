import React from "react";
import Layout from "../src/components/layout";
import Head from "next/head";
import { Button } from "../src/components/Button";
import { useRouter } from "next/router";

export default function NotFound() {
  const router = useRouter();

  return (
    <Layout>
      <Head>
        <title>Page Not Found</title>
        <meta name="description" content="Page not found" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      <main className="mx-auto space-y-5 bg-white p-7 md:p-11 md:max-w-lg md:shadow-md md:shadow-light-purple mt-12 md:mt-6">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-gray-700 mb-4">Page Not Found</h2>
          <p className="text-gray-600 mb-8">
            The page you are looking for doesn't exist or has been moved.
          </p>
          <Button
            onClick={() => router.push("/")}
            className="w-full md:w-fit"
          >
            Go Home
          </Button>
        </div>
      </main>
    </Layout>
  );
}
