import React from "react"
import { But<PERSON> } from "../components/Button"

export function ConfirmModal({ msg, onClick, type }) {
  return (
    <>
      <div className="justify-center md:mt-6 items-top flex overflow-x-hidden fixed inset-20 z-50 ">
        <div className="relative w-auto ">
          {/*content*/}
          <div className="border-0 rounded-md shadow-lg relative flex flex-col w-full bg-white outline-none focus:outline-none">
            <div className="flex items-center justify-center p-3 rounded-t">
              <div className="mx-auto space-y-5 bg-gray-50 p-7 md:max-w-md  md:gray-50">
                <h1 className="font-bold md:text-lg text-xl">{msg}</h1>
                <div className="mx-auto space-x-2">
                  <Button
                    className="text-center w-full md:w-fit"
                    onClick={el => onClick(type)}
                  >
                    {"Confirm"}
                  </Button>
                  <Button
                    className="text-center w-full md:w-fit"
                    variant="secondary"
                    onClick={el => onClick("cancel")}
                  >
                    {"Cancel"}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="opacity-25 fixed inset-0 z-40 bg-black"></div>
    </>
  )
}
