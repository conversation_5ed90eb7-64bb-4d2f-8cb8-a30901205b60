import React, { useContext, useEffect, useState } from "react";
import { navigate } from "gatsby";
import Layout from "../components/layout";
import { getSaveCert } from "../components/NewComp";
import axios from "axios";
import { Button } from "../components/Button";
import parse from "html-react-parser";
import Helmet from "react-helmet";
import { ValidationError } from "../components/ValidationError";
import { ProcessingModal } from "../components/processingModal";
import useUserStore from "../utils/storeApp";
import WithLocation from "../components/withLocation";

const FinalCharge = ({ ttl, content }) => {
  const {
    setUserLogedIn,
    setpageFormUrl,
    userForm,
    isUserLogedIn,
    formURL,
    setUserForm,
    setpageUrl,
    backPageButton,
    setBackPageButton,
    pageIndex,
    setPageIndex,
    backButtonMode,
    setBackButtonMode,
  } = useUserStore((state) => ({
    setUserLogedIn: state.setUserLogedIn,
    setpageFormUrl: state.setpageFormUrl,
    setUserForm: state.setUserForm,
    userForm: state.userForm,
    formURL: state.formURL,
    isUserLogedIn: state.isUserLogedIn,
    setpageUrl: state.setpageUrl,
    backPageButton: state.backPageButton,
    setBackPageButton: state.setBackPageButton,
    pageIndex: state.pageIndex,
    setPageIndex: state.setPageIndex,
    backButtonMode: state.backButtonMode,
    setBackButtonMode: state.setBackButtonMode,
  }));

  const [flag, SetFlag] = useState(false);
  const [urlPage, SetUrlPage] = useState(formURL);
  const [msg, SetMsg] = useState("");
  const [formData, SetFormData] = useState({});
  const [answers, setAnswers] = useState();
  const [showModal, setShowModal] = React.useState(false);
  const [disableBtn, SetdisableBtn] = useState(false);
  const SubmitData = async () => {
    SetdisableBtn(true);
    const frm = {
      Username: userForm.Username,
      UserId: userForm.UserId,
      AwardYear: userForm.AwardYear,
      ApplicationId: userForm.ApplicationId,
      OrgFormId: userForm.OrgFormId,
      FormId: userForm.FormId,
      ApplicationChargeId: userForm.ApplicationChargeId,
      Token: userForm.Token,
    };
    const dt = await getSaveCert("payment/PostSubmit", frm);
    if (dt.status === "SUCCESS") {
      SetdisableBtn(false);
      SetFlag(false);
      navigate("/dashboard");
    } else if (dt.status === "FAILED") {
      SetdisableBtn(false);
      SetFlag(true);
      SetMsg(dt.data.ErrorMessage);
    } else if (dt.status === "SERVERERROR") {
      SetdisableBtn(false);
      SetFlag(true);
      SetMsg(dt.data);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!isUserLogedIn) {
        navigate("https://account.collegeboard.org");
      }
      setpageUrl("finalCharge");
      setShowModal(true);
      const formUser = {
        Username: userForm.Username,
        UserId: userForm.UserId,
        AwardYear: userForm.AwardYear,
        ApplicationId: userForm.ApplicationId,
        OrgFormId: userForm.OrgFormId,
        FormId: userForm.FormId,
        PageName: "ApplicationCharge",
        Token: userForm.Token,
      };
      let urlDirect = process.env.API_URL + "Payment/GetApplicationCharge";
      setBackPageButton([...backPageButton, formUser]);
      if (!backButtonMode) {
        setPageIndex(backPageButton.length - 1);
      }
      await axios({
        method: "post",
        url: urlDirect,
        data: formUser,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userForm.Token}`,
        },
        withCredentials: true,
      })
        .then((res) => {
          setShowModal(false);
          if (res.status === 200) {
            SetFormData(res.data);
            const frm = {
              Username: userForm.Username,
              UserId: userForm.UserId,
              AwardYear: userForm.AwardYear,
              ApplicationId: userForm.ApplicationId,
              OrgFormId: userForm.OrgFormId,
              FormId: userForm.FormId,
              PageName: res.data.PageName,
              BalanceDueAmount: res.data.BalanceDueAmount,
              ApplicationChargeId: res.data.ApplicationChargeId,
              Token: userForm.Token,
            };
            setUserForm(frm);
            SetFlag(false);
          } else if (res.data.returnMessage === "SERVERERROR") {
            SetFlag(true);
            SetMsg("Server error has occured.");
          } else if (res.data.ReturnMessage === "FAILED") {
            SetFlag(true);
            SetMsg("No data found.");
          }
        })
        .catch((err) => {
          SetMsg("Server error has occured.");
          SetFlag(true);
          setShowModal(false);
        });
    };
    fetchData();
    document.getElementById("scrollToTop").scrollIntoView();
    document.getElementById("scrollToTop").focus();
  }, [urlPage]);
  const goToNextPage = async (url) => {
    setpageFormUrl(url);
    setBackButtonMode(false);
    navigate("/payment");
  };
  const goBackToPage = async () => {
    setBackButtonMode(true);
    const goBack = backPageButton[pageIndex];
    setPageIndex(pageIndex - 1);
    setUserForm(goBack);
    setpageFormUrl(goBack.PageName);
    if (
      // goBack.PageName === "AcademicInfo3" ||
      goBack.PageName === "AcademicInfo3a"
    ) {
      navigate("/school");
    } else if (goBack.PageName === "AcademicInfo4") {
      navigate("/program");
    } else if (
      goBack.PageName === "Correction" ||
      goBack.PageName === "Certification" ||
      goBack.PageName === "ApplicationCertification"
    ) {
      navigate("/payment");
    } else if (goBack.PageName === "AppCheckList") {
      navigate("/checkList");
    } else if (goBack.PageName === "FinalCharge") {
      navigate("/finalCharge");
    } else if (goBack.PageName === "ApplicationReview") {
      navigate("/appReview");
    } else if (goBack.Multiple === "Y") {
      navigate("/multiple");
    } else {
      navigate("/formpage");
    }
  };
  return (
    <Layout>
      <Helmet
        htmlAttributes={{
          lang: "en",
        }}
        title={formData.PageHeading}
      />
      <div className="mx-auto space-y-5 bg-white p-7 md:p-11 md:max-w-xl1 md:shadow-md md:shadow-light-purple mt-12 md:mt-86">
        {showModal ? <ProcessingModal /> : null}
        {flag && <ValidationError message={msg} />}
        <p className="font-bold md:text-sm text-md">
          <a
            href="javascript:void(0);"
            className="aStyle"
            onClick={() => navigate("/checkList")}
            id="GoBackBtn1"
          >
            ←Sections
          </a>
          {` / ${formData.PageHeading}`}
        </p>
        <hr />
        <h1 className="font-bold md:text-xl text-lg">{formData.PageHeading}</h1>

        <div className="mx-auto space-y-5 rounded-sm border-2 md:shadow-md md:shadow-gray-500 bg-white p-3">
          <p className="flex flex-col flex-wrap justify-between gap-x-3 gap-y-1 md:flex-row">
            <span className="">{formData.ApplicationFeeText}</span>
            <span>{formData.ApplicationFeeAmount}</span>
          </p>
          <div>
            <h2 className="font-bold mt-2">{formData.CollegeListHeading}</h2>

            {formData?.FWCollegeChargeList?.map((card) => (
              <p
                className="ml-1 flex flex-col flex-wrap justify-between gap-x-5 gap-y-2 md:flex-row"
                key={card.CSSCode}
              >
                <span>{card.CSSCode}</span>
                <span>{card.InstitutionName}</span>
                <span>{card.AmountDue}</span>
              </p>
            ))}
          </div>
          <p className="mt-2 flex flex-col flex-wrap justify-between gap-x-3 gap-y-1 md:flex-row">
            <h2 className="font-bold">{formData.TotalChargeText}</h2>
            <span>{formData.TotalChargeAmount}</span>
          </p>
          {formData.FeeWaiverCreditText ? (
            <div className="space-y-3 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50  p-5">
              {parse(formData.FeeWaiverFPCPaidText)}
            </div>
          ) : null}
          {formData.FeeWaiverCreditText ? (
            <p className="flex flex-col flex-wrap justify-between gap-x-3 gap-y-1 md:flex-row">
              <h2 className="font-bold">{formData.FeeWaiverCreditText}</h2>
              <span>{formData.FeeWaiverCreditAmount}</span>
            </p>
          ) : null}
          {formData.FeePaymentCodeCreditText ? (
            <p className="flex flex-col flex-wrap justify-between gap-x-3 gap-y-1 md:flex-row">
              <h2 className="font-bold">{formData.FeePaymentCodeCreditText}</h2>
              <span>{formData.FeePaymentCodeCreditAmount}</span>
            </p>
          ) : null}
          <p className="flex flex-col flex-wrap justify-between gap-x-3 gap-y-1 md:flex-row">
            <h2 className="font-bold">{formData.BalanceDueText}</h2>
            <span>{formData.BalanceDueAmount}</span>
          </p>
        </div>
        {formData.DisplayCreditCardBtn === "Y" ? (
          <>
            <div className="space-y-3 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50  p-5">
              <p>
                Please be sure your application is complete, accurate and ready
                to be submitted before clicking the payment button and then be
                patient as your information is processed. Do not close your
                browser until you see your Dashboard to ensure that your
                application and payment information is fully processed.
              </p>
            </div>
            <div>
              <Button
                className="w-full md:w-fit"
                onClick={() => goToNextPage("CreditDebit")}
              >
                Pay by Credit or Debit Card
              </Button>
            </div>
          </>
        ) : null}
        {formData.DisplayFeePaymentCodeBtn === "Y" ? (
          <>
            <div className="space-y-3 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50  p-5">
              <p>
                If you received a Fee Payment Code from one or more of your
                colleges or scholarship programs, click here to enter the
                information.
              </p>
            </div>
            <div>
              <Button
                className="w-full md:w-fit"
                onClick={() => goToNextPage("CodePayment")}
              >
                Pay by Fee Payment Code
              </Button>
            </div>
          </>
        ) : null}
        {parseInt(formData.BalanceDueAmount) === 0 ? (
          <Button
            type="button"
            disabled={disableBtn}
            onClick={() => SubmitData()}
            className="w-full md:w-fit"
          >
            Submit
          </Button>
        ) : null}
        <hr />
        {pageIndex > 0 ? (
          <a
            href="javascript:void(0);"
            className="aStyle md:text-sm text-md grid justify-items-end"
            onClick={() => goBackToPage()}
          >
            ←Go Back
          </a>
        ) : null}
      </div>
    </Layout>
  );
};

export default WithLocation(FinalCharge);
