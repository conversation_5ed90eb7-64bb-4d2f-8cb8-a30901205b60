import React, { useContext, useEffect, useState } from "react";
import { navigate } from "gatsby";
import { formPageFromJSON } from "../components/formPages";
import useUserStore from "../utils/storeApp";
import Layout from "../components/layout";
import axios from "axios";
import Helmet from "react-helmet";
import Congrats from "../components/Congrats";
import { ProcessingModal } from "../components/processingModal";
import { ValidationError } from "../components/ValidationError";
import { studentInfoPages } from "../fakeDB/studentInfoPages";
import WithLocation from "../components/withLocation";

const CongratsPage = () => {
  const { isUserLogedIn, userForm, formURL, setpageFormUrl, setpageUrl } =
    useUserStore((state) => ({
      isUserLogedIn: state.isUserLogedIn,
      userForm: state.userForm,
      formURL: state.formURL,
      setpageFormUrl: state.setpageFormUrl,
      setpageUrl: state.setpageUrl,
    }));

  const [flag, SetFlag] = useState(false);
  const [urlPage, SetUrlPage] = useState(formURL);
  const [msg, SetMsg] = useState("Error trying to fetch data.");
  const [formData, SetFormData] = useState({});
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      if (!isUserLogedIn) {
        navigate("https://account.collegeboard.org");
      }
      setpageUrl("congrats");
      setShowModal(true);
      const formUser = { ...userForm, PageName: urlPage };
      const bodyFormData = new FormData();
      bodyFormData.append("sbr", JSON.stringify(formUser));
      //if (urlPage === "CongratsGettingStarted") {
      await studentInfoPages.find((p) => {
        const js = formPageFromJSON(p);
        SetFormData(js);
        SetFlag(false);
        setShowModal(false);
      });
      // } else {
      //   await axios({
      //     method: "post",
      //     url: process.env.API_URL + "Congrats",
      //     data: bodyFormData,
      //     headers: { "Content-Type": "multipart/form-data" },
      //   })
      //     .then(res => {
      //       setShowModal(false)
      //       if (res.status === 200) {
      //         const js = formPageFromJSON(res.data)
      //         SetFormData(js)
      //         SetFlag(false)
      //       } else if (res.data.returnMessage === "SERVERERROR") {
      //         SetFlag(true)
      //         SetMsg("Server error has occured.")
      //         return
      //       } else if (res.data.ReturnMessage === "FAILED") {
      //         SetFlag(true)
      //         SetMsg("No data found.")
      //         return
      //       }
      //     })
      //     .catch(err => {
      //       SetMsg("Server error has occured.")
      //       setShowModal(false)
      //       return
      //     })
      // }
    };
    fetchData();
  }, [urlPage]);
  const goToNextPage = (url) => {
    setpageFormUrl(url);
    //dispatch({ type: "UPDATE_PAGE",payload: url })
    navigate("/formpage");
  };
  return (
    <Layout>
      <Helmet
        htmlAttributes={{
          lang: "en",
        }}
        title={formData.title}
      />
      <div className="mx-auto space-y-5 bg-white p-7 md:max-w-lg md:p-11 md:shadow-md md:shadow-gray-500 mt-12 md:mt-6">
        {showModal ? <ProcessingModal /> : null}
        {flag && <ValidationError message={msg} />}
        {Object.entries(formData).length && (
          <Congrats
            heading={formData.title}
            text={formData.pageText}
            list={formData.dicts[0].DictItems}
            onClick={(page, value) => goToNextPage(page, value)}
          />
        )}
      </div>
    </Layout>
  );
};

export default WithLocation(CongratsPage);
