export const checkListPage = [
  {
    PageName: "ApplicationChecklist",
    PageHeading: "Application Checklist",
    PageText:
      "Complete each section below before submitting your application. You will have a chance to review your answers before submission.",
    PageFootHeading: "Ready to submit your application?",
    PageFootText: "Please complete all the section above before submitting your application.",
    PageFootSubmit:
      "Before submitting your application, please review your answers and confirm that the information is accurate and correct.",
    AppCompleted: "N",
    FWFormItemList: [
      {
        section: "1",
        completed: "Y",
        disabled: "N",
        textContent: "Getting Started",
        pageTo: "StudentInfoIntro",
      },
      {
        section: "2",
        completed: "N",
        disabled: "Y",
        textContent: "Parent Information",
        pageTo: "ParentInfo",
      },
      {
        section: "3",
        completed: "N",
        disabled: "N",
        textContent: "Residence",
        pageTo: "ParentInfo",
      },
      {
        section: "4",
        completed: "I",
        disabled: "N",
        textContent: "Academic Information",
        pageTo: "ParentInfo",
      },
      {
        section: "5",
        completed: "Y",
        disabled: "N",
        textContent: "Current Academic Year",
        pageTo: "ParentInfo",
      },
      {
        section: "6",
        completed: "N",
        disabled: "Y",
        textContent: "Currency",
        pageTo: "ParentInfo",
      },
      {
        section: "7",
        completed: "N",
        disabled: "N",
        textContent: "Correction",
        pageTo: "ParentInfo",
      },
      {
        section: "8",
        completed: "N",
        disabled: "N",
        textContent: "Parent Details",
        pageTo: "ParentInfo",
      },
      {
        section: "9",
        completed: "N",
        disabled: "N",
        textContent: "Parent Income",
        pageTo: "ParentInfo",
      },

      {
        section: "10",
        completed: "Y",
        disabled: "N",
        textContent: "Fee Waiver Eligibility",
        pageTo: "ParentInfo",
      },
      {
        section: "11",
        completed: "N",
        disabled: "Y",
        textContent: "Parent Military Income",
        pageTo: "ParentInfo",
      },
      {
        section: "12",
        completed: "N",
        disabled: "N",
        textContent: "Housing Information",
        pageTo: "ParentInfo",
      },
      {
        section: "13",
        completed: "I",
        disabled: "N",
        textContent: "Household Summary",
        pageTo: "ParentInfo",
      },
      {
        section: "14",
        completed: "Y",
        disabled: "N",
        textContent: "Child Support",
        pageTo: "ParentInfo",
      },
      {
        section: "15",
        completed: "N",
        disabled: "Y",
        textContent: "Parent Expenses",
        pageTo: "ParentInfo",
      },
      {
        section: "16",
        completed: "N",
        disabled: "N",
        textContent: "Parent Assets",
        pageTo: "ParentInfo",
      },
      {
        section: "17",
        completed: "N",
        disabled: "N",
        textContent: "Student Income",
        pageTo: "ParentInfo",
      },
      {
        section: "18",
        completed: "N",
        disabled: "N",
        textContent: "Student Expenses",
        pageTo: "ParentInfo",
      },
      {
        section: "19",
        completed: "N",
        disabled: "N",
        textContent: "Student Assets",
        pageTo: "ParentInfo",
      },
      {
        section: "20",
        completed: "N",
        disabled: "N",
        textContent: "Explanations",
        pageTo: "ParentInfo",
      },
      {
        section: "21",
        completed: "N",
        disabled: "N",
        textContent: "Supplement Questions",
        pageTo: "ParentInfo",
      },
    ],
  },
];
export const checkListPageCompleted = [
  {
    PageName: "ApplicationChecklistCompleted",
    PageHeading: "Application Checklist",
    PageText:
      "Complete each section below before submitting your application. You will have a chance to review your answers before submission.",
    PageFootHeading: "Ready to submit your application?",
    PageFootText: "Please complete all the section above before submitting your application.",
    PageFootSubmit:
      "Before submitting your application, please review your answers and confirm that the information is accurate and correct.",
    AppCompleted: "Y",
    FWFormItemList: [
      {
        section: "1",
        completed: "Y",
        disabled: "N",
        textContent: "Getting Started",
        pageTo: "StudentInfoIntro",
      },
      {
        section: "2",
        completed: "N",
        disabled: "Y",
        textContent: "Parent Information",
        pageTo: "ParentInfo",
      },
      {
        section: "3",
        completed: "Y",
        disabled: "N",
        textContent: "Residence",
        pageTo: "ParentInfo",
      },
      {
        section: "4",
        completed: "Y",
        disabled: "N",
        textContent: "Academic Information",
        pageTo: "ParentInfo",
      },
      {
        section: "5",
        completed: "Y",
        disabled: "N",
        textContent: "Current Academic Year",
        pageTo: "ParentInfo",
      },
      {
        section: "6",
        completed: "Y",
        disabled: "Y",
        textContent: "Currency",
        pageTo: "ParentInfo",
      },
      {
        section: "7",
        completed: "Y",
        disabled: "N",
        textContent: "Correction",
        pageTo: "ParentInfo",
      },
      {
        section: "8",
        completed: "Y",
        disabled: "N",
        textContent: "Parent Details",
        pageTo: "ParentInfo",
      },
      {
        section: "9",
        completed: "Y",
        disabled: "N",
        textContent: "Parent Income",
        pageTo: "ParentInfo",
      },
      {
        section: "10",
        completed: "Y",
        disabled: "N",
        textContent: "Fee Waiver Eligibility",
        pageTo: "ParentInfo",
      },
      {
        section: "11",
        completed: "Y",
        disabled: "Y",
        textContent: "Parent Military Income",
        pageTo: "ParentInfo",
      },
      {
        section: "12",
        completed: "Y",
        disabled: "N",
        textContent: "Housing Information",
        pageTo: "ParentInfo",
      },
      {
        section: "13",
        completed: "Y",
        disabled: "N",
        textContent: "Household Summary",
        pageTo: "ParentInfo",
      },
      {
        section: "14",
        completed: "Y",
        disabled: "N",
        textContent: "Child Support",
        pageTo: "ParentInfo",
      },
      {
        section: "15",
        completed: "Y",
        disabled: "Y",
        textContent: "Parent Expenses",
        pageTo: "ParentInfo",
      },
      {
        section: "16",
        completed: "Y",
        disabled: "N",
        textContent: "Parent Assets",
        pageTo: "ParentInfo",
      },
      {
        section: "17",
        completed: "Y",
        disabled: "N",
        textContent: "Student Income",
        pageTo: "ParentInfo",
      },
      {
        section: "18",
        completed: "Y",
        disabled: "N",
        textContent: "Student Expenses",
        pageTo: "ParentInfo",
      },
      {
        section: "19",
        completed: "Y",
        disabled: "N",
        textContent: "Student Assets",
        pageTo: "ParentInfo",
      },
      {
        section: "20",
        completed: "Y",
        disabled: "N",
        textContent: "Explanations",
        pageTo: "ParentInfo",
      },
      {
        section: "21",
        completed: "Y",
        disabled: "N",
        textContent: "Supplement Questions",
        pageTo: "ParentInfo",
      },
    ],
  },
];
