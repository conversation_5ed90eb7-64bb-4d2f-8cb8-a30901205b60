"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/404",{

/***/ "(pages-dir-browser)/./src/components/header.js":
/*!**********************************!*\
  !*** ./src/components/header.js ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(pages-dir-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_storeApp__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/storeApp */ \"(pages-dir-browser)/./src/utils/storeApp.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(pages-dir-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction Header(param) {\n    let { ...props } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [skip, SetSkip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)( true ? window.location.href : 0);\n    const [isFocused, setIsFocused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isOpen, SetIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { isUserLogedIn, userForm, setUserLogedIn, setUserForm, setpageFormUrl, setpageUrl, setBackPageButton, setPageIndex, setMoneyObj, hideLogin } = (0,_utils_storeApp__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n        \"Header.useUserStore\": (state)=>({\n                isUserLogedIn: state.isUserLogedIn,\n                userForm: state.userForm,\n                setUserLogedIn: state.setUserLogedIn,\n                setUserForm: state.setUserForm,\n                setpageFormUrl: state.setpageFormUrl,\n                setpageUrl: state.setpageUrl,\n                setBackPageButton: state.setBackPageButton,\n                setPageIndex: state.setPageIndex,\n                setMoneyObj: state.setMoneyObj,\n                hideLogin: state.hideLogin\n            })\n    }[\"Header.useUserStore\"]);\n    const handleClose = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Header.useCallback[handleClose]\": (e)=>{\n            let clickedInsideMenu = e.target.closest(\"#menu\") === undefined;\n            if (!clickedInsideMenu) {\n                SetIsOpen(false);\n            }\n        }\n    }[\"Header.useCallback[handleClose]\"], []);\n    const handleLogin = ()=>{\n        let AppId = userForm.ApplicationId;\n        setUserLogedIn(false);\n        setUserForm({});\n        setBackPageButton([]);\n        setMoneyObj([]);\n        setpageFormUrl(\"\");\n        setPageIndex(\"\");\n        setpageUrl(\"\");\n        window.location.href = \"https://account.collegeboard.org/login/logout?appId=7&DURL=https://student.collegeboard.org/css-financial-aid-profile\";\n    };\n    const activeStyles = {\n        color: \"#e9d5ff\",\n        textDecorationLine: \"underline\",\n        textUnderlineOffset: \"5px\"\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            document.addEventListener(\"click\", handleClose, true);\n            return ({\n                \"Header.useEffect\": ()=>{\n                    document.removeEventListener(\"click\", handleClose, true);\n                }\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], [\n        handleClose\n    ]);\n    // Static assets for Next.js\n    const data = {\n        logo: {\n            publicURL: \"/logo.svg\"\n        },\n        profile: {\n            publicURL: \"/profile.jpg\"\n        }\n    };\n    const goto = (url)=>{\n        router.push(url);\n    };\n    const handleSkip = ()=>{\n        let btn = document.getElementById(\"GoBackBtn1\");\n        if (btn) {\n            btn.focus();\n            btn.checked = true;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed z-50 flex mb-8 w-full flex-row flex-nowrap items-center justify-center bg-black text-sm font-medium text-white md:text-lg\",\n                ...props,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        className: \"text-sm\",\n                        href: \"#nolinkID\",\n                        onClick: handleSkip,\n                        onFocus: ()=>setIsFocused(true),\n                        onBlur: ()=>setIsFocused(false),\n                        children: isFocused ? \"Skip to main content\" : null\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        className: \"pl-2 hidden grow pr-4 md:visible md:flex hover:text-gray-300\",\n                        href: \"https://www.collegeboard.org\",\n                        target: \"_blank\",\n                        id: \"CollegeBoardId\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex flex-row flex-nowrap items-center justify-center space-x-2 pr-1 hover:w-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: process.env.IMG_URL + data.logo.publicURL,\n                                    alt: \"College Board\",\n                                    className: \"inline\",\n                                    width: 32,\n                                    height: 32\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"CollegeBoard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex w-full flex-row flex-nowrap items-center justify-start bg-program-core-higher-ed p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"CSS Profile\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: process.env.IMG_URL + data.profile.publicURL,\n                                    alt: \"CSS Profile\",\n                                    className: \"inline\",\n                                    width: 116,\n                                    height: 36\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"ml-auto md:hidden\",\n                                onClick: ()=>SetIsOpen(true),\n                                children: \"Menu\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-auto hidden grow flex-row flex-nowrap items-center justify-end space-x-9 md:visible md:flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://privacy.collegeboard.org\",\n                                        target: \"_blank\",\n                                        className: \"hover:text-purple-300\",\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://CSSAPIProfile.collegeboard.org/contact-us\",\n                                        target: \"_blank\",\n                                        className: \"hover:text-purple-300\",\n                                        children: \"Contact Us\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this),\n                                    hideLogin ? \"\" : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/dashboard\",\n                                                legacyBehavior: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    className: \"hover:text-purple-300 cursor-pointer\",\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this),\n                                            isUserLogedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#nolinkID\",\n                                                className: \"hover:text-purple-300 cursor-pointer\",\n                                                onClick: ()=>handleLogin(),\n                                                children: \"Log out\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/login\",\n                                                legacyBehavior: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    className: \"hover:text-purple-300 cursor-pointer\",\n                                                    children: \"Login\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(isOpen ? \"block\" : \"hidden\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-0 left-0 right-0 top-0 z-10 bg-black opacity-20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"menu\",\n                        className: \"duration-600 fixed top-0 right-0 z-30 z-20 h-full w-64 transform overflow-auto bg-white bg-white text-black transition-all ease-in-out md:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between p-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold\",\n                                        children: \"Menu\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-20 cursor-pointer font-bold text-sky-700 underline\",\n                                        onClick: ()=>SetIsOpen(false),\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"flex flex-col gap-y-3 p-5 font-bold\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://privacy.collegeboard.org\",\n                                            target: \"_blank\",\n                                            className: \"text-program-core-higher-ed underline underline-offset-2\",\n                                            children: \"Privacy Policy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>goto(\"/dashboard\"),\n                                            className: \"text-program-core-higher-ed underline underline-offset-2 cursor-pointer\",\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://CSSAPIProfile.collegeboard.org/contact-us\",\n                                            target: \"_blank\",\n                                            className: \"text-program-core-higher-ed underline underline-offset-2\",\n                                            children: \"Contact Us\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: isUserLogedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#nolinkID\",\n                                            className: \"cursor-pointer text-program-core-higher-ed underline underline-offset-2\",\n                                            onClick: ()=>handleLogin(),\n                                            children: \"Log out\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>goto(\"/login\"),\n                                            className: \"text-program-core-higher-ed underline underline-offset-2\",\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"mSgFV5k+lvpapzdgoyfZvU8Q6NE=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _utils_storeApp__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/header.js\n"));

/***/ })

});