import React, { useContext, useEffect, useState } from "react";
import { navigate } from "gatsby";
import { saveAppReview } from "../components/NewComp";
import Layout from "../components/layout";
import { Button } from "../components/Button";
import parse from "html-react-parser";
import Helmet from "react-helmet";
import { ValidationError } from "../components/ValidationError";
import { ProcessingModal } from "../components/processingModal";
import useUserStore from "../utils/storeApp";
import WithLocation from "../components/withLocation";

const AppRevIntro = ({ ttl, content }) => {
  const {
    setUserLogedIn,
    setpageFormUrl,
    userForm,
    isUserLogedIn,
    formURL,
    setUserForm,
    setpageUrl,
    backPageButton,
    setBackPageButton,
    pageIndex,
    setPageIndex,
    backButtonMode,
    setBackButtonMode,
  } = useUserStore((state) => ({
    setUserLogedIn: state.setUserLogedIn,
    setpageFormUrl: state.setpageFormUrl,
    setUserForm: state.setUserForm,
    userForm: state.userForm,
    formURL: state.formURL,
    isUserLogedIn: state.isUserLogedIn,
    setpageUrl: state.setpageUrl,
    backPageButton: state.backPageButton,
    setBackPageButton: state.setBackPageButton,
    pageIndex: state.pageIndex,
    setPageIndex: state.setPageIndex,
    backButtonMode: state.backButtonMode,
    setBackButtonMode: state.setBackButtonMode,
  }));

  const [flag, SetFlag] = useState(false);
  const [urlPage, SetUrlPage] = useState(formURL);
  const [msg, SetMsg] = useState("");
  const [formData, SetFormData] = useState({});
  const [showModal, setShowModal] = React.useState(false);
  const [disableBtn, SetdisableBtn] = useState(false);

  const onFormSubmition = async (event) => {
    event.preventDefault();
    SetdisableBtn(true);
    setBackButtonMode(false);
    const formReview = {
      Username: userForm.Username,
      UserId: userForm.UserId,
      AwardYear: userForm.AwardYear,
      ApplicationId: userForm.ApplicationId,
      OrgFormId: userForm.OrgFormId,
      FormId: userForm.FormId,
      PageName: "ApplicationReview",
      SectionName: userForm.SectionName,
      Token: userForm.Token,
    };
    const res = await saveAppReview(formReview);
    if (res.status === "SUCCESS") {
      SetdisableBtn(false);
      SetFlag(false);
      setUserForm({ ...res.data, Token: userForm.Token });
      setpageFormUrl(res.data.PageName);
      navigate("/appReview");
    } else if (res.status === "FAILED") {
      SetdisableBtn(false);
      SetFlag(true);
      SetMsg(res.data.ErrorMessage);
    } else if (res.status === "SERVERERROR") {
      SetdisableBtn(false);
      SetFlag(true);
      SetMsg(res.data);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!isUserLogedIn) {
        navigate("https://account.collegeboard.org");
      }
      setpageUrl("appReviewIntro");
      setShowModal(true);
      const formReview = {
        Username: userForm.Username,
        UserId: userForm.UserId,
        AwardYear: userForm.AwardYear,
        ApplicationId: userForm.ApplicationId,
        OrgFormId: userForm.OrgFormId,
        FormId: userForm.FormId,
        PageName: "ApplicationReviewIntro",
        SectionName: "",
        Token: userForm.Token,
      };
      setBackPageButton([...backPageButton, formReview]);
      if (!backButtonMode) {
        setPageIndex(backPageButton.length - 1);
      }
      setBackButtonMode(false);
      const res = await saveAppReview(formReview);
      if (res.status === "SUCCESS") {
        SetFlag(false);
        setUserForm({ ...res.data, Token: userForm.Token });
        setpageFormUrl(res.data.PageName);
        SetFormData(res.data);
        document.getElementById("CollegeBoardId").focus();
      } else if (res.status === "FAILED") {
        if (res.data.ErrorMessage) {
          SetFlag(true);
          SetMsg(res.data.ErrorMessage);
        }
      } else if (res.status === "SERVERERROR") {
        SetFlag(true);
        SetMsg(res.data);
      }
      setShowModal(false);
    };
    fetchData();
  }, [urlPage]);
  const goBackToPage = async () => {
    setBackButtonMode(true);
    const goBack = backPageButton[pageIndex];
    setPageIndex(pageIndex - 1);
    setUserForm(goBack);
    setpageFormUrl(goBack.PageName);
    if (goBack.PageName === "DataChecks") {
      navigate("/dataChecks");
    } else if (goBack.PageName === "AppCheckList") {
      navigate("/checkList");
    } else if (goBack.Multiple === "Y") {
      navigate("/multiple");
    } else {
      navigate("/formpage");
    }
    //navigate("/dataChecks")
  };
  const CallCert = () => {
    const frm = { ...userForm, PageName: "ApplicationCertification" };
    setUserForm(frm);
    setpageFormUrl("payment");
    navigate("/payment");
  };
  return (
    <Layout>
      <Helmet
        htmlAttributes={{
          lang: "en",
        }}
        title={formData.PageHeading}
      />
      <div className="mx-auto space-y-5 bg-white p-7 md:p-11 md:max-w-lg md:shadow-md md:shadow-light-purple mt-12 md:mt-6">
        {showModal ? <ProcessingModal /> : null}
        {flag && <ValidationError message={msg} />}
        <form
          onSubmit={(event) => onFormSubmition(event)}
          className="space-y-5"
        >
          <hr />
          {formData.PageHeading ? (
            <h1
              className="md:text-xl text-lg font-bold"
              id="GoBackBtn1"
              tabindex="-1"
            >
              {formData.PageHeading}
            </h1>
          ) : null}
          {formData?.PageText && (
            <div className=" md:text-xl text-lg">
              {/* <p>
                Next, you’ll have the opportunity to review all the information
                you provided and fix any mistakes. Click “Begin Review” below to
                start.
              </p>
              <p>
                If you have already reviewed your answers and are ready to
                submit your CSS Profile, click “Skip Review”.
              </p> */}
              {parse(formData.PageText)}
            </div>
          )}

          {formData && (
            <div className="space-y-2">
              <div>
                <Button disabled={disableBtn} className="w-full md:w-fit">
                  Begin Review
                </Button>
              </div>
              <div>
                <Button
                  disabled={disableBtn}
                  type="button"
                  className="w-full md:w-fit"
                  onClick={() => {
                    CallCert();
                  }}
                >
                  Skip Review
                </Button>
              </div>
              <hr />
              {pageIndex > 0 ? (
                <a
                  href="javascript:void(0);"
                  className="aStyle md:text-sm text-md grid justify-items-end"
                  onClick={() => goBackToPage()}
                >
                  ←Go Back
                </a>
              ) : null}
            </div>
          )}
        </form>
      </div>
    </Layout>
  );
};
export default WithLocation(AppRevIntro);
