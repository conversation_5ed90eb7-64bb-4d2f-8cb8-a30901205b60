{"c": ["webpack"], "r": ["pages/404"], "m": ["./node_modules/next/dist/build/polyfills/object-assign.js", "./node_modules/next/dist/build/polyfills/process.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CCSharp%5CGitHub%5CProfileCSS%5CClientApp%5Cpages%5C404.js&page=%2F404!", "./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js", "./node_modules/next/dist/client/get-domain-locale.js", "./node_modules/next/dist/client/link.js", "./node_modules/next/dist/client/use-intersection.js", "./node_modules/next/dist/compiled/process/browser.js", "./node_modules/next/head.js", "./node_modules/next/link.js", "./node_modules/prop-types/checkPropTypes.js", "./node_modules/prop-types/factoryWithTypeCheckers.js", "./node_modules/prop-types/index.js", "./node_modules/prop-types/lib/ReactPropTypesSecret.js", "./node_modules/prop-types/lib/has.js", "./node_modules/prop-types/node_modules/react-is/cjs/react-is.development.js", "./node_modules/prop-types/node_modules/react-is/index.js", "./node_modules/react-idle-timer/dist/index.esm.js", "./node_modules/zustand/esm/index.mjs", "./node_modules/zustand/esm/middleware.mjs", "./node_modules/zustand/esm/react.mjs", "./node_modules/zustand/esm/vanilla.mjs", "./pages/404.js", "./src/components/Button/index.jsx", "./src/components/header.js", "./src/components/layout.js", "./src/components/shared/buttons.js", "./src/components/shared/inputs.js", "./src/utils/storeApp.js"]}