import React, { useState, useContext } from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import RadioButton from "../components/inputs/RadioButton";
import { Label } from "../components/inputs/Label";
import { TextInput } from "../components/inputs/TextInput";
import { Button } from "../components/Button";
import Layout from "../components/layout";
import Head from "next/head";
import { ValidationError } from "../components/ValidationError";
import { ProcessingModal } from "../components/processingModal";
import axios from "axios";
import useUserStore from "../utils/storeApp";
import WithLocation from "../components/withLocation";
import parse from "html-react-parser";

const NextLink = ({ children, href, ...props }) => {
  return (
    <Link href={href}>
      <a {...props}>{children}</a>
    </Link>
  );
};
const Section = ({ title, children }) => {
  return (
    <section className="flex w-full flex-col gap-2">
      <h2 id={title} className="group text-2xl font-bold underline">
        <span className="float-left -ml-5 hidden text-gray-500 group-hover:inline">
          #
        </span>
        {title}
      </h2>
      {children}
    </section>
  );
};
const Component = ({ name, children, isRequired }) => {
  return (
    <>
      <Label required={isRequired}>{name}</Label>
      {children}
    </>
  );
};

const CreateAccount = () => {
  const [isChecked, setIsChecked] = useState(false);
  const [answers, setAnswers] = useState();
  const [flag, SetFlag] = useState(false);
  const [flagEmail, SetFlagEmail] = useState(false);
  const [msg, SetMsg] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [disableBtn, SetdisableBtn] = useState(false);
  const {
    setUserLogedIn,
    setpageFormUrl,
    userForm,
    setUserForm,
    isUserLogedIn,
    appPool,
  } = useUserStore((state) => ({
    setUserLogedIn: state.setUserLogedIn,
    setpageFormUrl: state.setpageFormUrl,
    setUserForm: state.setUserForm,
    userForm: state.userForm,
    isUserLogedIn: state.isUserLogedIn,
    appPool: state.appPool,
  }));

  const setAnswer = (questionId, answer) => {
    setAnswers({ ...answers, [questionId]: answer });
    if (questionId === "ConfirmPassword" || questionId === "Password") {
      if (
        answers?.Password &&
        questionId === "ConfirmPassword" &&
        answers?.Password != answer
      ) {
        SetFlag(true);
      } else if (
        answers?.ConfirmPassword &&
        questionId === "Password" &&
        answers?.ConfirmPassword != answer
      ) {
        SetFlag(true);
      } else {
        SetFlag(false);
      }
    }
    if (questionId === "ConfirmUsername" || questionId === "Username") {
      if (
        answers?.Username &&
        questionId === "ConfirmUsername" &&
        answers?.Username != answer
      ) {
        SetFlagEmail(true);
      } else if (
        answers?.ConfirmUsername &&
        questionId === "Username" &&
        answers?.ConfirmUsername != answer
      ) {
        SetFlagEmail(true);
      } else {
        SetFlagEmail(false);
      }
    }
  };
  const onFormSubmition = async (event) => {
    event.preventDefault();
    SetdisableBtn(true);
    if (answers.Password != answers.ConfirmPassword) {
      SetFlag(true);
      return;
    } else if (answers.Password === answers.ConfirmPassword) {
      SetFlag(false);
    }
    if (answers.Username != answers.ConfirmUsername) {
      SetFlagEmail(true);
      return;
    } else if (answers.Username === answers.ConfirmUsername) {
      SetFlagEmail(false);
    }
    // if (answers.SecretPhrase !== process.env.SECRET_PHRASE) {
    //   SetMsg("You are not allowed to use this application.")
    //   SetFlag(true)
    //   return
    // }
    setShowModal(true);
    const bodyFormData = new FormData();
    bodyFormData.append("sbr", JSON.stringify(answers));
    const user = await axios({
      method: "post",
      url: process.env.API_URL + "Login/CreateAccount",
      data: bodyFormData,
      headers: { "Content-Type": "multipart/form-data" },
      withCredentials: true,
    })
      .then((res) => {
        setShowModal(false);
        if (res.data.ReturnMessage.toUpperCase() === "SUCCESS") {
          SetdisableBtn(false);
          SetMsg(null);
          setUserLogedIn(true);
          setUserForm(res.data);
          setpageFormUrl(res.data.PageName);
          setAnswers({});
          navigate("/terms");
        } else if (
          res.data.ReturnMessage.toUpperCase() === "FAILEDVALIDATION"
        ) {
          SetdisableBtn(false);
          SetMsg(res.data.QuestionErrorList);
          setShowModal(false);
          return;
        } else if (res.data.ReturnMessage.toUpperCase() === "ACCOUNTEXISTS") {
          SetdisableBtn(false);
          SetMsg("Account Exists. Please Login.");
          setShowModal(false);
          return;
        } else {
          SetdisableBtn(false);
          SetMsg("Server error has occured.");
          navigate("https://account.collegeboard.org");
        }
      })
      .catch((err) => {
        SetdisableBtn(false);
        SetMsg("Server error has occured.");
        return;
      });
  };
  const termsFormSubmit = async (event) => {
    event.preventDefault();
    setShowModal(true);
    const formUser = { ...userForm, AcceptTermsCondition: "Y" };
    const bodyFormData = new FormData();
    bodyFormData.append("sbr", JSON.stringify(formUser));
    const user = await axios({
      method: "post",
      url: process.env.API_URL + "PostAcceptTerms",
      data: bodyFormData,
      headers: { "Content-Type": "multipart/form-data" },
    })
      .then((res) => {
        setShowModal(false);
        if (res.data.ReturnMessage.toUpperCase() === "SUCCESS") {
          setUserForm(res.data);
          setpageFormUrl(res.data.PageName);
          // dispatch({ type: "UPDATE_FORM", payload: res.data })
          // dispatch({ type: "UPDATE_PAGE", payload: res.data.PageName })
          navigate(`/dashboard`);
        } else if (
          res.data.ReturnMessage.toUpperCase() === "FAILEDVALIDATION"
        ) {
          SetMsg(res.data.QuestionErrorList);
          setShowModal(false);
          return;
        } else {
          SetMsg("Server error has occured.");
          return;
        }
      })
      .catch((err) => {
        SetMsg("Server error has occured.");
        return;
      });
  };
  return (
    <Layout>
      <Helmet
        htmlAttributes={{
          lang: "en",
        }}
        title={"Create Account"}
      />
      <main className="w-xl space-y-5 bg-white p-7 md:p-11 md:max-w-lg md:shadow-md md:shadow-light-purple mt-12 md:mt-6">
        {showModal ? <ProcessingModal /> : null}
        <div>
          <p className="md:text-xl text-lg mb-2 font-bold">
            Create New Account
          </p>
          <Component name="">
            {/* yellow-600 */}
            <div className="space-y-3 rounded-md border-2 border-solid border-yellow-600  bg-yellow-50 p-5 text-base">
              <p>
                <strong>{"Note: "}</strong>Your username and password will allow
                you to log back into the system if you cannot finish the
                application process in one sitting.
              </p>
              <p>
                Passwords must be 10-40 characters long and must contain both
                letters and numbers. Spaces and characters such as ampersands
                are not permitted. If you forget your password, you can use the
                Forgot your password? link to reset your password.
              </p>
              <p>Your email address will serve as your username.</p>
            </div>
          </Component>

          <form onSubmit={(event) => onFormSubmition(event)}>
            {" "}
            <Section>
              <Component name="Profile Year" isRequired={true}>
                {appPool === "CSSAPIProfile AppPool" ||
                appPool === "CSSAPIProfileBeta App Pool" ? (
                  <RadioButton
                    onChange={(checked) => {
                      setIsChecked(checked.value);
                      setAnswer("AwardYear", checked.value);
                    }}
                    id="GoBackBtn"
                    name="profile_year"
                    value="2027"
                    label="2026-2027"
                    checked={"2027" === isChecked}
                    required
                  />
                ) : null}
                <RadioButton
                  onChange={(checked) => {
                    setIsChecked(checked.value);
                    setAnswer("AwardYear", checked.value);
                  }}
                  id="GoBackBtn1"
                  name="profile_year"
                  value="2026"
                  label="2025-2026"
                  checked={"2026" === isChecked}
                  required
                />
              </Component>
              <Component name="Email" isRequired={true}>
                <TextInput
                  onChange={(event) => setAnswer("Username", event)}
                  type="email"
                  required
                  id="Username"
                />
              </Component>
              <Component name="Confirm Email" isRequired={true}>
                <TextInput
                  onChange={(event) => setAnswer("ConfirmUsername", event)}
                  type="email"
                  required
                  id="ConfirmUsername"
                />
              </Component>
              {flagEmail && (
                <Component name="">
                  {flagEmail && (
                    <ValidationError message={"Emails don't match."} />
                  )}
                </Component>
              )}
              <Component name="Password" isRequired={true}>
                <TextInput
                  type="password"
                  id="Password"
                  onChange={(event) => setAnswer("Password", event)}
                  required
                  minLength={10}
                  maxLength={40}
                />
              </Component>
              <Component name="Confirm Password" isRequired={true}>
                <TextInput
                  type="password"
                  id="ConfirmPassword"
                  onChange={(event) => setAnswer("ConfirmPassword", event)}
                  required
                  minLength={10}
                  maxLength={40}
                />
              </Component>
              {flag && (
                <Component name="">
                  {flag && (
                    <ValidationError message={"Passwords don't match."} />
                  )}
                </Component>
              )}
              <Component name="First Name" isRequired={true}>
                <TextInput
                  onChange={(event) => setAnswer("FirstName", event)}
                  required
                />
              </Component>
              <Component name="Last Name" isRequired={true}>
                <TextInput
                  onChange={(event) => setAnswer("LastName", event)}
                  required
                />
              </Component>
              <Component name="Phone" isRequired={true}>
                <TextInput
                  onChange={(event) => setAnswer("Phone", event)}
                  type="number"
                  required
                />
              </Component>
              <Component
                name="Secret Phrase"
                for="SecretPhrase"
                isRequired={true}
              >
                <TextInput
                  type="text"
                  onChange={(event) => setAnswer("Key", event)}
                  required
                  id="Key"
                />
              </Component>
              <Button disabled={disableBtn} className="w-full md:w-fit">
                Save and Login
              </Button>
            </Section>
          </form>
        </div>
        {msg ? (
          Array.isArray(msg) ? (
            msg.map((err, idx) => (
              <ValidationError key={idx} message={parse(err.ErrorMsg)} />
            ))
          ) : (
            <ValidationError message={parse(msg)} />
          )
        ) : null}
      </main>
    </Layout>
  );
};
export default WithLocation(CreateAccount);
