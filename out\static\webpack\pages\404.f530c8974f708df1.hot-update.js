"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/404",{

/***/ "(pages-dir-browser)/./src/components/layout.js":
/*!**********************************!*\
  !*** ./src/components/layout.js ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! prop-types */ \"(pages-dir-browser)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./header */ \"(pages-dir-browser)/./src/components/header.js\");\n/* harmony import */ var react_idle_timer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-idle-timer */ \"(pages-dir-browser)/./node_modules/react-idle-timer/dist/index.esm.js\");\n/* harmony import */ var _utils_storeApp__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/storeApp */ \"(pages-dir-browser)/./src/utils/storeApp.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Layout(param) {\n    let { children } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const timeout = 60000 * 30;\n    const [remaining, setRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(timeout);\n    const [elapsed, setElapsed] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [lastActive, setLastActive] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(+new Date());\n    const [isIdle, setIsIdle] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const handleOnActive = ()=>setIsIdle(false);\n    const { isUserLogedIn, userForm, setUserLogedIn, setUserForm, setpageFormUrl, setpageUrl, setBackPageButton, setPageIndex, appPool } = (0,_utils_storeApp__WEBPACK_IMPORTED_MODULE_5__[\"default\"])({\n        \"Layout.useUserStore\": (state)=>({\n                isUserLogedIn: state.isUserLogedIn,\n                userForm: state.userForm,\n                setUserLogedIn: state.setUserLogedIn,\n                setUserForm: state.setUserForm,\n                setpageFormUrl: state.setpageFormUrl,\n                setpageUrl: state.setpageUrl,\n                setBackPageButton: state.setBackPageButton,\n                setPageIndex: state.setPageIndex,\n                appPool: state.appPool\n            })\n    }[\"Layout.useUserStore\"]);\n    const handleOnIdle = ()=>{\n        let AppId = userForm.ApplicationId;\n        setUserLogedIn(false);\n        setUserForm({});\n        setpageFormUrl(\"\");\n        setpageUrl(\"\");\n        setBackPageButton([]);\n        setPageIndex(\"\");\n        window.location.href = \"https://account.collegeboard.org/login/logout?appId=\" + AppId + \"&DURL=https://student.collegeboard.org/css-financial-aid-profile\";\n    };\n    const { getRemainingTime, getLastActiveTime, getElapsedTime } = (0,react_idle_timer__WEBPACK_IMPORTED_MODULE_4__.useIdleTimer)({\n        timeout,\n        onActive: handleOnActive,\n        onIdle: handleOnIdle\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Layout.useEffect\": ()=>{\n            setRemaining(getRemainingTime());\n            setLastActive(getLastActiveTime());\n            setElapsed(getElapsedTime());\n            const intervalId = setInterval({\n                \"Layout.useEffect.intervalId\": ()=>{\n                    setRemaining(getRemainingTime());\n                    setLastActive(getLastActiveTime());\n                    setElapsed(getElapsedTime());\n                }\n            }[\"Layout.useEffect.intervalId\"], 1000);\n            // Cleanup function to clear the interval\n            return ({\n                \"Layout.useEffect\": ()=>{\n                    clearInterval(intervalId);\n                }\n            })[\"Layout.useEffect\"];\n        //adobeAnalyticsPush();\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"Layout.useEffect\"], []);\n    const adobeAnalyticsPush = ()=>{\n        window.adobeDataLayer = window.adobeDataLayer || [];\n        window.adobeDataLayer.push({\n            page: {\n                flowCode: userForm.MenuName,\n                pageCode: userForm.PageName,\n                appViewCode: \"\"\n            }\n        });\n        try {\n            window._satellite.track(\"cbTrack.viewInDom\");\n        } catch (errSatelliteTrack) {}\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen font-sans text-gray-900 md:text-lg text-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\layout.js\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                id: \"scrollToTop\",\n                className: \"flex-1 w-full max-w-4xl px-4 py-8 mx-auto md:px-8 md:py-16\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\layout.js\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\layout.js\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\n_s(Layout, \"OWd8OGTf4PX6DktOuL5/+psvDsc=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _utils_storeApp__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        react_idle_timer__WEBPACK_IMPORTED_MODULE_4__.useIdleTimer\n    ];\n});\n_c = Layout;\nLayout.propTypes = {\n    children: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().node).isRequired\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\nvar _c;\n$RefreshReg$(_c, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/layout.js\n"));

/***/ })

});