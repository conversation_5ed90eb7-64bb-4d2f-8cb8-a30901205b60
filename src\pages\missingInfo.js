import React, { useContext, useEffect, useState } from "react";
import { navigate } from "gatsby";
import useUserStore from "../utils/storeApp";
import Layout from "../components/layout";
import axios from "axios";
import { Heading } from "../components/Heading";
import Helmet from "react-helmet";
import parse from "html-react-parser";
import { ValidationError } from "../components/ValidationError";
import { ProcessingModal } from "../components/processingModal";
import WithLocation from "../components/withLocation";

const MissingInfo = () => {
  const {
    isUserLogedIn,
    userForm,
    formURL,
    setUserForm,
    setpageFormUrl,
    currentPageURL,
    setpageUrl,
    backPageButton,
    setBackPageButton,
    pageIndex,
    setPageIndex,
    backButtonMode,
    setBackButtonMode,
  } = useUserStore((state) => ({
    isUserLogedIn: state.isUserLogedIn,
    userForm: state.userForm,
    formURL: state.setUserformURLLogedIn,
    setUserForm: state.setUserForm,
    setpageFormUrl: state.setpageFormUrl,
    currentPageURL: state.currentPageURL,
    setpageUrl: state.setpageUrl,
    backPageButton: state.backPageButton,
    setBackPageButton: state.setBackPageButton,
    pageIndex: state.pageIndex,
    setPageIndex: state.setPageIndex,
    backButtonMode: state.backButtonMode,
    setBackButtonMode: state.setBackButtonMode,
  }));

  const [flag, SetFlag] = useState(false);
  const [msg, SetMsg] = useState("Error trying to fetch data.");
  const [formData, SetFormData] = useState({});
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      if (!isUserLogedIn) {
        navigate("https://account.collegeboard.org");
      }
      setpageUrl("missingInfo");
      setShowModal(true);
      SetFormData(userForm);
      setShowModal(false);
    };
    fetchData();
  }, []);
  const goToNextPage = (pageName, menuName, multiple) => {
    const formUser = {
      ...userForm,
      MenuName: menuName,
      PageName: pageName,
      Multiple: multiple,
    };
    setBackButtonMode(false);
    setUserForm(formUser);
    setpageFormUrl(pageName);
    // if (pageName === "AcademicInfo3" || pageName === "AcademicInfo3a") {
    //   navigate("/school");
    // } else
    if (pageName === "AcademicInfo4") {
      navigate("/program");
    } else if (pageName === "Correction") {
      navigate("/payment");
    } else if (pageName === "ApplicationChecklist") {
      navigate("/checkList");
    } else if (multiple === "Y") {
      navigate("/multiple");
    } else {
      navigate("/formpage");
    }
  };
  const goBackToPage = () => {
    setBackButtonMode(true);
    const goBack = backPageButton[backPageButton.length - 1];
    setPageIndex(pageIndex - 1);
    setUserForm(goBack);
    setpageFormUrl(goBack.PageName);
    // if (
    //   goBack.PageName === "AcademicInfo3" ||
    //   goBack.PageName === "AcademicInfo3a"
    // ) {
    //   navigate("/school");
    // } else
    if (goBack.PageName === "AcademicInfo4") {
      navigate("/program");
    } else if (goBack.PageName === "Correction") {
      navigate("/payment");
    } else if (goBack.PageName === "AppCheckList") {
      navigate("/checkList");
    } else if (goBack.Multiple === "Y") {
      navigate("/multiple");
    } else {
      navigate("/formpage");
    }
  };
  return (
    <Layout>
      <Helmet
        htmlAttributes={{
          lang: "en",
        }}
        title={formData.PageHeading ? formData.PageHeading : "Missing Info"}
      />
      <div className="mx-auto space-y-5 bg-white p-7 md:max-w-lg md:p-11 md:shadow-md md:shadow-gray-500 mt-12 md:mt-6">
        {showModal ? <ProcessingModal /> : null}
        {flag && <ValidationError message={msg} />}
        {formData.PageHeading ? (
          <h1
            className="md:text-xl text-lg font-bold"
            id="GoBackBtn1"
            tabindex="-1"
          >
            {formData.PageHeading}
          </h1>
        ) : null}
        {formData.PageText ? <p>{parse(formData.PageText)}</p> : null}
        {formData?.FWMissingInfoMenuList?.map((elem, currentPosition) => (
          <div
            key={elem.PageName}
            className={`max-w-xl space-y-5 rounded-md border-2 p-3`}
          >
            <div
              className={`flex flex-col flex-wrap justify-between gap-x-3 gap-y-1 md:flex-row`}
            >
              <a
                // id={`GoBackBtn${currentPosition + 1}`}
                href="#nolinkID"
                className="text-center aStyle"
                onClick={(value) => {
                  goToNextPage(elem.PageName, elem.MenuName, elem.Multiple);
                }}
              >
                {elem.PageNameHeading}
              </a>
            </div>
          </div>
        ))}
        <hr />
        {pageIndex > 0 ? (
          <p className="font-bold md:text-sm text-md grid justify-items-end">
            <a
              href="#nolinkID"
              className="aStyle"
              onClick={() => goBackToPage("ApplicationChecklist")}
            >
              ←Go Back
            </a>
          </p>
        ) : null}
      </div>
    </Layout>
  );
};

export default WithLocation(MissingInfo);
