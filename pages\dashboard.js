import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { GetPdfUrl } from "../src/components/NewComp";
import { Label } from "../src/components/inputs/Label";
import { TextInput } from "../src/components/inputs/TextInput";
import Layout from "../src/components/layout";
import axios from "axios";
import Head from "next/head";
import { Button } from "../src/components/Button";
import { ValidationError } from "../src/components/ValidationError";
import { ProcessingModal } from "../src/components/processingModal";
import { ConfirmModal } from "../src/components/ConfirmModal";
import CollegeTable from "../src/components/CollegeTable";
import DocTable from "../src/components/DocTable";
import useUserStore from "../src/utils/storeApp";
import WithLocation from "../src/components/withLocation";
import parse from "html-react-parser";

const Dashboard = () => {
  const router = useRouter();
  const {
    setUserLogedIn,
    setpageFormUrl,
    userForm,
    isUserLogedIn,
    formURL,
    setUserForm,
    updateUserForm,
    setpageUrl,
    appPool,
    setBackPageButton,
    setPageIndex,
    setDollarFormat,
    dollarFormat,
    moneyObj,
    setMoneyObj,
    SSNValue,
    setSSNValue,
    setAppReviewNr,
    AppReviewNr,
    setAaddCollegeFlag,
    addCollegeFlag,
    backButtonMode,
    setBackButtonMode,
  } = useUserStore((state) => ({
    setUserLogedIn: state.setUserLogedIn,
    setpageFormUrl: state.setpageFormUrl,
    userForm: state.userForm,
    isUserLogedIn: state.isUserLogedIn,
    formURL: state.formURL,
    setUserForm: state.setUserForm,
    updateUserForm: state.updateUserForm,
    setpageUrl: state.setpageUrl,
    appPool: state.appPool,
    setBackPageButton: state.setBackPageButton,
    setPageIndex: state.setPageIndex,
    setDollarFormat: state.setDollarFormat,
    dollarFormat: state.dollarFormat,
    moneyObj: state.moneyObj,
    setMoneyObj: state.setMoneyObj,
    SSNValue: state.SSNValue,
    setSSNValue: state.setSSNValue,
    setAppReviewNr: state.setAppReviewNr,
    AppReviewNr: state.AppReviewNr,
    setAaddCollegeFlag: state.setAaddCollegeFlag,
    addCollegeFlag: state.addCollegeFlag,
    backButtonMode: state.backButtonMode,
    setBackButtonMode: state.setBackButtonMode,
  }));

  const [flag, SetFlag] = useState(false);
  const [urlPage, SetUrlPage] = useState("");
  const [msg, SetMsg] = useState("");
  const [formData, SetFormData] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [disableBtn, SetdisableBtn] = useState(false);
  const [objFlag, SetObjFlag] = useState(Date.now());
  const [actionUrl, SetActionUrl] = useState("");
  const [answers, setAnswers] = useState();
  const [openConfirm, setOpenConfirm] = useState(false);
  const [optOutConfirm, setOptOutConfirm] = useState(false);
  const [showColoradoMsg, setShowColoradoMsg] = useState(false);
  const [confirmFlag, setConfirmFlag] = useState(false);
  const setAnswer = async (questionId, answer) => {
    setAnswers({ ...answers, [questionId]: answer });
  };
  useEffect(() => {
    const fetchData = async () => {
      if (!isUserLogedIn) {
        window.location.href = "https://account.collegeboard.org";
      }
      setpageUrl("dashboard");
      setShowModal(true);
      const formUser = {
        Username: userForm.Username,
        UserId: userForm.UserId,
        AwardYear: userForm.AwardYear,
        ApplicationId: userForm.ApplicationId,
        OrgFormId: userForm.OrgFormId,
        FormId: userForm.FormId,
        PageName: "Dashboard",
        Token: userForm.Token,
      };
      setUserForm(formUser);
      setBackPageButton([...backPageButton, formUser]);
      if (!backButtonMode) {
        setPageIndex(backPageButton.length - 1);
      }
      setDollarFormat(dollarFormat);
      await axios({
        method: "post",
        url: process.env.API_URL + "navigation/GetDashboard",
        data: formUser,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userForm.Token}`,
        },
      })
        .then((res) => {
          setShowModal(false);
          if (
            res.status === 200 ||
            res.data.ReturnMessage.toUpperCase() === "SUCCESS"
          ) {
            SetFlag(false);
            const js = res.data;
            setpageFormUrl(js.PageName);
            setUserForm(js);
            if (js.PageName === "AcademicInfo4") {
              router.push("/program");
            } else if (js.PageName === "Dashboard") {
              SetObjFlag(Date.now());
            }
          } else if (res.data.returnMessage.toUpperCase() === "SERVERERROR") {
            SetdisableBtn(false);
            SetFlag(true);
            SetMsg("Server error has occured.");
            setShowModal(false);
            return;
          } else if (res.data.ReturnMessage === "FAILED") {
            SetFlag(true);
            setShowModal(false);
            return;
          }
        })
        .catch((err) => {
          SetMsg("Server error has occured.");
          setShowModal(false);
          return;
        });
    };
    fetchData();
    document.getElementById("scrollToTop").scrollIntoView();
    document.getElementById("scrollToTop").focus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const goToNextPage = (url, page, multiple) => {
    const formUser = {
      ...userForm,
      PageName: page,
      Multiple: multiple,
    };
    setUserForm(formUser);
    setpageFormUrl(page);
    router.push("/" + url);
  };
  const Component = ({ name, id, children, isRequired }) => {
    return (
      <>
        <Label required={isRequired} for={id}>
          {name}
        </Label>
        {children}
      </>
    );
  };
  return (
    <Layout>
      <Head>
        <title>{formData.PageName || "Dashboard"}</title>
        <meta name="description" content="CSS Profile Dashboard" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      <div
        className="mx-auto space-y-5 bg-white p-7 text-md md:max-w-3xl md:p-11  mt-12 md:mt-6"
        id="dashboard"
      >
        <div id="scrollToTop" tabIndex="-1"></div>
        {flag && (
          <Component name="">
            {flag && <ValidationError message={msg} />}
          </Component>
        )}
        {/* Rest of the dashboard content would go here */}
        {/* This is a simplified version - the full component would include all the dashboard UI */}
      </div>
      {showModal ? <ProcessingModal /> : null}
    </Layout>
  );
};

export default WithLocation(Dashboard);
