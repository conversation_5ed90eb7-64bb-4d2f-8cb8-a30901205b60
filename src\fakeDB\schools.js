export const Schools = [
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 4034,
    OrgName: "California Institute of Tech(No Rollover)",
    MACity: "Caltechia",
    MAState: "",
    MAZip: 12345,
    ParentsDatafromIndependentStudents: "N",
    ParentsDataAgeCutoff: 35,
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Y",
    CSSAPIProfilePartRetDomUndergrad: "N",
    CSSAPIProfilePartNewIntUndergrad: "N",
    CSSAPIProfilePartRetIntUndergrad: "N",
    GraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomGrad: "N",
    CSSAPIProfilePartRetDomGrad: "Y",
    CSSAPIProfilePartNewIntGrad: "N",
    CSSAPIProfilePartRetIntGrad: "N",
    CSSAPIProfilePartTranDomUndergrad: "N",
    CSSAPIProfilePartTranIntUndergrad: "N",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 3901,
    OrgName: "Tufts University(No Rollover)",
    MACity: "Tufts",
    MAState: "",
    MAZip: 12345,
    ParentsDatafromIndependentStudents: "N",
    ParentsDataAgeCutoff: 35,
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "N",
    CSSAPIProfilePartRetDomUndergrad: "N",
    CSSAPIProfilePartNewIntUndergrad: "Y",
    CSSAPIProfilePartRetIntUndergrad: "Y",
    GraduateEnrollment: "N",
    CSSAPIProfilePartNewDomGrad: "N",
    CSSAPIProfilePartRetDomGrad: "N",
    CSSAPIProfilePartNewIntGrad: "N",
    CSSAPIProfilePartRetIntGrad: "N",
    CSSAPIProfilePartTranDomUndergrad: "N",
    CSSAPIProfilePartTranIntUndergrad: "Y",
  },
  {
    OrgMultipleEntryKey: 2024,
    OrgId: 1565,
    OrgName: "Northwestern University(old rollover)",
    MACity: "North",
    MAState: "MA",
    MAZip: 60208,
    ParentsDatafromIndependentStudents: "Y",
    ParentsDataAgeCutoff: 28,
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Profile",
    CSSAPIProfilePartRetDomUndergrad: "Profile",
    CSSAPIProfilePartNewIntUndergrad: "Y",
    CSSAPIProfilePartRetIntUndergrad: "Y",
    GraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomGrad: "Profile",
    CSSAPIProfilePartRetDomGrad: "Profile",
    CSSAPIProfilePartNewIntGrad: "Y",
    CSSAPIProfilePartRetIntGrad: "Y",
    CSSAPIProfilePartTranDomUndergrad: "Profile",
    CSSAPIProfilePartTranIntUndergrad: "Y",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 1565,
    OrgName: "Northwestern University",
    MACity: "North",
    MAState: "IL",
    MAZip: 60208,
    ParentsDatafromIndependentStudents: "Y",
    ParentsDataAgeCutoff: 30,
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Y",
    CSSAPIProfilePartRetDomUndergrad: "Y",
    CSSAPIProfilePartNewIntUndergrad: "Y",
    CSSAPIProfilePartRetIntUndergrad: "Y",
    GraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomGrad: "Y",
    CSSAPIProfilePartRetDomGrad: "Y",
    CSSAPIProfilePartNewIntGrad: "Y",
    CSSAPIProfilePartRetIntGrad: "Y",
    CSSAPIProfilePartTranDomUndergrad: "Y",
    CSSAPIProfilePartTranIntUndergrad: "Y",
  },
  {
    OrgMultipleEntryKey: 2024,
    OrgId: 3434,
    OrgName: "Harvard University(old rollover)",
    MACity: "Reston",
    MAState: "MA",
    MAZip: 20190,
    ParentsDatafromIndependentStudents: "Y",
    ParentsDataAgeCutoff: 30,
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Profile Lite",
    CSSAPIProfilePartRetDomUndergrad: "Profile Lite",
    CSSAPIProfilePartNewIntUndergrad: "Y",
    CSSAPIProfilePartRetIntUndergrad: "Y",
    GraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomGrad: "Profile Lite",
    CSSAPIProfilePartRetDomGrad: "Profile Lite",
    CSSAPIProfilePartNewIntGrad: "Y",
    CSSAPIProfilePartRetIntGrad: "Y",
    CSSAPIProfilePartTranDomUndergrad: "Profile Lite",
    CSSAPIProfilePartTranIntUndergrad: "Y",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 3282,
    OrgName: "College of the Holy Cross",
    MACity: "NULL",
    MAState: "NULL",
    MAZip: "NULL",
    ParentsDatafromIndependentStudents: "NULL",
    ParentsDataAgeCutoff: "NULL",
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Y",
    CSSAPIProfilePartRetDomUndergrad: "N",
    CSSAPIProfilePartNewIntUndergrad: "N",
    CSSAPIProfilePartRetIntUndergrad: "N",
    GraduateEnrollment: "N",
    CSSAPIProfilePartNewDomGrad: "N",
    CSSAPIProfilePartRetDomGrad: "N",
    CSSAPIProfilePartNewIntGrad: "N",
    CSSAPIProfilePartRetIntGrad: "N",
    CSSAPIProfilePartTranDomUndergrad: "N",
    CSSAPIProfilePartTranIntUndergrad: "N",
  },
  {
    OrgMultipleEntryKey: 2024,
    OrgId: 5025,
    OrgName: "Sample University(old rollover)",
    MACity: "Minneapolis &amp; St Paul",
    MAState: "MA",
    MAZip: "^^^^^",
    ParentsDatafromIndependentStudents: "Y",
    ParentsDataAgeCutoff: 35,
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Profile",
    CSSAPIProfilePartRetDomUndergrad: "None",
    CSSAPIProfilePartNewIntUndergrad: "Y",
    CSSAPIProfilePartRetIntUndergrad: "Y",
    GraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomGrad: "None",
    CSSAPIProfilePartRetDomGrad: "None",
    CSSAPIProfilePartNewIntGrad: "Y",
    CSSAPIProfilePartRetIntGrad: "Y",
    CSSAPIProfilePartTranDomUndergrad: "None",
    CSSAPIProfilePartTranIntUndergrad: "Y",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 3351,
    OrgName: "Dartmouth College",
    MACity: "Dartmouth",
    MAState: "",
    MAZip: 12345,
    ParentsDatafromIndependentStudents: "NULL",
    ParentsDataAgeCutoff: "NULL",
    UndergraduateEnrollment: "N",
    CSSAPIProfilePartNewDomUndergrad: "N",
    CSSAPIProfilePartRetDomUndergrad: "N",
    CSSAPIProfilePartNewIntUndergrad: "N",
    CSSAPIProfilePartRetIntUndergrad: "N",
    GraduateEnrollment: "N",
    CSSAPIProfilePartNewDomGrad: "N",
    CSSAPIProfilePartRetDomGrad: "N",
    CSSAPIProfilePartNewIntGrad: "N",
    CSSAPIProfilePartRetIntGrad: "N",
    CSSAPIProfilePartTranDomUndergrad: "N",
    CSSAPIProfilePartTranIntUndergrad: "N",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 3762,
    OrgName: "Smith College",
    MACity: "Smith City",
    MAState: "",
    MAZip: 1,
    ParentsDatafromIndependentStudents: "Y",
    ParentsDataAgeCutoff: 35,
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Y",
    CSSAPIProfilePartRetDomUndergrad: "Y",
    CSSAPIProfilePartNewIntUndergrad: "Y",
    CSSAPIProfilePartRetIntUndergrad: "Y",
    GraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomGrad: "N",
    CSSAPIProfilePartRetDomGrad: "Y",
    CSSAPIProfilePartNewIntGrad: "N",
    CSSAPIProfilePartRetIntGrad: "N",
    CSSAPIProfilePartTranDomUndergrad: "Y",
    CSSAPIProfilePartTranIntUndergrad: "Y",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 4563,
    OrgName: "Georgetown University in Qatar",
    MACity: "Match",
    MAState: "AL",
    MAZip: 20170,
    ParentsDatafromIndependentStudents: "N",
    ParentsDataAgeCutoff: 35,
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Y",
    CSSAPIProfilePartRetDomUndergrad: "Y",
    CSSAPIProfilePartNewIntUndergrad: "Y",
    CSSAPIProfilePartRetIntUndergrad: "Y",
    GraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomGrad: "Y",
    CSSAPIProfilePartRetDomGrad: "N",
    CSSAPIProfilePartNewIntGrad: "N",
    CSSAPIProfilePartRetIntGrad: "N",
    CSSAPIProfilePartTranDomUndergrad: "Y",
    CSSAPIProfilePartTranIntUndergrad: "Y",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 5024,
    OrgName: "College of Sample",
    MACity: "Boston",
    MAState: "MA",
    MAZip: 2130,
    ParentsDatafromIndependentStudents: "N",
    ParentsDataAgeCutoff: 35,
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Y",
    CSSAPIProfilePartRetDomUndergrad: "N",
    CSSAPIProfilePartNewIntUndergrad: "N",
    CSSAPIProfilePartRetIntUndergrad: "N",
    GraduateEnrollment: "N",
    CSSAPIProfilePartNewDomGrad: "N",
    CSSAPIProfilePartRetDomGrad: "N",
    CSSAPIProfilePartNewIntGrad: "N",
    CSSAPIProfilePartRetIntGrad: "N",
    CSSAPIProfilePartTranDomUndergrad: "N",
    CSSAPIProfilePartTranIntUndergrad: "N",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 7050,
    OrgName: "Southeastern Baptist Theological Seminary, NC(No R",
    MACity: "NULL",
    MAState: "NULL",
    MAZip: "NULL",
    ParentsDatafromIndependentStudents: "NULL",
    ParentsDataAgeCutoff: "NULL",
    UndergraduateEnrollment: "NULL",
    CSSAPIProfilePartNewDomUndergrad: "NULL",
    CSSAPIProfilePartRetDomUndergrad: "NULL",
    CSSAPIProfilePartNewIntUndergrad: "NULL",
    CSSAPIProfilePartRetIntUndergrad: "NULL",
    GraduateEnrollment: "NULL",
    CSSAPIProfilePartNewDomGrad: "NULL",
    CSSAPIProfilePartRetDomGrad: "NULL",
    CSSAPIProfilePartNewIntGrad: "NULL",
    CSSAPIProfilePartRetIntGrad: "NULL",
    CSSAPIProfilePartTranDomUndergrad: "NULL",
    CSSAPIProfilePartTranIntUndergrad: "NULL",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 5025,
    OrgName: "Sample University",
    MACity: "Minneapolis &amp; St Paul",
    MAState: "MA",
    MAZip: "^^^^^",
    ParentsDatafromIndependentStudents: "Y",
    ParentsDataAgeCutoff: 35,
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Y",
    CSSAPIProfilePartRetDomUndergrad: "Y",
    CSSAPIProfilePartNewIntUndergrad: "Y",
    CSSAPIProfilePartRetIntUndergrad: "Y",
    GraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomGrad: "Y",
    CSSAPIProfilePartRetDomGrad: "Y",
    CSSAPIProfilePartNewIntGrad: "Y",
    CSSAPIProfilePartRetIntGrad: "Y",
    CSSAPIProfilePartTranDomUndergrad: "Y",
    CSSAPIProfilePartTranIntUndergrad: "Y",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 3434,
    OrgName: "Harvard University",
    MACity: "Reston",
    MAState: "",
    MAZip: 20190,
    ParentsDatafromIndependentStudents: "Y",
    ParentsDataAgeCutoff: 30,
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Y",
    CSSAPIProfilePartRetDomUndergrad: "Y",
    CSSAPIProfilePartNewIntUndergrad: "Y",
    CSSAPIProfilePartRetIntUndergrad: "Y",
    GraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomGrad: "Y",
    CSSAPIProfilePartRetDomGrad: "Y",
    CSSAPIProfilePartNewIntGrad: "Y",
    CSSAPIProfilePartRetIntGrad: "Y",
    CSSAPIProfilePartTranDomUndergrad: "Y",
    CSSAPIProfilePartTranIntUndergrad: "Y",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 3526,
    OrgName: "Middlebury College",
    MACity: "NULL",
    MAState: "NULL",
    MAZip: "NULL",
    ParentsDatafromIndependentStudents: "NULL",
    ParentsDataAgeCutoff: "NULL",
    UndergraduateEnrollment: "NULL",
    CSSAPIProfilePartNewDomUndergrad: "NULL",
    CSSAPIProfilePartRetDomUndergrad: "NULL",
    CSSAPIProfilePartNewIntUndergrad: "NULL",
    CSSAPIProfilePartRetIntUndergrad: "NULL",
    GraduateEnrollment: "NULL",
    CSSAPIProfilePartNewDomGrad: "NULL",
    CSSAPIProfilePartRetDomGrad: "NULL",
    CSSAPIProfilePartNewIntGrad: "NULL",
    CSSAPIProfilePartRetIntGrad: "NULL",
    CSSAPIProfilePartTranDomUndergrad: "NULL",
    CSSAPIProfilePartTranIntUndergrad: "NULL",
  },
  {
    OrgMultipleEntryKey: 2024,
    OrgId: 5024,
    OrgName: "College of Sample(old rollover)",
    MACity: "Boston",
    MAState: "MA",
    MAZip: 2130,
    ParentsDatafromIndependentStudents: "N",
    ParentsDataAgeCutoff: 99,
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Profile Lite",
    CSSAPIProfilePartRetDomUndergrad: "Profile Lite",
    CSSAPIProfilePartNewIntUndergrad: "N",
    CSSAPIProfilePartRetIntUndergrad: "N",
    GraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomGrad: "Profile Lite",
    CSSAPIProfilePartRetDomGrad: "None",
    CSSAPIProfilePartNewIntGrad: "N",
    CSSAPIProfilePartRetIntGrad: "N",
    CSSAPIProfilePartTranDomUndergrad: "Profile Lite",
    CSSAPIProfilePartTranIntUndergrad: "N",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 5370,
    OrgName: "Loyola University Maryland",
    MACity: "Baltimore",
    MAState: "MD",
    MAZip: 21210,
    ParentsDatafromIndependentStudents: "N",
    ParentsDataAgeCutoff: 35,
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Y",
    CSSAPIProfilePartRetDomUndergrad: "Y",
    CSSAPIProfilePartNewIntUndergrad: "N",
    CSSAPIProfilePartRetIntUndergrad: "Y",
    GraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomGrad: "Y",
    CSSAPIProfilePartRetDomGrad: "Y",
    CSSAPIProfilePartNewIntGrad: "Y",
    CSSAPIProfilePartRetIntGrad: "Y",
    CSSAPIProfilePartTranDomUndergrad: "N",
    CSSAPIProfilePartTranIntUndergrad: "Y",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 3667,
    OrgName: "Northeastern University",
    MACity: "",
    MAState: "",
    MAZip: "",
    ParentsDatafromIndependentStudents: "N",
    ParentsDataAgeCutoff: 35,
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Y",
    CSSAPIProfilePartRetDomUndergrad: "Y",
    CSSAPIProfilePartNewIntUndergrad: "Y",
    CSSAPIProfilePartRetIntUndergrad: "Y",
    GraduateEnrollment: "N",
    CSSAPIProfilePartNewDomGrad: "N",
    CSSAPIProfilePartRetDomGrad: "N",
    CSSAPIProfilePartNewIntGrad: "N",
    CSSAPIProfilePartRetIntGrad: "N",
    CSSAPIProfilePartTranDomUndergrad: "Y",
    CSSAPIProfilePartTranIntUndergrad: "Y",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 2821,
    OrgName: "Swarthmore College",
    MACity: "",
    MAState: "",
    MAZip: "",
    ParentsDatafromIndependentStudents: "Y",
    ParentsDataAgeCutoff: 35,
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Y",
    CSSAPIProfilePartRetDomUndergrad: "Y",
    CSSAPIProfilePartNewIntUndergrad: "Y",
    CSSAPIProfilePartRetIntUndergrad: "Y",
    GraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomGrad: "Y",
    CSSAPIProfilePartRetDomGrad: "Y",
    CSSAPIProfilePartNewIntGrad: "Y",
    CSSAPIProfilePartRetIntGrad: "Y",
    CSSAPIProfilePartTranDomUndergrad: "Y",
    CSSAPIProfilePartTranIntUndergrad: "Y",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 3076,
    OrgName: "Bates College",
    MACity: "Dublin",
    MAState: "CA",
    MAZip: 94568,
    ParentsDatafromIndependentStudents: "N",
    ParentsDataAgeCutoff: "NULL",
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Y",
    CSSAPIProfilePartRetDomUndergrad: "Y",
    CSSAPIProfilePartNewIntUndergrad: "Y",
    CSSAPIProfilePartRetIntUndergrad: "Y",
    GraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomGrad: "Y",
    CSSAPIProfilePartRetDomGrad: "Y",
    CSSAPIProfilePartNewIntGrad: "Y",
    CSSAPIProfilePartRetIntGrad: "Y",
    CSSAPIProfilePartTranDomUndergrad: "N",
    CSSAPIProfilePartTranIntUndergrad: "N",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 6002,
    OrgName: "University of Holy Cross, LA",
    MACity: "Holy",
    MAState: "",
    MAZip: 1010,
    ParentsDatafromIndependentStudents: "N",
    ParentsDataAgeCutoff: 35,
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Y",
    CSSAPIProfilePartRetDomUndergrad: "Y",
    CSSAPIProfilePartNewIntUndergrad: "Y",
    CSSAPIProfilePartRetIntUndergrad: "Y",
    GraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomGrad: "Y",
    CSSAPIProfilePartRetDomGrad: "Y",
    CSSAPIProfilePartNewIntGrad: "Y",
    CSSAPIProfilePartRetIntGrad: "Y",
    CSSAPIProfilePartTranDomUndergrad: "Y",
    CSSAPIProfilePartTranIntUndergrad: "Y",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 2116,
    OrgName: "Columbia U: College &amp; Engineering",
    MACity: "NULL",
    MAState: "NULL",
    MAZip: "NULL",
    ParentsDatafromIndependentStudents: "NULL",
    ParentsDataAgeCutoff: "NULL",
    UndergraduateEnrollment: "N",
    CSSAPIProfilePartNewDomUndergrad: "N",
    CSSAPIProfilePartRetDomUndergrad: "N",
    CSSAPIProfilePartNewIntUndergrad: "N",
    CSSAPIProfilePartRetIntUndergrad: "N",
    GraduateEnrollment: "N",
    CSSAPIProfilePartNewDomGrad: "N",
    CSSAPIProfilePartRetDomGrad: "N",
    CSSAPIProfilePartNewIntGrad: "N",
    CSSAPIProfilePartRetIntGrad: "N",
    CSSAPIProfilePartTranDomUndergrad: "N",
    CSSAPIProfilePartTranIntUndergrad: "N",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 3003,
    OrgName: "Amherst College",
    MACity: "Somewhere",
    MAState: "FL",
    MAZip: 0,
    ParentsDatafromIndependentStudents: "N",
    ParentsDataAgeCutoff: 24,
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Y",
    CSSAPIProfilePartRetDomUndergrad: "N",
    CSSAPIProfilePartNewIntUndergrad: "Y",
    CSSAPIProfilePartRetIntUndergrad: "Y",
    GraduateEnrollment: "N",
    CSSAPIProfilePartNewDomGrad: "N",
    CSSAPIProfilePartRetDomGrad: "N",
    CSSAPIProfilePartNewIntGrad: "N",
    CSSAPIProfilePartRetIntGrad: "N",
    CSSAPIProfilePartTranDomUndergrad: "N",
    CSSAPIProfilePartTranIntUndergrad: "N",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 3089,
    OrgName: "Bowdoin College",
    MACity: "Paris",
    MAState: "",
    MAZip: 92837,
    ParentsDatafromIndependentStudents: "Y",
    ParentsDataAgeCutoff: 35,
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Y",
    CSSAPIProfilePartRetDomUndergrad: "N",
    CSSAPIProfilePartNewIntUndergrad: "N",
    CSSAPIProfilePartRetIntUndergrad: "N",
    GraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomGrad: "Y",
    CSSAPIProfilePartRetDomGrad: "N",
    CSSAPIProfilePartNewIntGrad: "N",
    CSSAPIProfilePartRetIntGrad: "N",
    CSSAPIProfilePartTranDomUndergrad: "N",
    CSSAPIProfilePartTranIntUndergrad: "N",
  },
  {
    OrgMultipleEntryKey: 2023,
    OrgId: 2095,
    OrgName: "Columbia U: School of General Studies",
    MACity: "Columbia",
    MAState: "",
    MAZip: 11111,
    ParentsDatafromIndependentStudents: "N",
    ParentsDataAgeCutoff: 35,
    UndergraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomUndergrad: "Y",
    CSSAPIProfilePartRetDomUndergrad: "Y",
    CSSAPIProfilePartNewIntUndergrad: "Y",
    CSSAPIProfilePartRetIntUndergrad: "Y",
    GraduateEnrollment: "Y",
    CSSAPIProfilePartNewDomGrad: "Y",
    CSSAPIProfilePartRetDomGrad: "Y",
    CSSAPIProfilePartNewIntGrad: "Y",
    CSSAPIProfilePartRetIntGrad: "Y",
    CSSAPIProfilePartTranDomUndergrad: "Y",
    CSSAPIProfilePartTranIntUndergrad: "Y",
  },
]
