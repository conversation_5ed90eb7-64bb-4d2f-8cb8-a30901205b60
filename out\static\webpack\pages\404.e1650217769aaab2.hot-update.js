"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/404",{

/***/ "(pages-dir-browser)/./src/utils/storeApp.js":
/*!*******************************!*\
  !*** ./src/utils/storeApp.js ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(pages-dir-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(pages-dir-browser)/./node_modules/zustand/esm/middleware.mjs\");\n\n\nconst userStore = (set)=>({\n        isUserLogedIn: false,\n        formURL: \"\",\n        appPool: \"\",\n        currentPageURL: \"\",\n        userForm: {},\n        backPageButton: [],\n        collegeObj: [],\n        moneyObj: [],\n        userObject: {},\n        pageIndex: \"\",\n        backButtonMode: false,\n        addCollegeFlag: false,\n        dollarFormat: false,\n        hideLogin: false,\n        SSNValue: \"\",\n        AppReviewNr: 0,\n        setAppReviewNr: (val)=>{\n            set((state)=>({\n                    AppReviewNr: val\n                }));\n        },\n        setHideLogin: (val)=>{\n            set((state)=>({\n                    hideLogin: val\n                }));\n        },\n        setSSNValue: (val)=>{\n            set((state)=>({\n                    SSNValue: val\n                }));\n        },\n        setDollarFormat: (flag)=>{\n            set((state)=>({\n                    dollarFormat: flag\n                }));\n        },\n        setMoneyObj: (obj)=>{\n            set((state)=>({\n                    moneyObj: obj\n                }));\n        },\n        setCollegeObj: (obj)=>{\n            set((state)=>({\n                    collegeObj: obj\n                }));\n        },\n        setAaddCollegeFlag: (flg)=>{\n            set((state)=>({\n                    addCollegeFlag: flg\n                }));\n        },\n        setAppPool: (site)=>{\n            set((state)=>({\n                    appPool: site\n                }));\n        },\n        setBackButtonMode: (mode)=>{\n            set((state)=>({\n                    backButtonMode: mode\n                }));\n        },\n        setPageIndex: (idx)=>{\n            set((state)=>({\n                    pageIndex: idx\n                }));\n        },\n        setBackPageButton: (obj)=>{\n            set((state)=>({\n                    backPageButton: obj.length === 0 ? [] : obj\n                }));\n        },\n        setUserLogedIn: (flag)=>{\n            set((state)=>({\n                    isUserLogedIn: flag\n                }));\n        },\n        setpageUrl: (page)=>{\n            set((state)=>({\n                    currentPageURL: page\n                }));\n        },\n        setpageFormUrl: (page)=>{\n            set((state)=>({\n                    formURL: page\n                }));\n        },\n        setUserForm: (form)=>{\n            set((state)=>({\n                    userForm: form\n                }));\n        },\n        updateUserForm: (key, val)=>{\n            set((state)=>({\n                    userForm: {\n                        ...state.userForm,\n                        [key]: val\n                    }\n                }));\n        }\n    });\nconst useUserStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.devtools)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)(userStore, {\n    name: \"user\"\n})));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useUserStore);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/utils/storeApp.js\n"));

/***/ })

});