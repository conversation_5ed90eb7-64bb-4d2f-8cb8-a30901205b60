import React from "react";
import { useStaticQuery, graphql } from "gatsby";
import { Label } from "./inputs/Label";
import Checkbox from "./inputs/Checkbox";
import DateInputNew from "./inputs/DateInputNew";
import { MoneyInput } from "./inputs/MoneyInput";
import { NumberInput } from "./inputs/NumberInput";
import { RadioGroup } from "./inputs/RadioGroup";
import { SelectInput } from "./inputs/SelectInput";
import { TextInput } from "./inputs/TextInput";
import { SSNInput } from "./inputs/SSNInput";
import { TextArea } from "./inputs/TextArea";
import { ValidationError } from "./ValidationError";
import Hint from "./Hint";
import ExpandableHint from "./ExpandableHint";
import parse from "html-react-parser";
import { Button } from "./Button";

function QuestionInput({
  id,
  fieldType,
  format,
  error,
  dict,
  dicts,
  onChange,
  onClick,
  required,
  disabled,
  label,
  value,
  checkboxCheckedValue,
  minLength,
  maxLength,
  rangeLow,
  rangeHigh,
  data,
  displayFormat,
  questionValue,
}) {
  switch (fieldType) {
    case "text":
      return getTextInputForFormat(format, {
        id,
        onChange,
        error,
        required,
        disabled,
        value,
        minLength,
        maxLength,
        rangeLow,
        rangeHigh,
        format,
        displayFormat,
      });
    case "yesnobutton":
      return (
        <div className="w-full flex flex-row gap-x-3" role="radiogroup">
          {getDictOptions(dicts, dict).map((el, idx) => (
            <Button
              key={el.Value}
              type="button"
              id={`${id}${el.Display}`}
              variant="white"
              onClick={(elem) => {
                let no = document.getElementById(`${id}No`);
                let yes = document.getElementById(`${id}Yes`);
                if (el.Display === "Yes") {
                  no.classList.remove("bg-sky-200");
                  yes.classList.add("bg-sky-200");
                  no.ariaChecked = false;
                  yes.ariaChecked = true;
                } else if (el.Display === "No") {
                  yes.classList.remove("bg-sky-200");
                  no.classList.add("bg-sky-200");
                  yes.ariaChecked = false;
                  no.ariaChecked = true;
                }
                onClick(el.Value);
              }}
              className={`focus:bg-sky-100 active:bg-sky-200 w-full ${
                el.Value === questionValue ? "bg-sky-200" : ""
              }`}
              role="radio"
              aria-checked={el.Value === questionValue}
              onKeyDown={(el) => {
                let no = document.getElementById(`${id}No`);
                let yes = document.getElementById(`${id}Yes`);
                if (el.keyCode === 37) {
                  no.classList.remove("bg-sky-200");
                  yes.classList.add("bg-sky-200");
                  no.ariaChecked = false;
                  yes.ariaChecked = true;
                  yes.focus();
                } else if (el.keyCode === 39) {
                  yes.classList.remove("bg-sky-200");
                  no.classList.add("bg-sky-200");
                  yes.ariaChecked = false;
                  no.ariaChecked = true;
                  no.focus();
                }
              }}
            >
              <Label htmlFor={`${id}${el.Display}`}>
                {el.Display}{" "}
                <img
                  className="inline "
                  src={
                    el.Value === "Y"
                      ? process.env.IMG_URL + data.checkmark.publicURL
                      : process.env.IMG_URL + data.nobutton.publicURL
                  }
                  alt=""
                  height={16}
                  width={16}
                />
              </Label>
            </Button>
          ))}
        </div>
      );
    case "select":
      return (
        <SelectInput
          options={getDictOptions(dicts, dict)}
          {...{ id, onChange, error, required, disabled, value }}
        />
      );
    case "checkbox":
      return (
        <Checkbox
          {...{ id, onChange, error, required, value, checkboxCheckedValue }}
          label={label}
        />
      );
    case "radio":
      return (
        <RadioGroup
          options={getDictOptions(dicts, dict)}
          {...{ id, onChange, error, required, disabled, value }}
        />
      );
    case "textarea":
      return (
        <TextArea
          {...{
            id,
            onChange,
            minLength,
            maxLength,
            rangeLow,
            rangeHigh,
            error,
            required,
            disabled,
            value,
          }}
        />
      );
    default:
      throw `Unknown field type: ${fieldType}}`;
  }
}

function getTextInputForFormat(format, inputProps) {
  switch (format) {
    case "date":
      return (
        <DateInputNew
          pattern="[0-9]*"
          title="Only numbers are allowed"
          displayFormat={inputProps.displayFormat.toLowerCase()}
          {...inputProps}
        />
      );
    case "money":
      return <MoneyInput title="Only numbers are allowed" {...inputProps} />;
    case "number":
      return (
        <NumberInput
          pattern="[0-9]*"
          title="Only numbers are allowed"
          aria-describedby={inputProps.id + "Desc"}
          {...inputProps}
        />
      );
    case "email":
      return <TextInput {...inputProps} type="email" />;
    case "ssn":
      return (
        <SSNInput
          // pattern="[0-9]*"
          {...inputProps}
          title="Only numbers are allowed"
        />
      );
    case "zip":
      return (
        <TextInput
          {...inputProps}
          title="Only alphanumeric characters are allowed"
        />
      );
    case "":
      return <TextInput {...inputProps} />;
    default:
      throw `Unknown text field format: ${format}}`;
  }
}
function getDictOptions(options, optionName) {
  return findDict(options, optionName).sort(byAscendingOrder);
}

function findDict(options, optionName) {
  return options.find((option) => option.Name === optionName).DictItems;
}
const byAscendingOrder = (a, b) => {
  return a.Order - b.Order;
};
const Question = ({
  id,
  ariaDescribedby,
  required,
  fieldType,
  label,
  dict,
  dicts,
  format,
  onChange,
  onClick,
  disabled,
  value,
  error,
  checkboxCheckedValue,
  minLength,
  maxLength,
  rangeLow,
  rangeHigh,
  helpContent,
  helpStyle,
  helpTitle,
  displayFormat,
  questionValue,
}) => {
  const isRequired = required === "Y";
  const data = useStaticQuery(graphql`
    query {
      nobutton: file(relativePath: { eq: "no-button.svg" }) {
        name
        publicURL
      }
      checkmark: file(relativePath: { eq: "checkmark.svg" }) {
        name
        publicURL
      }
    }
  `);
  const isString = (thing) =>
    Object.prototype.toString.call(thing) === "[object String]";
  return (
    <fieldset className="space-y-2.5">
      {/* <div className="space-y-2.5"> */}
      {fieldType !== "checkbox" ? (
        fieldType === "yesnobutton" ? (
          <legend htmlFor={id} className="font-bold">
            {isString(label) ? parse(label) : label}
            {isRequired ? " (required)" : ""}
          </legend>
        ) : (
          <Label
            htmlFor={id}
            required={isRequired}
            labelFormat={format === "date" || fieldType === "radio"}
          >
            {label}
          </Label>
        )
      ) : null}

      {helpContent ? (
        helpStyle === "Expandable Hint" ? (
          <ExpandableHint title={helpTitle} content={parse(helpContent)} />
        ) : helpStyle === "Hint" ? (
          <Hint description={helpContent} />
        ) : null
      ) : null}
      <QuestionInput
        {...{
          dicts,
          dict,
          id,
          error,
          fieldType,
          format,
          onChange,
          onClick,
          disabled,
          label,
          value,
          required: isRequired,
          checkboxCheckedValue,
          minLength,
          maxLength,
          rangeLow,
          rangeHigh,
          data,
          displayFormat,
          questionValue,
        }}
      />

      {error && <ValidationError message={error} />}
      {helpStyle === "Notice"
        ? helpStyle && (
            <Hint
              description={helpContent}
              notice="true"
              id={ariaDescribedby ? ariaDescribedby + "Desc" : ""}
            />
          )
        : null}
      {/* </div> */}
    </fieldset>
  );
};
export default Question;
