import React from "react"
import { useStaticQuery, graphql } from "gatsby"
import parse from "html-react-parser"
import { hintClasses } from "../shared/hints"

const Hint = ({ description, ...props }) => {
  const { notice } = props
  const isNotice = <PERSON><PERSON><PERSON>(notice)

  const title = isNotice ? "Note:" : "Hint:"
  const showIcon = !isNotice

  const data = useStaticQuery(graphql`
    query {
      info: file(relativePath: { eq: "info-icon.svg" }) {
        name
        publicURL
      }
    }
  `)
  const infoIcon = (
    <img
      className="text-black inline "
      src={process.env.IMG_URL + data.info.publicURL}
      alt="Information Icon"
      height={16}
      width={16}
    />
  )
  return (
    <div className={`space-y-5 ${hintClasses} p-5`} {...props}>
      {showIcon && infoIcon}
      <span className={` ${showIcon ? "pl-2.5" : ""}`}>
        <strong>{title}</strong>&nbsp;
      </span>
      {parse(description)}
    </div>
  )
}
export default Hint
