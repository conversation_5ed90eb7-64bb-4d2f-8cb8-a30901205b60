import React, { useEffect, useState } from "react";
import { navigate } from "gatsby";
import { States } from "../fakeDB/state";
import Layout from "../components/layout";
import { Heading } from "../components/Heading";
import Helmet from "react-helmet";
import { Label } from "../components/inputs/Label";
import RadioButton from "../components/inputs/RadioButton";
import { SelectInput } from "../components/inputs/SelectInput";
import { TextInput } from "../components/inputs/TextInput";
import { Button } from "../components/Button";
import axios from "axios";
import { ProcessingModal } from "../components/processingModal";
import { saveFormPageMultiple } from "../components/NewComp";
import { ValidationError } from "../components/ValidationError";
import useUserStore from "../utils/storeApp";
import WithLocation from "../components/withLocation";
import Hint from "../components/Hint";

const Program = () => {
  const {
    setpageFormUrl,
    userForm,
    isUserLogedIn,
    setUserForm,
    setpageUrl,
    backPageButton,
    setBackPageButton,
    pageIndex,
    setPageIndex,
    backButtonMode,
    setBackButtonMode,
    addCollegeFlag,
    setAaddCollegeFlag,
  } = useUserStore((state) => ({
    setpageFormUrl: state.setpageFormUrl,
    setUserForm: state.setUserForm,
    userForm: state.userForm,
    isUserLogedIn: state.isUserLogedIn,
    setpageUrl: state.setpageUrl,
    backPageButton: state.backPageButton,
    setBackPageButton: state.setBackPageButton,
    pageIndex: state.pageIndex,
    setPageIndex: state.setPageIndex,
    backButtonMode: state.backButtonMode,
    setBackButtonMode: state.setBackButtonMode,
    addCollegeFlag: state.addCollegeFlag,
    setAaddCollegeFlag: state.setAaddCollegeFlag,
  }));

  const [isChecked, setIsChecked] = useState("CSS Code Number");
  const [selOpt, setSelOpt] = useState("CSS Code Number");
  const [selId, setSelId] = useState("CSSCodeNumber");
  const [stateSel, setStateSel] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [dataObj, setDataObj] = useState([]);
  const [flag, SetFlag] = useState(false);
  const [msg, SetMsg] = useState("");
  const [msgNoRecordFound, SetMsgNoRecordFound] = useState(false);
  const [disableBtn, SetdisableBtn] = useState(false);
  const [awardYear, SetAwardYear] = useState("");
  const [StudentFirstName, SetStudentFirstName] = useState("");

  const Component = ({ name, isLegend, id, children, isRequired }) => {
    return (
      <>
        {isLegend ? (
          <legend htmlFor={id} className="font-bold">
            {name}
            {isRequired ? " (required)" : ""}
          </legend>
        ) : (
          <Label required={isRequired} for={id}>
            {name}
          </Label>
        )}
        {children}
      </>
    );
  };
  const getOption = (opt) => {
    if (opt !== "State") {
      setSelOpt(opt);
      let vl = opt.replaceAll(" ", "").replace("/", "");
      setSelId(vl);
    }
  };
  const temp = [];
  const onFormSubmition = async (event) => {
    event.preventDefault();
    SetIsClicked({});
    setExistsCollege({});
    SetdisableBtn(true);
    setShowModal(true);
    SetMsgNoRecordFound(false);
    setDataObj([]);
    let searchVal,
      objItem = null;
    if (isChecked === "State") {
      searchVal = stateSel;
      objItem = { Name: "State", Value: searchVal };
    } else {
      searchVal = document.getElementById(selId).value;
      objItem = {
        Name: isChecked === "CSS Code Number" ? "OrgID" : "OrgName",
        Value: searchVal.trim(),
      };
    }
    // let obj = {
    //   Fields: [
    //     { Name: "ItemName", Value: "GetProfileOrganizations" },
    //     { Name: "AwardYear", Value: userForm.AwardYear },
    //     objItem,
    //   ],
    // };
    var fm = {
      Username: userForm.Username,
      UserId: userForm.UserId,
      AwardYear: userForm.AwardYear, //"2024",
      ApplicationId: userForm.ApplicationId,
      OrgFormId: userForm.OrgFormId,
      FormId: userForm.FormId,
      ...objItem,
    };
    const temp = [];
    await axios({
      method: "post",
      url: process.env.API_URL + "college/GetProfileOrganizations",
      data: fm,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userForm.Token}`,
      },
      withCredentials: true,
    })
      .then((res) => {
        if (res.status === 200) {
          SetdisableBtn(false);
          const dtObj = res.data.ProfileCollegeList;
          // let o = dtObj.map(async (elem, idx) => {
          //   const flatObj = {};
          //   elem.MessageList.map((elx) => {
          //     flatObj[elx.Name] = elx.Value;
          //   });
          //   temp.push(flatObj);
          // });
          // if (temp.length === 0) SetMsgNoRecordFound(true);
          // getMsg(temp);
          if (dtObj.length === 0) SetMsgNoRecordFound(true);
          setDataObj(dtObj);
          document.getElementById("scrollToTop").scrollIntoView();
          document.getElementById("scrollToTop").focus();
          SetPanelShow(true);
          setShowModal(false);
        } else {
          SetdisableBtn(false);
          setShowModal(false);
        }
      })
      .catch((err) => {
        SetdisableBtn(false);
        setShowModal(false);
      });
  };
  const getMsg = async (arr) => {
    setShowModal(true);
    for (const row of arr) {
      row.WrnMsg = await getCollegeMsg(row.OrgId);
    }
    setDataObj(arr);
    document.getElementById("scrollToTop").scrollIntoView();
    document.getElementById("scrollToTop").focus();
    setShowModal(false);
  };
  const getCollegeMsg = async (cssCode) => {
    const formUser = { ...userForm, CSSCode: cssCode };
    const bodyFormData = new FormData();
    bodyFormData.append("sbr", JSON.stringify(formUser));
    const arrErr = await axios({
      method: "post",
      url: process.env.API_URL + "college/GetCollegeMessages",
      data: bodyFormData,
      headers: {
        "Content-Type": "multipart/form-data",
        Authorization: `Bearer ${userForm.Token}`,
      },
    })
      .then((res) => {
        if (res.status === 200) {
          return res.data.MessageList;
        } else if (res.data.returnMessage === "SERVERERROR") {
          SetFlag(true);
          SetMsg("Server error has occured.");
          return;
        } else if (res.data.ReturnMessage === "FAILED") {
          SetFlag(true);
          SetMsg("No data found.");
          return;
        }
      })
      .catch((err) => {
        SetMsg("Server error has occured.");
        return;
      });
    return await arrErr;
  };
  const [orgSel, setOrgSel] = useState([]);
  const [hasOrgSel, setHasOrgSel] = useState(false);
  const [existsCollege, setExistsCollege] = useState({});
  let tempobj = [];
  const AddCollege = async (action, code, name, state, old) => {
    setShowModal(true);
    setHasOrgSel(false);
    setAaddCollegeFlag(false);
    let showDiv = false;
    let obj = [];
    let tmp = { ...isClicked };
    let existsTemp = { ...existsCollege };
    tmp[code] || old ? (action = "remove") : (action = "add");
    if (action === "add") {
      await setHasOrgSel(true);
      showDiv = true;
      let add = {
        CSSCode: code,
        InstitutionName: name,
        State: state,
        Action: "Add",
        MessageList: msg,
      };
      tempobj.push(add);
      if (orgSel.length > 0) {
        let exists = false;
        const tempObj = orgSel.map((el) => {
          if (el.CSSCode == code && el.Action === "Remove") {
            exists = true;
          }
          if (el.CSSCode == code && el.SchoolSubmitted !== "Y") {
            el.Action = "Add";
          }
          return el;
        });
        let hasProp = orgSel.some((org) => org["CSSCode"] === code);
        if (hasProp) {
          existsTemp[code] = true;
          await setOrgSel(tempObj);
        } else {
          existsTemp[code] = false;
          tmp[code] = true;
          await SetIsClicked(tmp);
          await setOrgSel([...orgSel, add]);
        }
        if (exists) {
          tmp[code] = true;
          await SetIsClicked(tmp);
        }
      } else {
        existsTemp[code] = false;
        tmp[code] = true;
        SetIsClicked(tmp);
        await setOrgSel([...orgSel, add]);
      }
    } else {
      existsTemp[code] = false;
      orgSel.map((el) => {
        if (el.CSSCode === code) {
          el.Action = "Remove";
          tmp[code] = false;
          SetIsClicked(tmp);
        }
        obj.push(el);
      });
      await setOrgSel([...obj]);
    }
    orgSel.map((el) => {
      if (el.Action !== "Remove") {
        setHasOrgSel(true);
        showDiv = true;
      }
    });
    // if (showDiv) {
    //   document.getElementById("scrollToTop").scrollIntoView()
    //   document.getElementById("scrollToTop").focus()
    // }
    setExistsCollege(existsTemp);
    setShowModal(false);
    if (action === "add") {
      setAaddCollegeFlag(code);
      SubmitData();
    }
  };
  const SubmitData = async () => {
    SetdisableBtn(true);
    setShowModal(true);
    setBackButtonMode(false);
    let o = [...orgSel, ...tempobj];
    const formPost = { ...userForm, ProfileCollegeList: o };
    const res = await saveFormPageMultiple(
      "college/PostCollegeSelection",
      formPost
    );
    console.log(res);
    if (res.status === "SUCCESS") {
      SetdisableBtn(false);
      SetFlag(false);
      setUserForm({ ...res.data, Token: userForm.Token });
      setpageFormUrl(res.data.PageName);
      // if (res.data.PageName === "AcademicInfo3") {
      //   navigate("/school");
      // } else
      if (res.data.Multiple === "Y") {
        navigate("/multiple");
      } else {
        navigate("/formpage");
      }
    } else if (res.status === "FAILED") {
      SetdisableBtn(false);
      const failVal = res.data.QuestionErrorList
        ? valErrors(res.data.QuestionErrorList)
        : "Error has occurred submitting your selection.";
      SetFlag(true);
      SetMsg(failVal);
    } else if (res.status === "SERVERERROR") {
      SetdisableBtn(false);
      SetFlag(true);
      SetMsg(res.data);
    }
    setShowModal(false);
    document.getElementById("scrollToTop").scrollIntoView();
    document.getElementById("scrollToTop").focus();
  };
  const valErrors = (obj) => {
    return obj.map((el) => <p>{`${el.Response} ${el.ErrorMsg}`}</p>);
  };
  const goToNextPage = async (url) => {
    setpageFormUrl(url);
    //await dispatch({ type: "UPDATE_PAGE", payload: url })
    navigate("/checkList");
  };
  const Section = ({ title, id, children }) => {
    return (
      <section className="flex w-full flex-col gap-2" id={id}>
        <h2 id={title} className="group md:text-xl text-lg font-bold">
          {title}
        </h2>
        <fieldset className="space-y-2.5">{children}</fieldset>
      </section>
    );
  };
  const [panelShow, SetPanelShow] = useState(false);
  useEffect(() => {
    setAaddCollegeFlag(false);
    const fetchData = async () => {
      if (!isUserLogedIn) {
        navigate("https://account.collegeboard.org");
      }
      setShowModal(true);
      setpageUrl("program");
      const formUser = userForm;
      backPageButton.push(formUser);
      if (!backButtonMode) {
        setPageIndex(backPageButton.length - 2);
      }
      setHasOrgSel(false);
      await axios({
        method: "post",
        url: process.env.API_URL + "FormPage/GetFormPageItems",
        data: formUser,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userForm.Token}`,
        },
        withCredentials: true,
      })
        .then((res) => {
          setShowModal(false);
          if (res.status === 200) {
            SetStudentFirstName(res.data);
            if (res.data.ProfileCollegeList.length > 0) {
              setHasOrgSel(true);
            }
            setOrgSel(res.data.ProfileCollegeList);
          } else if (res.data.returnMessage === "SERVERERROR") {
            SetFlag(true);
            SetMsg("Server error has occured.");
            return;
          } else if (res.data.ReturnMessage === "FAILED") {
            SetFlag(true);
            SetMsg("No data found.");
            return;
          }
        })
        .catch((err) => {
          SetMsg("Server error has occured.");
          setShowModal(false);
          return;
        });
    };
    fetchData();
  }, []);
  const goBackToPage = async () => {
    setBackButtonMode(true);
    const goBack = backPageButton[pageIndex];
    setPageIndex(pageIndex - 1);
    setUserForm(goBack);
    setpageFormUrl(goBack.PageName);
    if (
      // goBack.PageName === "AcademicInfo3" ||
      goBack.PageName === "AcademicInfo3a"
    ) {
      navigate("/school");
    } else if (goBack.PageName === "AcademicInfo4") {
      navigate("/program");
    } else if (goBack.PageName === "Correction") {
      navigate("/payment");
    } else if (goBack.PageName === "AppCheckList") {
      navigate("/checkList");
    } else if (goBack.PageName === "ApplicationReview") {
      navigate("/appReview");
    } else if (goBack.PageName === "DataChecks") {
      navigate("/dataChecks");
    } else if (goBack.Multiple === "Y") {
      navigate("/multiple");
    } else {
      navigate("/formpage");
    }
  };
  const [isClicked, SetIsClicked] = useState({});
  const isString = (thing) =>
    Object.prototype.toString.call(thing) === "[object String]";

  return (
    <Layout>
      <Helmet
        htmlAttributes={{
          lang: "en",
        }}
        title={"College/Program Selection"}
      />
      <main className="mx-auto space-y-5 bg-white p-7 md:p-11 md:max-w-lg md:shadow-md md:shadow-light-purple">
        {showModal ? <ProcessingModal /> : null}
        {flag && <ValidationError message={msg} />}
        <div className="font-bold md:text-sm text-md">
          <div>
            <a
              href="javascript:void(0);"
              className="cursor-pointer text-sky-700 underline"
              onClick={() => goToNextPage("ApplicationChecklist")}
              id={`GoBackBtn1`}
            >
              ←Sections
            </a>{" "}
            / {StudentFirstName.PageHeading}
          </div>
        </div>
        <hr />

        {panelShow ? (
          <>
            <Section title={"College/Program Selection"}>
              {msgNoRecordFound ? (
                <ValidationError
                  message={
                    "No colleges or programs matched your search.  Please Go Back and try another search term."
                  }
                />
              ) : null}
              {dataObj.map((el, idx) => {
                return (
                  <div
                    key={el.CSSCode}
                    className={` max-w-xl space-y-5 rounded-md border-2 p-3`}
                  >
                    <div key={el.InstitutionName}>
                      <div
                        className={`flex flex-col flex-wrap justify-between gap-x-3 gap-y-1 md:flex-row`}
                      >
                        <div
                          className="font-bold"
                          dangerouslySetInnerHTML={{
                            __html: el.InstitutionName,
                          }}
                        />
                        <div>{`CSS Code Number ${el.CSSCode}`}</div>
                      </div>
                      {el.MessageList ? (
                        <div
                          className={`space-y-2 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50 p-3`}
                        >
                          {el.MessageList.map((p) => (
                            <p key={p.Message}>{p.Message}</p>
                          ))}
                        </div>
                      ) : null}
                      <div
                        className={`cursor-pointer text-center font-bold ${
                          isClicked[el.CSSCode]
                            ? "text-green-700 underline"
                            : existsCollege[el.CSSCode]
                            ? "text-yellow-600"
                            : "text-sky-700"
                        } hover:underline active:text-sky-900`}
                      >
                        <a
                          href="javascript:void(0);"
                          onClick={() => {
                            AddCollege(
                              "add",
                              el.CSSCode,
                              el.InstitutionName,
                              el.State,
                              false
                            );
                          }}
                        >
                          {isClicked[el.CSSCode] || existsCollege[el.CSSCode]
                            ? null
                            : `Select`}{" "}
                          <span
                            dangerouslySetInnerHTML={{
                              __html: el.OrgName,
                            }}
                          />
                          {isClicked[el.CSSCode]
                            ? " Selected"
                            : existsCollege[el.CSSCode]
                            ? " Already Submitted"
                            : null}
                        </a>
                      </div>
                    </div>
                  </div>
                );
              })}
              {/* <Button
                type="button"
                onClick={() => SetPanelShow(false)}
                className="w-full md:w-fit"
              >
                Continue
              </Button> */}
            </Section>
            <hr />
            <a
              href="javascript:void(0);"
              className="cursor-pointer text-sky-700 underline font-bold md:text-sm text-md grid justify-items-end"
              onClick={() => SetPanelShow(false)}
            >
              ←Go Back
            </a>
          </>
        ) : (
          <div className="max-w-md space-y-4">
            <form onSubmit={(event) => onFormSubmition(event, true)}>
              <div className={`space-y-4`}>
                <h2 className={`font-bold`}>
                  {StudentFirstName.SectionHeading}
                </h2>
                <div className="space-y-3 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50 p-3">
                  Search to select the colleges and programs that you want to
                  receive your CSS Profile information. You can add or remove
                  colleges or programs at any time before you submit your
                  application.
                </div>
                <fieldset className={`space-y-3`}>
                  <legend className={`font-bold`} for="f_stu_programs">
                    Search By
                  </legend>
                  <div id="f_stu_programs" className={`space-y-3`}>
                    <RadioButton
                      onChange={(checked) => {
                        setIsChecked(checked.value);
                        getOption(checked.value);
                      }}
                      id="program_code"
                      name="program"
                      value="CSS Code Number"
                      label="CSS Code Number"
                      checked={"CSS Code Number" === isChecked}
                      required
                    />
                    <RadioButton
                      onChange={(checked) => {
                        setIsChecked(checked.value);
                        getOption(checked.value);
                      }}
                      id="program_name"
                      name="program"
                      value="College/Program Name"
                      label="College/Program Name"
                      required
                      checked={"College/Program Name" === isChecked}
                    />
                    <RadioButton
                      onChange={(checked) => {
                        setIsChecked(checked.value);
                        getOption(checked.value);
                      }}
                      id="program_state"
                      name="program"
                      value="State"
                      label="State"
                      required
                      checked={"State" === isChecked}
                    />
                  </div>
                </fieldset>
                <div id="f_stu_programs" className={`space-y-3`}>
                  {isChecked === "State" ? (
                    <Component name="State" id="State" isRequired={true}>
                      <SelectInput
                        name="State"
                        id="State"
                        value={stateSel}
                        onChange={(ev) => setStateSel(ev)}
                        options={States}
                        required
                      />
                    </Component>
                  ) : (
                    <Component name={selOpt} id={selId} isRequired={true}>
                      <TextInput
                        type="text"
                        id={selId}
                        onChange={(event) => console.log(event)}
                        required
                      />
                    </Component>
                  )}
                  <Button disabled={disableBtn} className="w-full md:w-fit">
                    Search
                  </Button>
                </div>
              </div>
            </form>
            {hasOrgSel ? (
              <Section
                title="Selected Colleges or Programs"
                id="SelectedCollPrograms"
              >
                {orgSel.map((el) =>
                  el?.Action === "Remove" ? null : (
                    <div
                      key={el.CSSCode}
                      className={` max-w-xl space-y-5 rounded-md border-2 p-3`}
                    >
                      <div
                        className={`flex flex-col flex-wrap justify-between gap-x-3 gap-y-1 md:flex-row`}
                      >
                        <span>{el.CSSCode}</span>
                        <span
                          dangerouslySetInnerHTML={{
                            __html: el.InstitutionName,
                          }}
                        />
                        <span>{el.State}</span>
                        {el.SchoolSubmitted === "Y" ? (
                          <span className=" text-center font-bold text-program-core-higher-ed underline">
                            Submitted
                          </span>
                        ) : (
                          <a
                            href="javascript:void(0);"
                            className="cursor-pointer text-center font-bold text-program-core-higher-ed underline"
                            onClick={() =>
                              AddCollege(
                                "remove",
                                el.CSSCode,
                                el.InstitutionName,
                                el.state,
                                true
                              )
                            }
                          >
                            Remove
                          </a>
                        )}
                      </div>
                      <div>{el.MessageList}</div>
                    </div>
                  )
                )}
                <Button
                  type="button"
                  onClick={() => SubmitData()}
                  disabled={disableBtn}
                  className="w-full md:w-fit"
                >
                  Save and Continue
                </Button>
              </Section>
            ) : null}
            <hr />
            {pageIndex > 0 ? (
              <a
                href="javascript:void(0);"
                className="cursor-pointer text-sky-700 underline font-bold md:text-sm text-md grid justify-items-end"
                onClick={() => goBackToPage()}
              >
                ←Go Back
              </a>
            ) : null}
          </div>
        )}
      </main>
    </Layout>
  );
};
export default WithLocation(Program);
