import React, { useState } from "react"
import { InputBox } from "../shared/InputBox"

const RadioButton = ({ label, onChange, ...inputProps }) => {
  const [isChecked, setIsChecked] = useState(inputProps.checkboxCheckedValue)
  const [val, setVal] = useState("")
  return (
    <InputBox
      label={label}
      checked={inputProps.checked}
      disabled={inputProps.disabled}
      error={inputProps.error}
    >
      <input
        onChange={event => {
          const { checked, value } = event.target
          setIsChecked(checked)
          setVal(value)
          onChange({ checked, value })
        }}
        className="sky-500 mr-2.5 scale-[1.375]"
        type="radio"
        checked={isChecked}
        value={val}
        {...inputProps}
      />
    </InputBox>
  )
}
export default RadioButton
