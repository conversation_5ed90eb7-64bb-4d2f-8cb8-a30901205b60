import React, { useEffect, useCallback } from "react";
import { Tooltip } from "react-tooltip";
import parse from "html-react-parser";

const CollegeTable = ({ CollegeList }) => {
  const [tooltipOne, setTooltipOne] = React.useState(false);
  const [tooltipTwo, setTooltipTwo] = React.useState(false);
  const [tooltipThree, setTooltipThree] = React.useState(false);
  const newObj = CollegeList?.sort((a, b) =>
    a.InstitutionName.localeCompare(b.InstitutionName)
  );
  const infoIcon = (
    <img
      className="text-black inline"
      src={`${process.env.IMG_URL}/info-icon.svg`}
      height={16}
      width={16}
      alt=""
    />
  );
  const escFunction = useCallback((event) => {
    if (event.key === "Escape") {
      document
        .querySelectorAll(".tipOne,.tipTwo,.tipThree")
        .forEach((el) => el.classList.remove("show"));
    }
  }, []);
  useEffect(() => {
    document.addEventListener("keydown", escFunction, false);
    return () => {
      document.removeEventListener("keydown", escFunction, false);
    };
  }, [escFunction]);
  return (
    <div
      className="overflow-auto"
      style={{
        maxHeight: 350,
      }}
    >
      <table className="table w-full text-md md:text-sm">
        <thead className="table-header-group bg-sky-50">
          <tr className="table-row">
            <th className="table-cell text-left p-2">
              Institution Name{" "}
              <button
                type="button"
                aria-label="Institution Name more information"
                aria-expanded={tooltipOne}
                aria-describedby={"Institution"}
                aria-controls="Institution"
                data-tip="Click the link to go directly to the institution’s student portal for additional information, requirements, and statuses specific to you and that school or program."
                data-for="Institution"
                currentitem="false"
                className="bg-transparent border-none cursor-pointer p-0"
              >
                {infoIcon}
              </button>
              <Tooltip
                globalEventOff="click"
                id="Institution"
                place="top"
                className="max-w-md tipOne"
                role="tooltip"
                afterShow={() => {
                  setTooltipOne(true);
                }}
                afterHide={() => {
                  setTooltipOne(false);
                }}
              />
            </th>
            <th className="table-cell text-left p-2">CSS Code</th>
            <th className="table-cell text-left p-2">Submission Date</th>
            <th className="table-cell text-left p-2">Correction Date</th>
            <th className="table-cell text-left p-2">
              Priority Filing Date{" "}
              <button
                type="button"
                aria-label="Filing Date more information"
                aria-expanded={tooltipTwo}
                aria-describedby={"FilingDate"}
                aria-controls="FilingDate"
                data-tip="Your application will be considered on time if you submit the
                CSS Profile by midnight Eastern Time of your earliest priority
                filing date. If a date is not displayed, check that college's or
                program's website or other materials for information."
                data-for="FilingDate"
                currentitem="false"
                className="bg-transparent border-none cursor-pointer p-0"
              >
                {infoIcon}
              </button>
              <Tooltip
                globalEventOff="click"
                id="FilingDate"
                place="top"
                className="max-w-md tipTwo"
                role="tooltip"
                afterShow={() => {
                  setTooltipTwo(true);
                }}
                afterHide={() => {
                  setTooltipTwo(false);
                }}
              />
            </th>
            <th className="table-cell text-left p-2">
              Priority Filing Date Information
            </th>
            <th className="table-cell text-left">
              Award Letter Date{" "}
              <button
                type="button"
                aria-label="Award letter date more information"
                aria-expanded={tooltipThree}
                aria-describedby={"AwardTooltip"}
                aria-controls="AwardTooltip"
                data-tip="The Award Letter Date is the approximate date that your colleges
                start to send financial aid award letters. You may not hear from
                a college before then. In general, students must be admitted to
                the college before awards are determined. If a date is not
                displayed, check that college’s website or other materials for
                information."
                data-for="AwardTooltip"
                currentitem="false"
                className="bg-transparent border-none cursor-pointer p-0"
              >
                {infoIcon}
              </button>
              <Tooltip
                globalEventOff="click"
                id="AwardTooltip"
                place="top"
                className="max-w-md tipThree"
                role="tooltip"
                afterShow={() => {
                  setTooltipThree(true);
                }}
                afterHide={() => {
                  setTooltipThree(false);
                }}
              />
            </th>
          </tr>
        </thead>
        <tbody className="table-row-group bg-yellow-50">
          {newObj.map((el) => (
            <tr key={el.CSSCode} className={`table-row `}>
              <td className="table-cell text-left p-2">
                {el.StudentPortalURL ? (
                  <a
                    className="aStyle"
                    href={el.StudentPortalURL}
                    target="_blank"
                    rel="noreferrer"
                  >
                    {parse(el.InstitutionName)}
                  </a>
                ) : (
                  parse(el.InstitutionName)
                )}
              </td>
              <td className="table-cell text-left p-2">{el.CSSCode}</td>
              <td className="table-cell text-left p-2">{el.SubmissionDate}</td>
              <td className="table-cell text-left p-2">{el.CorrectionDate}</td>
              <td className="table-cell text-left p-2">
                {el.PriorityFilingDate}
              </td>
              <td className="table-cell text-left p-2">
                {el.PriorityFilingDateURL ? (
                  <a
                    className="aStyle"
                    href={el.PriorityFilingDateURL}
                    target="_blank"
                    rel="noreferrer"
                  >
                    Click for details
                  </a>
                ) : null}
              </td>
              <td className="table-cell text-left p-2">{el.AwardLetterDate}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
export default CollegeTable;
