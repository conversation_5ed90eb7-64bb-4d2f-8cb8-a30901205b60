import React, { useEffect, useState } from "react";
import { navigate } from "gatsby";
import useUserStore from "../utils/storeApp";
import Layout from "../components/layout";
import parse from "html-react-parser";
import Helmet from "react-helmet";
import { Button } from "../components/Button";
import { ValidationError } from "../components/ValidationError";
import { ProcessingModal } from "../components/processingModal";
import WithLocation from "../components/withLocation";

const DataChecks = () => {
  const {
    isUserLogedIn,
    userForm,
    setUserForm,
    setpageFormUrl,
    setpageUrl,
    backPageButton,
    setBackPageButton,
    pageIndex,
    setPageIndex,
    backButtonMode,
    setBackButtonMode,
  } = useUserStore((state) => ({
    isUserLogedIn: state.isUserLogedIn,
    userForm: state.userForm,
    setUserForm: state.setUserForm,
    setpageFormUrl: state.setpageFormUrl,
    setpageUrl: state.setpageUrl,
    backPageButton: state.backPageButton,
    setBackPageButton: state.setBackPageButton,
    pageIndex: state.pageIndex,
    setPageIndex: state.setPageIndex,
    backButtonMode: state.backButtonMode,
    setBackButtonMode: state.setBackButtonMode,
  }));

  const [flag, SetFlag] = useState(false);
  const [msg, SetMsg] = useState("Error trying to fetch data.");
  const [formData, SetFormData] = useState({});
  const [showModal, setShowModal] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      if (!isUserLogedIn) {
        navigate("https://account.collegeboard.org");
      }
      setpageUrl("dataChecks");
      setBackPageButton([...backPageButton, userForm]);
      if (!backButtonMode) {
        setPageIndex(backPageButton.length - 1);
      }
      setShowModal(true);
      SetFormData(userForm);
      setShowModal(false);
    };
    fetchData();
  }, []);
  const goToNextPage = (pageName, menuName, multiple, MultipleParentName) => {
    const formUser = {
      Username: userForm.Username,
      UserId: userForm.UserId,
      AwardYear: userForm.AwardYear,
      ApplicationId: userForm.ApplicationId,
      OrgFormId: userForm.OrgFormId,
      FormId: userForm.FormId,
      MultipleParentName: MultipleParentName,
      MenuName: menuName,
      PageName: pageName,
      Multiple: multiple,
      Token: userForm.Token,
    };
    setBackButtonMode(false);
    setUserForm(formUser);
    setpageFormUrl(pageName);
    // if (pageName === "AcademicInfo3" || pageName === "AcademicInfo3a") {
    //   navigate("/school");
    // } else
    if (pageName === "AcademicInfo4") {
      navigate("/program");
    } else if (pageName === "Correction") {
      navigate("/payment");
    } else if (pageName === "ApplicationChecklist") {
      navigate("/checkList");
    } else if (multiple === "Y") {
      navigate("/multiple");
    } else {
      navigate("/formpage");
    }
  };
  const goBackToPage = () => {
    setBackButtonMode(true);
    const goBack = backPageButton[backPageButton.length - 2];
    setPageIndex(pageIndex - 1);
    setUserForm(goBack);
    setpageFormUrl(goBack.PageName);
    if (
      // goBack.PageName === "AcademicInfo3" ||
      goBack.PageName === "AcademicInfo3a"
    ) {
      navigate("/school");
    } else if (goBack.PageName === "AcademicInfo4") {
      navigate("/program");
    } else if (goBack.PageName === "Correction") {
      navigate("/payment");
    } else if (goBack.PageName === "AppCheckList") {
      navigate("/checkList");
    } else if (goBack.Multiple === "Y") {
      navigate("/multiple");
    } else {
      navigate("/formpage");
    }
  };
  const goToReview = async () => {
    navigate("/appReviewIntro");
  };
  return (
    <Layout>
      <Helmet
        htmlAttributes={{
          lang: "en",
        }}
        title={formData.PageHeading ? formData.PageHeading : "Data Checks"}
      />
      <div className="mx-auto space-y-5 bg-white p-7 md:max-w-lg md:p-11 md:shadow-md md:shadow-gray-500 mt-12 md:mt-6">
        {showModal ? <ProcessingModal /> : null}
        {flag && <ValidationError message={msg} />}

        {formData.PageHeading ? (
          <h1
            className="md:text-xl text-lg font-bold"
            id="GoBackBtn1"
            tabindex="-1"
          >
            {formData.PageHeading}
          </h1>
        ) : null}
        {formData.PageText ? <p>{parse(formData.PageText)}</p> : null}
        {formData?.FWDataCheckList?.map((elem) => (
          <div
            key={elem.PageName}
            className={`max-w-xl space-y-5 rounded-md border-2 p-3`}
          >
            <div className="space-y-3 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50  p-5 text-base">
              <strong>{"WARNING: "}</strong> {elem.Message}
            </div>
            <div>
              <span>Click </span>
              <button
                type="button"
                // id={`GoBackBtn${currentPosition + 1}`}
                className="text-center aStyle bg-transparent border-none cursor-pointer p-0"
                onClick={() => {
                  goToNextPage(
                    elem.PageName,
                    elem.MenuName,
                    elem.Multiple,
                    elem.MultipleParentName
                  );
                }}
              >
                {elem.PageNameHeading}
              </button>
              <span> to review and update.</span>
            </div>
          </div>
        ))}
        <Button
          onClick={() => {
            goToReview();
          }}
          className="w-full md:w-fit"
        >{`Continue`}</Button>
        <hr />
        {pageIndex > 0 ? (
          <p className="font-bold md:text-sm text-md grid justify-items-end">
            <a
              href="javascript:void(0);"
              className="aStyle"
              onClick={() => goBackToPage("ApplicationChecklist")}
            >
              ←Go Back
            </a>
          </p>
        ) : null}
      </div>
    </Layout>
  );
};

export default WithLocation(DataChecks);
