# CSS Profile Application - Next.js

This is a CSS Profile application built with Next.js, converted from Gatsby.

## 🚀 Quick Start

1. **Install dependencies**

   ```bash
   npm install
   ```

2. **Set up environment variables**

   Copy `.env.local.example` to `.env.local` and fill in your API URL:

   ```bash
   cp .env.local.example .env.local
   ```

3. **Start the development server**

   ```bash
   npm run dev
   ```

   Your site will be running at `http://localhost:3000`

4. **Build for production**

   ```bash
   npm run build
   npm start
   ```

## 🧐 What's inside?

A quick look at the top-level files and directories in this Next.js project:

    .
    ├── node_modules
    ├── pages/           # Next.js pages (routing)
    ├── src/
    │   ├── components/  # React components
    │   ├── styles/      # Global styles
    │   ├── utils/       # Utility functions and stores
    │   └── fakeDB/      # Mock data
    ├── public/          # Static assets
    ├── .env.local.example
    ├── next.config.js   # Next.js configuration
    ├── tailwind.config.js
    ├── postcss.config.js
    ├── package.json
    └── README.md

## 📁 Key Files

- **`pages/_app.js`**: Custom App component that wraps all pages
- **`pages/_document.js`**: Custom Document for HTML structure
- **`pages/index.js`**: Home page
- **`next.config.js`**: Next.js configuration with basePath and export settings
- **`src/components/layout.js`**: Main layout component
- **`src/utils/storeApp.js`**: Zustand store for state management

## 🔧 Technologies Used

- **Next.js 14** - React framework
- **React 18** - UI library
- **Tailwind CSS** - Utility-first CSS framework
- **Zustand** - State management
- **Axios** - HTTP client
- **React Idle Timer** - Idle detection
- **use-query-params** - URL query parameter management

## 🚀 Deployment

This app is configured for static export. To deploy:

1. Build the application:
   ```bash
   npm run build
   ```

2. The static files will be in the `out` directory

3. Deploy the `out` directory to your hosting provider

## 🔄 Migration from Gatsby

This application was migrated from Gatsby to Next.js with the following key changes:

- Replaced `gatsby` navigation with Next.js `useRouter`
- Converted GraphQL queries to static imports
- Updated `gatsby-plugin-*` configurations to Next.js equivalents
- Replaced `react-helmet` with Next.js `Head` component
- Updated routing from Gatsby's file-based routing to Next.js pages

## 📝 Environment Variables

- `API_URL` - The base URL for your API endpoints

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request
