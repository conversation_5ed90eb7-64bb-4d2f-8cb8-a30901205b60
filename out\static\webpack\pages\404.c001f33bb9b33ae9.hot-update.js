"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/404",{

/***/ "(pages-dir-browser)/./src/components/layout.js":
/*!**********************************!*\
  !*** ./src/components/layout.js ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! prop-types */ \"(pages-dir-browser)/./node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./header */ \"(pages-dir-browser)/./src/components/header.js\");\n/* harmony import */ var react_idle_timer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-idle-timer */ \"(pages-dir-browser)/./node_modules/react-idle-timer/dist/index.esm.js\");\n/* harmony import */ var _utils_storeApp__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/storeApp */ \"(pages-dir-browser)/./src/utils/storeApp.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Layout(param) {\n    let { children } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const timeout = 60000 * 30;\n    const [remaining, setRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(timeout);\n    const [elapsed, setElapsed] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [lastActive, setLastActive] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(+new Date());\n    const [isIdle, setIsIdle] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const handleOnActive = ()=>setIsIdle(false);\n    const { isUserLogedIn, userForm, setUserLogedIn, setUserForm, setpageFormUrl, setpageUrl, setBackPageButton, setPageIndex, appPool } = (0,_utils_storeApp__WEBPACK_IMPORTED_MODULE_5__[\"default\"])({\n        \"Layout.useUserStore\": (state)=>({\n                isUserLogedIn: state.isUserLogedIn,\n                userForm: state.userForm,\n                setUserLogedIn: state.setUserLogedIn,\n                setUserForm: state.setUserForm,\n                setpageFormUrl: state.setpageFormUrl,\n                setpageUrl: state.setpageUrl,\n                setBackPageButton: state.setBackPageButton,\n                setPageIndex: state.setPageIndex,\n                appPool: state.appPool\n            })\n    }[\"Layout.useUserStore\"]);\n    const handleOnIdle = ()=>{\n        let AppId = userForm.ApplicationId;\n        setUserLogedIn(false);\n        setUserForm({});\n        setpageFormUrl(\"\");\n        setpageUrl(\"\");\n        setBackPageButton([]);\n        setPageIndex(\"\");\n        window.location.href = \"https://account.collegeboard.org/login/logout?appId=\" + AppId + \"&DURL=https://student.collegeboard.org/css-financial-aid-profile\";\n    };\n    const { getRemainingTime, getLastActiveTime, getElapsedTime } = (0,react_idle_timer__WEBPACK_IMPORTED_MODULE_4__.useIdleTimer)({\n        timeout,\n        onActive: handleOnActive,\n        onIdle: handleOnIdle\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"Layout.useEffect\": ()=>{\n            setRemaining(getRemainingTime());\n            setLastActive(getLastActiveTime());\n            setElapsed(getElapsedTime());\n            const intervalId = setInterval({\n                \"Layout.useEffect.intervalId\": ()=>{\n                    setRemaining(getRemainingTime());\n                    setLastActive(getLastActiveTime());\n                    setElapsed(getElapsedTime());\n                }\n            }[\"Layout.useEffect.intervalId\"], 1000);\n            // Cleanup function to clear the interval\n            return ({\n                \"Layout.useEffect\": ()=>{\n                    clearInterval(intervalId);\n                }\n            })[\"Layout.useEffect\"];\n        //adobeAnalyticsPush();\n        }\n    }[\"Layout.useEffect\"], [\n        getRemainingTime,\n        getLastActiveTime,\n        getElapsedTime\n    ]);\n    const adobeAnalyticsPush = ()=>{\n        window.adobeDataLayer = window.adobeDataLayer || [];\n        window.adobeDataLayer.push({\n            page: {\n                flowCode: userForm.MenuName,\n                pageCode: userForm.PageName,\n                appViewCode: \"\"\n            }\n        });\n        try {\n            window._satellite.track(\"cbTrack.viewInDom\");\n        } catch (errSatelliteTrack) {}\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen font-sans text-gray-900 md:text-lg text-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\layout.js\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                id: \"scrollToTop\",\n                className: \"flex-1 w-full max-w-4xl px-4 py-8 mx-auto md:px-8 md:py-16\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\layout.js\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\layout.js\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(Layout, \"OWd8OGTf4PX6DktOuL5/+psvDsc=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _utils_storeApp__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        react_idle_timer__WEBPACK_IMPORTED_MODULE_4__.useIdleTimer\n    ];\n});\n_c = Layout;\nLayout.propTypes = {\n    children: (prop_types__WEBPACK_IMPORTED_MODULE_6___default().node).isRequired\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\nvar _c;\n$RefreshReg$(_c, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./src/components/layout.js\n"));

/***/ })

});