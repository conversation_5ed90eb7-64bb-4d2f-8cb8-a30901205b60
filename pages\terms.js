import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import Layout from "../src/components/layout";
import Head from "next/head";
import useUserStore from "../src/utils/storeApp";

const Terms = () => {
  const router = useRouter();
  const { isUserLogedIn } = useUserStore((state) => ({
    isUserLogedIn: state.isUserLogedIn,
  }));

  useEffect(() => {
    if (!isUserLogedIn) {
      window.location.href = "https://account.collegeboard.org";
    }
  }, [isUserLogedIn]);

  return (
    <Layout>
      <Head>
        <title>Terms and Conditions</title>
        <meta name="description" content="CSS Profile Terms and Conditions" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      <main className="mx-auto space-y-5 bg-white p-7 md:p-11 md:max-w-3xl md:shadow-md md:shadow-light-purple mt-12 md:mt-6">
        <div id="scrollToTop" tabIndex="-1"></div>
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Terms and Conditions</h1>
        <div className="prose max-w-none">
          <p>Terms and conditions content would go here...</p>
          {/* Add your terms content here */}
        </div>
      </main>
    </Layout>
  );
};

export default Terms;
