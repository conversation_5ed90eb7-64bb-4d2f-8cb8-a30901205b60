/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/404";
exports.ids = ["pages/404"];
exports.modules = {

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F404&preferredRegion=&absolutePagePath=.%2Fpages%5C404.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F404&preferredRegion=&absolutePagePath=.%2Fpages%5C404.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./pages/_document.js\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.js\");\n/* harmony import */ var _pages_404_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\404.js */ \"(pages-dir-node)/./pages/404.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_404_js__WEBPACK_IMPORTED_MODULE_5__]);\n_pages_404_js__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/404\",\n        pathname: \"/404\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_404_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtcm91dGUtbG9hZGVyL2luZGV4LmpzP2tpbmQ9UEFHRVMmcGFnZT0lMkY0MDQmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZwYWdlcyU1QzQwNC5qcyZhYnNvbHV0ZUFwcFBhdGg9cHJpdmF0ZS1uZXh0LXBhZ2VzJTJGX2FwcCZhYnNvbHV0ZURvY3VtZW50UGF0aD1wcml2YXRlLW5leHQtcGFnZXMlMkZfZG9jdW1lbnQmbWlkZGxld2FyZUNvbmZpZ0Jhc2U2ND1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBd0Y7QUFDaEM7QUFDRTtBQUMxRDtBQUN5RDtBQUNWO0FBQy9DO0FBQzRDO0FBQzVDO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQywwQ0FBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyx1QkFBdUIsd0VBQUssQ0FBQywwQ0FBUTtBQUNyQyx1QkFBdUIsd0VBQUssQ0FBQywwQ0FBUTtBQUNyQywyQkFBMkIsd0VBQUssQ0FBQywwQ0FBUTtBQUN6QyxlQUFlLHdFQUFLLENBQUMsMENBQVE7QUFDN0Isd0JBQXdCLHdFQUFLLENBQUMsMENBQVE7QUFDN0M7QUFDTyxnQ0FBZ0Msd0VBQUssQ0FBQywwQ0FBUTtBQUM5QyxnQ0FBZ0Msd0VBQUssQ0FBQywwQ0FBUTtBQUM5QyxpQ0FBaUMsd0VBQUssQ0FBQywwQ0FBUTtBQUMvQyxnQ0FBZ0Msd0VBQUssQ0FBQywwQ0FBUTtBQUM5QyxvQ0FBb0Msd0VBQUssQ0FBQywwQ0FBUTtBQUN6RDtBQUNPLHdCQUF3QixrR0FBZ0I7QUFDL0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLGFBQWEsOERBQVc7QUFDeEIsa0JBQWtCLG1FQUFnQjtBQUNsQyxLQUFLO0FBQ0wsWUFBWTtBQUNaLENBQUM7O0FBRUQsaUMiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQYWdlc1JvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9wYWdlcy9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSBhcHAgYW5kIGRvY3VtZW50IG1vZHVsZXMuXG5pbXBvcnQgKiBhcyBkb2N1bWVudCBmcm9tIFwicHJpdmF0ZS1uZXh0LXBhZ2VzL19kb2N1bWVudFwiO1xuaW1wb3J0ICogYXMgYXBwIGZyb20gXCJwcml2YXRlLW5leHQtcGFnZXMvX2FwcFwiO1xuLy8gSW1wb3J0IHRoZSB1c2VybGFuZCBjb2RlLlxuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIi4vcGFnZXNcXFxcNDA0LmpzXCI7XG4vLyBSZS1leHBvcnQgdGhlIGNvbXBvbmVudCAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgJ2RlZmF1bHQnKTtcbi8vIFJlLWV4cG9ydCBtZXRob2RzLlxuZXhwb3J0IGNvbnN0IGdldFN0YXRpY1Byb3BzID0gaG9pc3QodXNlcmxhbmQsICdnZXRTdGF0aWNQcm9wcycpO1xuZXhwb3J0IGNvbnN0IGdldFN0YXRpY1BhdGhzID0gaG9pc3QodXNlcmxhbmQsICdnZXRTdGF0aWNQYXRocycpO1xuZXhwb3J0IGNvbnN0IGdldFNlcnZlclNpZGVQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCAnZ2V0U2VydmVyU2lkZVByb3BzJyk7XG5leHBvcnQgY29uc3QgY29uZmlnID0gaG9pc3QodXNlcmxhbmQsICdjb25maWcnKTtcbmV4cG9ydCBjb25zdCByZXBvcnRXZWJWaXRhbHMgPSBob2lzdCh1c2VybGFuZCwgJ3JlcG9ydFdlYlZpdGFscycpO1xuLy8gUmUtZXhwb3J0IGxlZ2FjeSBtZXRob2RzLlxuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFN0YXRpY1Byb3BzID0gaG9pc3QodXNlcmxhbmQsICd1bnN0YWJsZV9nZXRTdGF0aWNQcm9wcycpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFN0YXRpY1BhdGhzID0gaG9pc3QodXNlcmxhbmQsICd1bnN0YWJsZV9nZXRTdGF0aWNQYXRocycpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFN0YXRpY1BhcmFtcyA9IGhvaXN0KHVzZXJsYW5kLCAndW5zdGFibGVfZ2V0U3RhdGljUGFyYW1zJyk7XG5leHBvcnQgY29uc3QgdW5zdGFibGVfZ2V0U2VydmVyUHJvcHMgPSBob2lzdCh1c2VybGFuZCwgJ3Vuc3RhYmxlX2dldFNlcnZlclByb3BzJyk7XG5leHBvcnQgY29uc3QgdW5zdGFibGVfZ2V0U2VydmVyU2lkZVByb3BzID0gaG9pc3QodXNlcmxhbmQsICd1bnN0YWJsZV9nZXRTZXJ2ZXJTaWRlUHJvcHMnKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTLFxuICAgICAgICBwYWdlOiBcIi80MDRcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiLzQwNFwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6ICcnLFxuICAgICAgICBmaWxlbmFtZTogJydcbiAgICB9LFxuICAgIGNvbXBvbmVudHM6IHtcbiAgICAgICAgLy8gZGVmYXVsdCBleHBvcnQgbWlnaHQgbm90IGV4aXN0IHdoZW4gb3B0aW1pemVkIGZvciBkYXRhIG9ubHlcbiAgICAgICAgQXBwOiBhcHAuZGVmYXVsdCxcbiAgICAgICAgRG9jdW1lbnQ6IGRvY3VtZW50LmRlZmF1bHRcbiAgICB9LFxuICAgIHVzZXJsYW5kXG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFnZXMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F404&preferredRegion=&absolutePagePath=.%2Fpages%5C404.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/404.js":
/*!**********************!*\
  !*** ./pages/404.js ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_components_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../src/components/layout */ \"(pages-dir-node)/./src/components/layout.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _src_components_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../src/components/Button */ \"(pages-dir-node)/./src/components/Button/index.jsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_components_layout__WEBPACK_IMPORTED_MODULE_2__]);\n_src_components_layout__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction NotFound() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Page Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Page not found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"mx-auto space-y-5 bg-white p-7 md:p-11 md:max-w-lg md:shadow-md md:shadow-light-purple mt-12 md:mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-700 mb-4\",\n                            children: \"Page Not Found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-8\",\n                            children: \"The page you are looking for doesn't exist or has been moved.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>router.push(\"/\"),\n                            className: \"w-full md:w-fit\",\n                            children: \"Go Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/404.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _src_styles_global_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../src/styles/global.css */ \"(pages-dir-node)/./src/styles/global.css\");\n/* harmony import */ var _src_styles_global_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_src_styles_global_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var use_query_params__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! use-query-params */ \"use-query-params\");\n/* harmony import */ var use_query_params__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(use_query_params__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_query_params__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-query-params */ \"next-query-params\");\n/* harmony import */ var next_query_params__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_query_params__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(use_query_params__WEBPACK_IMPORTED_MODULE_2__.QueryParamProvider, {\n        adapter: next_query_params__WEBPACK_IMPORTED_MODULE_3__.NextAdapter,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_app.js\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_app.js\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUFpQztBQUNvQjtBQUNOO0FBRWhDLFNBQVNFLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUU7SUFDbEQscUJBQ0UsOERBQUNKLGdFQUFrQkE7UUFBQ0ssU0FBU0osMERBQVdBO2tCQUN0Qyw0RUFBQ0U7WUFBVyxHQUFHQyxTQUFTOzs7Ozs7Ozs7OztBQUc5QiIsInNvdXJjZXMiOlsiQzpcXENTaGFycFxcR2l0SHViXFxQcm9maWxlQ1NTXFxDbGllbnRBcHBcXHBhZ2VzXFxfYXBwLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi4vc3JjL3N0eWxlcy9nbG9iYWwuY3NzJ1xuaW1wb3J0IHsgUXVlcnlQYXJhbVByb3ZpZGVyIH0gZnJvbSAndXNlLXF1ZXJ5LXBhcmFtcydcbmltcG9ydCB7IE5leHRBZGFwdGVyIH0gZnJvbSAnbmV4dC1xdWVyeS1wYXJhbXMnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH0pIHtcbiAgcmV0dXJuIChcbiAgICA8UXVlcnlQYXJhbVByb3ZpZGVyIGFkYXB0ZXI9e05leHRBZGFwdGVyfT5cbiAgICAgIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz5cbiAgICA8L1F1ZXJ5UGFyYW1Qcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlF1ZXJ5UGFyYW1Qcm92aWRlciIsIk5leHRBZGFwdGVyIiwiQXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIiwiYWRhcHRlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_document.js":
/*!****************************!*\
  !*** ./pages/_document.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"(pages-dir-node)/./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_document.js\",\n                        lineNumber: 7,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_document.js\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#000000\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_document.js\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"CSS Profile Application\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_document.js\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_document.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_document.js\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_document.js\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_document.js\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_document.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19kb2N1bWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNEQ7QUFFN0MsU0FBU0k7SUFDdEIscUJBQ0UsOERBQUNKLCtDQUFJQTtRQUFDSyxNQUFLOzswQkFDVCw4REFBQ0osK0NBQUlBOztrQ0FDSCw4REFBQ0s7d0JBQUtDLFNBQVE7Ozs7OztrQ0FDZCw4REFBQ0M7d0JBQUtDLEtBQUk7d0JBQU9DLE1BQUs7Ozs7OztrQ0FDdEIsOERBQUNKO3dCQUFLSyxNQUFLO3dCQUFjQyxTQUFROzs7Ozs7a0NBQ2pDLDhEQUFDTjt3QkFBS0ssTUFBSzt3QkFBY0MsU0FBUTs7Ozs7Ozs7Ozs7OzBCQUVuQyw4REFBQ0M7O2tDQUNDLDhEQUFDWCwrQ0FBSUE7Ozs7O2tDQUNMLDhEQUFDQyxxREFBVUE7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSW5CIiwic291cmNlcyI6WyJDOlxcQ1NoYXJwXFxHaXRIdWJcXFByb2ZpbGVDU1NcXENsaWVudEFwcFxccGFnZXNcXF9kb2N1bWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIdG1sLCBIZWFkLCBNYWluLCBOZXh0U2NyaXB0IH0gZnJvbSAnbmV4dC9kb2N1bWVudCdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRG9jdW1lbnQoKSB7XG4gIHJldHVybiAoXG4gICAgPEh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8SGVhZD5cbiAgICAgICAgPG1ldGEgY2hhclNldD1cInV0Zi04XCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIGhyZWY9XCIvZmF2aWNvbi5pY29cIiAvPlxuICAgICAgICA8bWV0YSBuYW1lPVwidGhlbWUtY29sb3JcIiBjb250ZW50PVwiIzAwMDAwMFwiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJkZXNjcmlwdGlvblwiIGNvbnRlbnQ9XCJDU1MgUHJvZmlsZSBBcHBsaWNhdGlvblwiIC8+XG4gICAgICA8L0hlYWQ+XG4gICAgICA8Ym9keT5cbiAgICAgICAgPE1haW4gLz5cbiAgICAgICAgPE5leHRTY3JpcHQgLz5cbiAgICAgIDwvYm9keT5cbiAgICA8L0h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJIdG1sIiwiSGVhZCIsIk1haW4iLCJOZXh0U2NyaXB0IiwiRG9jdW1lbnQiLCJsYW5nIiwibWV0YSIsImNoYXJTZXQiLCJsaW5rIiwicmVsIiwiaHJlZiIsIm5hbWUiLCJjb250ZW50IiwiYm9keSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_document.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/Button/index.jsx":
/*!*****************************************!*\
  !*** ./src/components/Button/index.jsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _shared_buttons_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared/buttons.js */ \"(pages-dir-node)/./src/components/shared/buttons.js\");\n/* harmony import */ var _shared_inputs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../shared/inputs */ \"(pages-dir-node)/./src/components/shared/inputs.js\");\n\n\n\n\nfunction Button({ children, className = \"\", disabled = false, variant = \"primary\", ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        disabled: disabled,\n        className: `${(0,_shared_buttons_js__WEBPACK_IMPORTED_MODULE_2__.buttonClasses)({\n            variant\n        })} ${_shared_inputs__WEBPACK_IMPORTED_MODULE_3__.focusWithinStyles} ${className}`,\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\Button\\\\index.jsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9jb21wb25lbnRzL0J1dHRvbi9pbmRleC5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBeUI7QUFDMkI7QUFDQTtBQUU3QyxTQUFTRyxPQUFPLEVBQ3JCQyxRQUFRLEVBQ1JDLFlBQVksRUFBRSxFQUNkQyxXQUFXLEtBQUssRUFDaEJDLFVBQVUsU0FBUyxFQUNuQixHQUFHQyxPQUNKO0lBQ0MscUJBQ0UsOERBQUNDO1FBQ0NILFVBQVVBO1FBQ1ZELFdBQVcsR0FBR0osaUVBQWFBLENBQUM7WUFDMUJNO1FBQ0YsR0FBRyxDQUFDLEVBQUVMLDZEQUFpQkEsQ0FBQyxDQUFDLEVBQUVHLFdBQVc7UUFDckMsR0FBR0csS0FBSztrQkFFUko7Ozs7OztBQUdQIiwic291cmNlcyI6WyJDOlxcQ1NoYXJwXFxHaXRIdWJcXFByb2ZpbGVDU1NcXENsaWVudEFwcFxcc3JjXFxjb21wb25lbnRzXFxCdXR0b25cXGluZGV4LmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCJcclxuaW1wb3J0IHsgYnV0dG9uQ2xhc3NlcyB9IGZyb20gXCIuLi9zaGFyZWQvYnV0dG9ucy5qc1wiXHJcbmltcG9ydCB7IGZvY3VzV2l0aGluU3R5bGVzIH0gZnJvbSBcIi4uL3NoYXJlZC9pbnB1dHNcIlxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIEJ1dHRvbih7XHJcbiAgY2hpbGRyZW4sXHJcbiAgY2xhc3NOYW1lID0gXCJcIixcclxuICBkaXNhYmxlZCA9IGZhbHNlLFxyXG4gIHZhcmlhbnQgPSBcInByaW1hcnlcIixcclxuICAuLi5wcm9wc1xyXG59KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxidXR0b25cclxuICAgICAgZGlzYWJsZWQ9e2Rpc2FibGVkfVxyXG4gICAgICBjbGFzc05hbWU9e2Ake2J1dHRvbkNsYXNzZXMoe1xyXG4gICAgICAgIHZhcmlhbnQsXHJcbiAgICAgIH0pfSAke2ZvY3VzV2l0aGluU3R5bGVzfSAke2NsYXNzTmFtZX1gfVxyXG4gICAgICB7Li4ucHJvcHN9XHJcbiAgICA+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvYnV0dG9uPlxyXG4gIClcclxufVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJidXR0b25DbGFzc2VzIiwiZm9jdXNXaXRoaW5TdHlsZXMiLCJCdXR0b24iLCJjaGlsZHJlbiIsImNsYXNzTmFtZSIsImRpc2FibGVkIiwidmFyaWFudCIsInByb3BzIiwiYnV0dG9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/Button/index.jsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/header.js":
/*!**********************************!*\
  !*** ./src/components/header.js ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_storeApp__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/storeApp */ \"(pages-dir-node)/./src/utils/storeApp.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_utils_storeApp__WEBPACK_IMPORTED_MODULE_4__]);\n_utils_storeApp__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nfunction Header({ ...props }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [skip, SetSkip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)( false ? 0 : \"\");\n    const [isFocused, setIsFocused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isOpen, SetIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { isUserLogedIn, userForm, setUserLogedIn, setUserForm, setpageFormUrl, setpageUrl, setBackPageButton, setPageIndex, setMoneyObj, hideLogin } = (0,_utils_storeApp__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n        \"Header.useUserStore\": (state)=>({\n                isUserLogedIn: state.isUserLogedIn,\n                userForm: state.userForm,\n                setUserLogedIn: state.setUserLogedIn,\n                setUserForm: state.setUserForm,\n                setpageFormUrl: state.setpageFormUrl,\n                setpageUrl: state.setpageUrl,\n                setBackPageButton: state.setBackPageButton,\n                setPageIndex: state.setPageIndex,\n                setMoneyObj: state.setMoneyObj,\n                hideLogin: state.hideLogin\n            })\n    }[\"Header.useUserStore\"]);\n    const handleClose = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"Header.useCallback[handleClose]\": (e)=>{\n            let clickedInsideMenu = e.target.closest(\"#menu\") === undefined;\n            if (!clickedInsideMenu) {\n                SetIsOpen(false);\n            }\n        }\n    }[\"Header.useCallback[handleClose]\"], []);\n    const handleLogin = ()=>{\n        let AppId = userForm.ApplicationId;\n        setUserLogedIn(false);\n        setUserForm({});\n        setBackPageButton([]);\n        setMoneyObj([]);\n        setpageFormUrl(\"\");\n        setPageIndex(\"\");\n        setpageUrl(\"\");\n        window.location.href = \"https://account.collegeboard.org/login/logout?appId=7&DURL=https://student.collegeboard.org/css-financial-aid-profile\";\n    };\n    const activeStyles = {\n        color: \"#e9d5ff\",\n        textDecorationLine: \"underline\",\n        textUnderlineOffset: \"5px\"\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            document.addEventListener(\"click\", handleClose, true);\n            return ({\n                \"Header.useEffect\": ()=>{\n                    document.removeEventListener(\"click\", handleClose, true);\n                }\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], [\n        handleClose\n    ]);\n    // Static assets for Next.js\n    const data = {\n        logo: {\n            publicURL: \"/logo.svg\"\n        },\n        profile: {\n            publicURL: \"/profile.jpg\"\n        }\n    };\n    const goto = (url)=>{\n        router.push(url);\n    };\n    const handleSkip = ()=>{\n        let btn = document.getElementById(\"GoBackBtn1\");\n        if (btn) {\n            btn.focus();\n            btn.checked = true;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed z-50 flex mb-8 w-full flex-row flex-nowrap items-center justify-center bg-black text-sm font-medium text-white md:text-lg\",\n                ...props,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        className: \"text-sm\",\n                        href: \"#nolinkID\",\n                        onClick: handleSkip,\n                        onFocus: ()=>setIsFocused(true),\n                        onBlur: ()=>setIsFocused(false),\n                        children: isFocused ? \"Skip to main content\" : null\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        className: \"pl-2 hidden grow pr-4 md:visible md:flex hover:text-gray-300\",\n                        href: \"https://www.collegeboard.org\",\n                        target: \"_blank\",\n                        id: \"CollegeBoardId\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex flex-row flex-nowrap items-center justify-center space-x-2 pr-1 hover:w-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: process.env.IMG_URL + data.logo.publicURL,\n                                    alt: \"College Board\",\n                                    className: \"inline\",\n                                    width: 32,\n                                    height: 32\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"CollegeBoard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex w-full flex-row flex-nowrap items-center justify-start bg-program-core-higher-ed p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"CSS Profile\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: process.env.IMG_URL + data.profile.publicURL,\n                                    alt: \"CSS Profile\",\n                                    className: \"inline\",\n                                    width: 116,\n                                    height: 36\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"ml-auto md:hidden\",\n                                onClick: ()=>SetIsOpen(true),\n                                children: \"Menu\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-auto hidden grow flex-row flex-nowrap items-center justify-end space-x-9 md:visible md:flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://privacy.collegeboard.org\",\n                                        target: \"_blank\",\n                                        className: `hover:text-purple-300`,\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://CSSAPIProfile.collegeboard.org/contact-us\",\n                                        target: \"_blank\",\n                                        className: `hover:text-purple-300`,\n                                        children: \"Contact Us\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this),\n                                    hideLogin ? \"\" : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/dashboard\",\n                                                legacyBehavior: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    className: `hover:text-purple-300 cursor-pointer`,\n                                                    children: \"Dashboard\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this),\n                                            isUserLogedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#nolinkID\",\n                                                className: `hover:text-purple-300 cursor-pointer`,\n                                                onClick: ()=>handleLogin(),\n                                                children: \"Log out\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/login\",\n                                                legacyBehavior: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    className: `hover:text-purple-300 cursor-pointer`,\n                                                    children: \"Login\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${isOpen ? \"block\" : \"hidden\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-0 left-0 right-0 top-0 z-10 bg-black opacity-20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"menu\",\n                        className: \"duration-600 fixed top-0 right-0 z-30 z-20 h-full w-64 transform overflow-auto bg-white bg-white text-black transition-all ease-in-out md:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between p-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold\",\n                                        children: \"Menu\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-20 cursor-pointer font-bold text-sky-700 underline\",\n                                        onClick: ()=>SetIsOpen(false),\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"flex flex-col gap-y-3 p-5 font-bold\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://privacy.collegeboard.org\",\n                                            target: \"_blank\",\n                                            className: `text-program-core-higher-ed underline underline-offset-2`,\n                                            children: \"Privacy Policy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 193,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>goto(\"/dashboard\"),\n                                            className: `text-program-core-higher-ed underline underline-offset-2 cursor-pointer`,\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://CSSAPIProfile.collegeboard.org/contact-us\",\n                                            target: \"_blank\",\n                                            className: `text-program-core-higher-ed underline underline-offset-2`,\n                                            children: \"Contact Us\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: isUserLogedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#nolinkID\",\n                                            className: \"cursor-pointer text-program-core-higher-ed underline underline-offset-2\",\n                                            onClick: ()=>handleLogin(),\n                                            children: \"Log out\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>goto(\"/login\"),\n                                            className: `text-program-core-higher-ed underline underline-offset-2`,\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 192,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/header.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/layout.js":
/*!**********************************!*\
  !*** ./src/components/layout.js ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prop-types */ \"prop-types\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./header */ \"(pages-dir-node)/./src/components/header.js\");\n/* harmony import */ var react_idle_timer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-idle-timer */ \"react-idle-timer\");\n/* harmony import */ var react_idle_timer__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_idle_timer__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _utils_storeApp__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/storeApp */ \"(pages-dir-node)/./src/utils/storeApp.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_header__WEBPACK_IMPORTED_MODULE_4__, _utils_storeApp__WEBPACK_IMPORTED_MODULE_6__]);\n([_header__WEBPACK_IMPORTED_MODULE_4__, _utils_storeApp__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nfunction Layout({ children }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const timeout = 60000 * 30;\n    const [remaining, setRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(timeout);\n    const [elapsed, setElapsed] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [lastActive, setLastActive] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(+new Date());\n    const [isIdle, setIsIdle] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const handleOnActive = ()=>setIsIdle(false);\n    const { isUserLogedIn, userForm, setUserLogedIn, setUserForm, setpageFormUrl, setpageUrl, setBackPageButton, setPageIndex, appPool } = (0,_utils_storeApp__WEBPACK_IMPORTED_MODULE_6__[\"default\"])({\n        \"Layout.useUserStore\": (state)=>({\n                isUserLogedIn: state.isUserLogedIn,\n                userForm: state.userForm,\n                setUserLogedIn: state.setUserLogedIn,\n                setUserForm: state.setUserForm,\n                setpageFormUrl: state.setpageFormUrl,\n                setpageUrl: state.setpageUrl,\n                setBackPageButton: state.setBackPageButton,\n                setPageIndex: state.setPageIndex,\n                appPool: state.appPool\n            })\n    }[\"Layout.useUserStore\"]);\n    const handleOnIdle = ()=>{\n        let AppId = userForm.ApplicationId;\n        setUserLogedIn(false);\n        setUserForm({});\n        setpageFormUrl(\"\");\n        setpageUrl(\"\");\n        setBackPageButton([]);\n        setPageIndex(\"\");\n        window.location.href = \"https://account.collegeboard.org/login/logout?appId=\" + AppId + \"&DURL=https://student.collegeboard.org/css-financial-aid-profile\";\n    };\n    const { getRemainingTime, getLastActiveTime, getElapsedTime } = (0,react_idle_timer__WEBPACK_IMPORTED_MODULE_5__.useIdleTimer)({\n        timeout,\n        onActive: handleOnActive,\n        onIdle: handleOnIdle\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Layout.useEffect\": ()=>{\n            setRemaining(getRemainingTime());\n            setLastActive(getLastActiveTime());\n            setElapsed(getElapsedTime());\n            const intervalId = setInterval({\n                \"Layout.useEffect.intervalId\": ()=>{\n                    setRemaining(getRemainingTime());\n                    setLastActive(getLastActiveTime());\n                    setElapsed(getElapsedTime());\n                }\n            }[\"Layout.useEffect.intervalId\"], 1000);\n            // Cleanup function to clear the interval\n            return ({\n                \"Layout.useEffect\": ()=>{\n                    clearInterval(intervalId);\n                }\n            })[\"Layout.useEffect\"];\n        //adobeAnalyticsPush();\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }\n    }[\"Layout.useEffect\"], []);\n    const adobeAnalyticsPush = ()=>{\n        window.adobeDataLayer = window.adobeDataLayer || [];\n        window.adobeDataLayer.push({\n            page: {\n                flowCode: userForm.MenuName,\n                pageCode: userForm.PageName,\n                appViewCode: \"\"\n            }\n        });\n        try {\n            window._satellite.track(\"cbTrack.viewInDom\");\n        } catch (errSatelliteTrack) {}\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen font-sans text-gray-900 md:text-lg text-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\layout.js\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                id: \"scrollToTop\",\n                className: \"flex-1 w-full max-w-4xl px-4 py-8 mx-auto md:px-8 md:py-16\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\layout.js\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\layout.js\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, this);\n}\nLayout.propTypes = {\n    children: (prop_types__WEBPACK_IMPORTED_MODULE_1___default().node).isRequired\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/layout.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/shared/buttons.js":
/*!******************************************!*\
  !*** ./src/components/shared/buttons.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buttonClasses: () => (/* binding */ buttonClasses)\n/* harmony export */ });\nconst primaryButtonColors = \"text-white bg-sky-700 hover:bg-sky-600 disabled:border-gray-500 disabled:bg-gray-400\";\nconst secondaryButtonColors = \"text-black bg-gray-300 hover:bg-gray-400 disabled:border-gray-500 disabled:bg-gray-100 disabled:text-gray-500\";\nconst greenButtonColors = \"bg-green-100 text-green-800 hover:bg-green-200\";\nconst whiteButtonColors = \"hover:bg-gray-100\";\nconst baseButtonClasses = `py-3 px-6 border-2 border-b-4 border-black rounded-sm text-center font-bold\nnot(:disabled):hover:cursor-pointer not(:disabled):active:outline active:outline-offset-2 active:outline-4 active:outline-yellow-600`;\nfunction buttonClasses({ variant }) {\n    switch(variant){\n        case \"primary\":\n            return `${baseButtonClasses} ${primaryButtonColors}`;\n        case \"secondary\":\n            return `${baseButtonClasses} ${secondaryButtonColors}`;\n        case \"green\":\n            return `${baseButtonClasses} ${greenButtonColors}`;\n        case \"white\":\n            return `${baseButtonClasses} ${whiteButtonColors}`;\n        default:\n            return baseButtonClasses;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/shared/buttons.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/shared/inputs.js":
/*!*****************************************!*\
  !*** ./src/components/shared/inputs.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   borderStyles: () => (/* binding */ borderStyles),\n/* harmony export */   focusStyles: () => (/* binding */ focusStyles),\n/* harmony export */   focusWithinStyles: () => (/* binding */ focusWithinStyles),\n/* harmony export */   inputStyles: () => (/* binding */ inputStyles)\n/* harmony export */ });\nconst borderStyles = (error)=>error ? \"border-red-600\" : \"border-gray-500\";\nconst inputStyles = \"w-full appearance-none border-2 py-3.5 px-2.5 rounded-none bg-white shadow-inner\";\nconst focusStyles = \"focus:outline focus:outline-offset-2 focus:outline-4 focus:outline-sky-600\";\nconst focusWithinStyles = \"focus-within:outline focus-within:outline-offset-2 focus-within:outline-4 focus-within:outline-sky-600\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9jb21wb25lbnRzL3NoYXJlZC9pbnB1dHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFPLE1BQU1BLGVBQWUsQ0FBQ0MsUUFBV0EsUUFBUSxtQkFBbUIsa0JBQW1CO0FBRS9FLE1BQU1DLGNBQ1gsbUZBQW1GO0FBRTlFLE1BQU1DLGNBQ1gsNkVBQTZFO0FBRXhFLE1BQU1DLG9CQUNYLHlHQUF5RyIsInNvdXJjZXMiOlsiQzpcXENTaGFycFxcR2l0SHViXFxQcm9maWxlQ1NTXFxDbGllbnRBcHBcXHNyY1xcY29tcG9uZW50c1xcc2hhcmVkXFxpbnB1dHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGJvcmRlclN0eWxlcyA9IChlcnJvcikgPT4gKGVycm9yID8gXCJib3JkZXItcmVkLTYwMFwiIDogXCJib3JkZXItZ3JheS01MDBcIik7XHJcblxyXG5leHBvcnQgY29uc3QgaW5wdXRTdHlsZXMgPVxyXG4gIFwidy1mdWxsIGFwcGVhcmFuY2Utbm9uZSBib3JkZXItMiBweS0zLjUgcHgtMi41IHJvdW5kZWQtbm9uZSBiZy13aGl0ZSBzaGFkb3ctaW5uZXJcIjtcclxuXHJcbmV4cG9ydCBjb25zdCBmb2N1c1N0eWxlcyA9XHJcbiAgXCJmb2N1czpvdXRsaW5lIGZvY3VzOm91dGxpbmUtb2Zmc2V0LTIgZm9jdXM6b3V0bGluZS00IGZvY3VzOm91dGxpbmUtc2t5LTYwMFwiO1xyXG5cclxuZXhwb3J0IGNvbnN0IGZvY3VzV2l0aGluU3R5bGVzID1cclxuICBcImZvY3VzLXdpdGhpbjpvdXRsaW5lIGZvY3VzLXdpdGhpbjpvdXRsaW5lLW9mZnNldC0yIGZvY3VzLXdpdGhpbjpvdXRsaW5lLTQgZm9jdXMtd2l0aGluOm91dGxpbmUtc2t5LTYwMFwiO1xyXG4iXSwibmFtZXMiOlsiYm9yZGVyU3R5bGVzIiwiZXJyb3IiLCJpbnB1dFN0eWxlcyIsImZvY3VzU3R5bGVzIiwiZm9jdXNXaXRoaW5TdHlsZXMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/shared/inputs.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/styles/global.css":
/*!*******************************!*\
  !*** ./src/styles/global.css ***!
  \*******************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./src/utils/storeApp.js":
/*!*******************************!*\
  !*** ./src/utils/storeApp.js ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"zustand\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"zustand/middleware\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([zustand__WEBPACK_IMPORTED_MODULE_0__, zustand_middleware__WEBPACK_IMPORTED_MODULE_1__]);\n([zustand__WEBPACK_IMPORTED_MODULE_0__, zustand_middleware__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst userStore = (set)=>({\n        isUserLogedIn: false,\n        formURL: \"\",\n        appPool: \"\",\n        currentPageURL: \"\",\n        userForm: {},\n        backPageButton: [],\n        collegeObj: [],\n        moneyObj: [],\n        userObject: {},\n        pageIndex: \"\",\n        backButtonMode: false,\n        addCollegeFlag: false,\n        dollarFormat: false,\n        hideLogin: false,\n        SSNValue: \"\",\n        AppReviewNr: 0,\n        setAppReviewNr: (val)=>{\n            set((state)=>({\n                    AppReviewNr: val\n                }));\n        },\n        setHideLogin: (val)=>{\n            set((state)=>({\n                    hideLogin: val\n                }));\n        },\n        setSSNValue: (val)=>{\n            set((state)=>({\n                    SSNValue: val\n                }));\n        },\n        setDollarFormat: (flag)=>{\n            set((state)=>({\n                    dollarFormat: flag\n                }));\n        },\n        setMoneyObj: (obj)=>{\n            set((state)=>({\n                    moneyObj: obj\n                }));\n        },\n        setCollegeObj: (obj)=>{\n            set((state)=>({\n                    collegeObj: obj\n                }));\n        },\n        setAaddCollegeFlag: (flg)=>{\n            set((state)=>({\n                    addCollegeFlag: flg\n                }));\n        },\n        setAppPool: (site)=>{\n            set((state)=>({\n                    appPool: site\n                }));\n        },\n        setBackButtonMode: (mode)=>{\n            set((state)=>({\n                    backButtonMode: mode\n                }));\n        },\n        setPageIndex: (idx)=>{\n            set((state)=>({\n                    pageIndex: idx\n                }));\n        },\n        setBackPageButton: (obj)=>{\n            set((state)=>({\n                    backPageButton: obj.length === 0 ? [] : obj\n                }));\n        },\n        setUserLogedIn: (flag)=>{\n            set((state)=>({\n                    isUserLogedIn: flag\n                }));\n        },\n        setpageUrl: (page)=>{\n            set((state)=>({\n                    currentPageURL: page\n                }));\n        },\n        setpageFormUrl: (page)=>{\n            set((state)=>({\n                    formURL: page\n                }));\n        },\n        setUserForm: (form)=>{\n            set((state)=>({\n                    userForm: form\n                }));\n        },\n        updateUserForm: (key, val)=>{\n            set((state)=>({\n                    userForm: {\n                        ...state.userForm,\n                        [key]: val\n                    }\n                }));\n        }\n    });\nconst useUserStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.devtools)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)(userStore, {\n    name: \"user\"\n})));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useUserStore);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy91dGlscy9zdG9yZUFwcC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBaUM7QUFFc0I7QUFFdkQsTUFBTUcsWUFBWSxDQUFDQyxNQUFTO1FBQzFCQyxlQUFlO1FBQ2ZDLFNBQVM7UUFDVEMsU0FBUztRQUNUQyxnQkFBZ0I7UUFDaEJDLFVBQVUsQ0FBQztRQUNYQyxnQkFBZ0IsRUFBRTtRQUNsQkMsWUFBWSxFQUFFO1FBQ2RDLFVBQVUsRUFBRTtRQUNaQyxZQUFZLENBQUM7UUFDYkMsV0FBVztRQUNYQyxnQkFBZ0I7UUFDaEJDLGdCQUFnQjtRQUNoQkMsY0FBYztRQUNkQyxXQUFXO1FBQ1hDLFVBQVU7UUFDVkMsYUFBYTtRQUNiQyxnQkFBZ0IsQ0FBQ0M7WUFDZmxCLElBQUksQ0FBQ21CLFFBQVc7b0JBQ2RILGFBQWFFO2dCQUNmO1FBQ0Y7UUFDQUUsY0FBYyxDQUFDRjtZQUNibEIsSUFBSSxDQUFDbUIsUUFBVztvQkFDZEwsV0FBV0k7Z0JBQ2I7UUFDRjtRQUNBRyxhQUFhLENBQUNIO1lBQ1psQixJQUFJLENBQUNtQixRQUFXO29CQUNkSixVQUFVRztnQkFDWjtRQUNGO1FBQ0FJLGlCQUFpQixDQUFDQztZQUNoQnZCLElBQUksQ0FBQ21CLFFBQVc7b0JBQ2ROLGNBQWNVO2dCQUNoQjtRQUNGO1FBQ0FDLGFBQWEsQ0FBQ0M7WUFDWnpCLElBQUksQ0FBQ21CLFFBQVc7b0JBQ2RYLFVBQVVpQjtnQkFDWjtRQUNGO1FBQ0FDLGVBQWUsQ0FBQ0Q7WUFDZHpCLElBQUksQ0FBQ21CLFFBQVc7b0JBQ2RaLFlBQVlrQjtnQkFDZDtRQUNGO1FBQ0FFLG9CQUFvQixDQUFDQztZQUNuQjVCLElBQUksQ0FBQ21CLFFBQVc7b0JBQ2RQLGdCQUFnQmdCO2dCQUNsQjtRQUNGO1FBQ0FDLFlBQVksQ0FBQ0M7WUFDWDlCLElBQUksQ0FBQ21CLFFBQVc7b0JBQ2RoQixTQUFTMkI7Z0JBQ1g7UUFDRjtRQUNBQyxtQkFBbUIsQ0FBQ0M7WUFDbEJoQyxJQUFJLENBQUNtQixRQUFXO29CQUNkUixnQkFBZ0JxQjtnQkFDbEI7UUFDRjtRQUNBQyxjQUFjLENBQUNDO1lBQ2JsQyxJQUFJLENBQUNtQixRQUFXO29CQUNkVCxXQUFXd0I7Z0JBQ2I7UUFDRjtRQUNBQyxtQkFBbUIsQ0FBQ1Y7WUFDbEJ6QixJQUFJLENBQUNtQixRQUFXO29CQUNkYixnQkFBZ0JtQixJQUFJVyxNQUFNLEtBQUssSUFBSSxFQUFFLEdBQUdYO2dCQUMxQztRQUNGO1FBQ0FZLGdCQUFnQixDQUFDZDtZQUNmdkIsSUFBSSxDQUFDbUIsUUFBVztvQkFDZGxCLGVBQWVzQjtnQkFDakI7UUFDRjtRQUNBZSxZQUFZLENBQUNDO1lBQ1h2QyxJQUFJLENBQUNtQixRQUFXO29CQUNkZixnQkFBZ0JtQztnQkFDbEI7UUFDRjtRQUNBQyxnQkFBZ0IsQ0FBQ0Q7WUFDZnZDLElBQUksQ0FBQ21CLFFBQVc7b0JBQ2RqQixTQUFTcUM7Z0JBQ1g7UUFDRjtRQUNBRSxhQUFhLENBQUNDO1lBQ1oxQyxJQUFJLENBQUNtQixRQUFXO29CQUNkZCxVQUFVcUM7Z0JBQ1o7UUFDRjtRQUNBQyxnQkFBZ0IsQ0FBQ0MsS0FBSzFCO1lBQ3BCbEIsSUFBSSxDQUFDbUIsUUFBVztvQkFDZGQsVUFBVTt3QkFBRSxHQUFHYyxNQUFNZCxRQUFRO3dCQUFFLENBQUN1QyxJQUFJLEVBQUUxQjtvQkFBSTtnQkFDNUM7UUFDRjtJQUNGO0FBRUEsTUFBTTJCLGVBQWVqRCwrQ0FBTUEsQ0FDekJDLDREQUFRQSxDQUNOQywyREFBT0EsQ0FBQ0MsV0FBVztJQUNqQitDLE1BQU07QUFDUjtBQUlKLGlFQUFlRCxZQUFZQSxFQUFDIiwic291cmNlcyI6WyJDOlxcQ1NoYXJwXFxHaXRIdWJcXFByb2ZpbGVDU1NcXENsaWVudEFwcFxcc3JjXFx1dGlsc1xcc3RvcmVBcHAuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlIH0gZnJvbSBcInp1c3RhbmRcIjtcclxuXHJcbmltcG9ydCB7IGRldnRvb2xzLCBwZXJzaXN0IH0gZnJvbSBcInp1c3RhbmQvbWlkZGxld2FyZVwiO1xyXG5cclxuY29uc3QgdXNlclN0b3JlID0gKHNldCkgPT4gKHtcclxuICBpc1VzZXJMb2dlZEluOiBmYWxzZSxcclxuICBmb3JtVVJMOiBcIlwiLFxyXG4gIGFwcFBvb2w6IFwiXCIsXHJcbiAgY3VycmVudFBhZ2VVUkw6IFwiXCIsXHJcbiAgdXNlckZvcm06IHt9LFxyXG4gIGJhY2tQYWdlQnV0dG9uOiBbXSxcclxuICBjb2xsZWdlT2JqOiBbXSxcclxuICBtb25leU9iajogW10sXHJcbiAgdXNlck9iamVjdDoge30sXHJcbiAgcGFnZUluZGV4OiBcIlwiLFxyXG4gIGJhY2tCdXR0b25Nb2RlOiBmYWxzZSxcclxuICBhZGRDb2xsZWdlRmxhZzogZmFsc2UsXHJcbiAgZG9sbGFyRm9ybWF0OiBmYWxzZSxcclxuICBoaWRlTG9naW46IGZhbHNlLFxyXG4gIFNTTlZhbHVlOiBcIlwiLFxyXG4gIEFwcFJldmlld05yOiAwLFxyXG4gIHNldEFwcFJldmlld05yOiAodmFsKSA9PiB7XHJcbiAgICBzZXQoKHN0YXRlKSA9PiAoe1xyXG4gICAgICBBcHBSZXZpZXdOcjogdmFsLFxyXG4gICAgfSkpO1xyXG4gIH0sXHJcbiAgc2V0SGlkZUxvZ2luOiAodmFsKSA9PiB7XHJcbiAgICBzZXQoKHN0YXRlKSA9PiAoe1xyXG4gICAgICBoaWRlTG9naW46IHZhbCxcclxuICAgIH0pKTtcclxuICB9LFxyXG4gIHNldFNTTlZhbHVlOiAodmFsKSA9PiB7XHJcbiAgICBzZXQoKHN0YXRlKSA9PiAoe1xyXG4gICAgICBTU05WYWx1ZTogdmFsLFxyXG4gICAgfSkpO1xyXG4gIH0sXHJcbiAgc2V0RG9sbGFyRm9ybWF0OiAoZmxhZykgPT4ge1xyXG4gICAgc2V0KChzdGF0ZSkgPT4gKHtcclxuICAgICAgZG9sbGFyRm9ybWF0OiBmbGFnLFxyXG4gICAgfSkpO1xyXG4gIH0sXHJcbiAgc2V0TW9uZXlPYmo6IChvYmopID0+IHtcclxuICAgIHNldCgoc3RhdGUpID0+ICh7XHJcbiAgICAgIG1vbmV5T2JqOiBvYmosXHJcbiAgICB9KSk7XHJcbiAgfSxcclxuICBzZXRDb2xsZWdlT2JqOiAob2JqKSA9PiB7XHJcbiAgICBzZXQoKHN0YXRlKSA9PiAoe1xyXG4gICAgICBjb2xsZWdlT2JqOiBvYmosXHJcbiAgICB9KSk7XHJcbiAgfSxcclxuICBzZXRBYWRkQ29sbGVnZUZsYWc6IChmbGcpID0+IHtcclxuICAgIHNldCgoc3RhdGUpID0+ICh7XHJcbiAgICAgIGFkZENvbGxlZ2VGbGFnOiBmbGcsXHJcbiAgICB9KSk7XHJcbiAgfSxcclxuICBzZXRBcHBQb29sOiAoc2l0ZSkgPT4ge1xyXG4gICAgc2V0KChzdGF0ZSkgPT4gKHtcclxuICAgICAgYXBwUG9vbDogc2l0ZSxcclxuICAgIH0pKTtcclxuICB9LFxyXG4gIHNldEJhY2tCdXR0b25Nb2RlOiAobW9kZSkgPT4ge1xyXG4gICAgc2V0KChzdGF0ZSkgPT4gKHtcclxuICAgICAgYmFja0J1dHRvbk1vZGU6IG1vZGUsXHJcbiAgICB9KSk7XHJcbiAgfSxcclxuICBzZXRQYWdlSW5kZXg6IChpZHgpID0+IHtcclxuICAgIHNldCgoc3RhdGUpID0+ICh7XHJcbiAgICAgIHBhZ2VJbmRleDogaWR4LFxyXG4gICAgfSkpO1xyXG4gIH0sXHJcbiAgc2V0QmFja1BhZ2VCdXR0b246IChvYmopID0+IHtcclxuICAgIHNldCgoc3RhdGUpID0+ICh7XHJcbiAgICAgIGJhY2tQYWdlQnV0dG9uOiBvYmoubGVuZ3RoID09PSAwID8gW10gOiBvYmosXHJcbiAgICB9KSk7XHJcbiAgfSxcclxuICBzZXRVc2VyTG9nZWRJbjogKGZsYWcpID0+IHtcclxuICAgIHNldCgoc3RhdGUpID0+ICh7XHJcbiAgICAgIGlzVXNlckxvZ2VkSW46IGZsYWcsXHJcbiAgICB9KSk7XHJcbiAgfSxcclxuICBzZXRwYWdlVXJsOiAocGFnZSkgPT4ge1xyXG4gICAgc2V0KChzdGF0ZSkgPT4gKHtcclxuICAgICAgY3VycmVudFBhZ2VVUkw6IHBhZ2UsXHJcbiAgICB9KSk7XHJcbiAgfSxcclxuICBzZXRwYWdlRm9ybVVybDogKHBhZ2UpID0+IHtcclxuICAgIHNldCgoc3RhdGUpID0+ICh7XHJcbiAgICAgIGZvcm1VUkw6IHBhZ2UsXHJcbiAgICB9KSk7XHJcbiAgfSxcclxuICBzZXRVc2VyRm9ybTogKGZvcm0pID0+IHtcclxuICAgIHNldCgoc3RhdGUpID0+ICh7XHJcbiAgICAgIHVzZXJGb3JtOiBmb3JtLFxyXG4gICAgfSkpO1xyXG4gIH0sXHJcbiAgdXBkYXRlVXNlckZvcm06IChrZXksIHZhbCkgPT4ge1xyXG4gICAgc2V0KChzdGF0ZSkgPT4gKHtcclxuICAgICAgdXNlckZvcm06IHsgLi4uc3RhdGUudXNlckZvcm0sIFtrZXldOiB2YWwgfSxcclxuICAgIH0pKTtcclxuICB9LFxyXG59KTtcclxuXHJcbmNvbnN0IHVzZVVzZXJTdG9yZSA9IGNyZWF0ZShcclxuICBkZXZ0b29scyhcclxuICAgIHBlcnNpc3QodXNlclN0b3JlLCB7XHJcbiAgICAgIG5hbWU6IFwidXNlclwiLFxyXG4gICAgfSlcclxuICApXHJcbik7XHJcblxyXG5leHBvcnQgZGVmYXVsdCB1c2VVc2VyU3RvcmU7XHJcbiJdLCJuYW1lcyI6WyJjcmVhdGUiLCJkZXZ0b29scyIsInBlcnNpc3QiLCJ1c2VyU3RvcmUiLCJzZXQiLCJpc1VzZXJMb2dlZEluIiwiZm9ybVVSTCIsImFwcFBvb2wiLCJjdXJyZW50UGFnZVVSTCIsInVzZXJGb3JtIiwiYmFja1BhZ2VCdXR0b24iLCJjb2xsZWdlT2JqIiwibW9uZXlPYmoiLCJ1c2VyT2JqZWN0IiwicGFnZUluZGV4IiwiYmFja0J1dHRvbk1vZGUiLCJhZGRDb2xsZWdlRmxhZyIsImRvbGxhckZvcm1hdCIsImhpZGVMb2dpbiIsIlNTTlZhbHVlIiwiQXBwUmV2aWV3TnIiLCJzZXRBcHBSZXZpZXdOciIsInZhbCIsInN0YXRlIiwic2V0SGlkZUxvZ2luIiwic2V0U1NOVmFsdWUiLCJzZXREb2xsYXJGb3JtYXQiLCJmbGFnIiwic2V0TW9uZXlPYmoiLCJvYmoiLCJzZXRDb2xsZWdlT2JqIiwic2V0QWFkZENvbGxlZ2VGbGFnIiwiZmxnIiwic2V0QXBwUG9vbCIsInNpdGUiLCJzZXRCYWNrQnV0dG9uTW9kZSIsIm1vZGUiLCJzZXRQYWdlSW5kZXgiLCJpZHgiLCJzZXRCYWNrUGFnZUJ1dHRvbiIsImxlbmd0aCIsInNldFVzZXJMb2dlZEluIiwic2V0cGFnZVVybCIsInBhZ2UiLCJzZXRwYWdlRm9ybVVybCIsInNldFVzZXJGb3JtIiwiZm9ybSIsInVwZGF0ZVVzZXJGb3JtIiwia2V5IiwidXNlVXNlclN0b3JlIiwibmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/utils/storeApp.js\n");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next-query-params":
/*!************************************!*\
  !*** external "next-query-params" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-query-params");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prop-types":
/*!*****************************!*\
  !*** external "prop-types" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("prop-types");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react-idle-timer":
/*!***********************************!*\
  !*** external "react-idle-timer" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-idle-timer");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "use-query-params":
/*!***********************************!*\
  !*** external "use-query-params" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("use-query-params");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "zustand":
/*!**************************!*\
  !*** external "zustand" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("zustand");;

/***/ }),

/***/ "zustand/middleware":
/*!*************************************!*\
  !*** external "zustand/middleware" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = import("zustand/middleware");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F404&preferredRegion=&absolutePagePath=.%2Fpages%5C404.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();