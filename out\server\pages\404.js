/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/404";
exports.ids = ["pages/404"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F404&preferredRegion=&absolutePagePath=.%2Fpages%5C404.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F404&preferredRegion=&absolutePagePath=.%2Fpages%5C404.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./pages/_document.js\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.js\");\n/* harmony import */ var _pages_404_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\404.js */ \"./pages/404.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_404_js__WEBPACK_IMPORTED_MODULE_5__]);\n_pages_404_js__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_404_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/404\",\n        pathname: \"/404\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_404_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F404&preferredRegion=&absolutePagePath=.%2Fpages%5C404.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./pages/404.js":
/*!**********************!*\
  !*** ./pages/404.js ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _src_components_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../src/components/layout */ \"./src/components/layout.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _src_components_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../src/components/Button */ \"./src/components/Button/index.jsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_components_layout__WEBPACK_IMPORTED_MODULE_2__]);\n_src_components_layout__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction NotFound() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_3___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Page Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Page not found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"mx-auto space-y-5 bg-white p-7 md:p-11 md:max-w-lg md:shadow-md md:shadow-light-purple mt-12 md:mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-4\",\n                            children: \"404\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold text-gray-700 mb-4\",\n                            children: \"Page Not Found\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-8\",\n                            children: \"The page you are looking for doesn't exist or has been moved.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_src_components_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>router.push(\"/\"),\n                            className: \"w-full md:w-fit\",\n                            children: \"Go Home\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\404.js\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/404.js\n");

/***/ }),

/***/ "./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _src_styles_global_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../src/styles/global.css */ \"./src/styles/global.css\");\n/* harmony import */ var _src_styles_global_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_src_styles_global_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var use_query_params__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! use-query-params */ \"use-query-params\");\n/* harmony import */ var use_query_params__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(use_query_params__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_query_params__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-query-params */ \"next-query-params\");\n/* harmony import */ var next_query_params__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_query_params__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(use_query_params__WEBPACK_IMPORTED_MODULE_2__.QueryParamProvider, {\n        adapter: next_query_params__WEBPACK_IMPORTED_MODULE_3__.NextAdapter,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...pageProps\n        }, void 0, false, {\n            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_app.js\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_app.js\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBaUM7QUFDb0I7QUFDTjtBQUVoQyxTQUFTRSxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFFO0lBQ2xELHFCQUNFLDhEQUFDSixnRUFBa0JBO1FBQUNLLFNBQVNKLDBEQUFXQTtrQkFDdEMsNEVBQUNFO1lBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7QUFHOUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9maWxlLWNzcy1hcHAvLi9wYWdlcy9fYXBwLmpzP2UwYWQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICcuLi9zcmMvc3R5bGVzL2dsb2JhbC5jc3MnXG5pbXBvcnQgeyBRdWVyeVBhcmFtUHJvdmlkZXIgfSBmcm9tICd1c2UtcXVlcnktcGFyYW1zJ1xuaW1wb3J0IHsgTmV4dEFkYXB0ZXIgfSBmcm9tICduZXh0LXF1ZXJ5LXBhcmFtcydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gKFxuICAgIDxRdWVyeVBhcmFtUHJvdmlkZXIgYWRhcHRlcj17TmV4dEFkYXB0ZXJ9PlxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgIDwvUXVlcnlQYXJhbVByb3ZpZGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsiUXVlcnlQYXJhbVByb3ZpZGVyIiwiTmV4dEFkYXB0ZXIiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJhZGFwdGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/_app.js\n");

/***/ }),

/***/ "./pages/_document.js":
/*!****************************!*\
  !*** ./pages/_document.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        charSet: \"utf-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_document.js\",\n                        lineNumber: 7,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_document.js\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#000000\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_document.js\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"CSS Profile Application\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_document.js\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_document.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_document.js\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_document.js\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_document.js\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\pages\\\\_document.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fZG9jdW1lbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTREO0FBRTdDLFNBQVNJO0lBQ3RCLHFCQUNFLDhEQUFDSiwrQ0FBSUE7UUFBQ0ssTUFBSzs7MEJBQ1QsOERBQUNKLCtDQUFJQTs7a0NBQ0gsOERBQUNLO3dCQUFLQyxTQUFROzs7Ozs7a0NBQ2QsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLOzs7Ozs7a0NBQ3RCLDhEQUFDSjt3QkFBS0ssTUFBSzt3QkFBY0MsU0FBUTs7Ozs7O2tDQUNqQyw4REFBQ047d0JBQUtLLE1BQUs7d0JBQWNDLFNBQVE7Ozs7Ozs7Ozs7OzswQkFFbkMsOERBQUNDOztrQ0FDQyw4REFBQ1gsK0NBQUlBOzs7OztrQ0FDTCw4REFBQ0MscURBQVVBOzs7Ozs7Ozs7Ozs7Ozs7OztBQUluQiIsInNvdXJjZXMiOlsid2VicGFjazovL3Byb2ZpbGUtY3NzLWFwcC8uL3BhZ2VzL19kb2N1bWVudC5qcz81MzhiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEh0bWwsIEhlYWQsIE1haW4sIE5leHRTY3JpcHQgfSBmcm9tICduZXh0L2RvY3VtZW50J1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEb2N1bWVudCgpIHtcbiAgcmV0dXJuIChcbiAgICA8SHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxIZWFkPlxuICAgICAgICA8bWV0YSBjaGFyU2V0PVwidXRmLThcIiAvPlxuICAgICAgICA8bGluayByZWw9XCJpY29uXCIgaHJlZj1cIi9mYXZpY29uLmljb1wiIC8+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJ0aGVtZS1jb2xvclwiIGNvbnRlbnQ9XCIjMDAwMDAwXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cImRlc2NyaXB0aW9uXCIgY29udGVudD1cIkNTUyBQcm9maWxlIEFwcGxpY2F0aW9uXCIgLz5cbiAgICAgIDwvSGVhZD5cbiAgICAgIDxib2R5PlxuICAgICAgICA8TWFpbiAvPlxuICAgICAgICA8TmV4dFNjcmlwdCAvPlxuICAgICAgPC9ib2R5PlxuICAgIDwvSHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkh0bWwiLCJIZWFkIiwiTWFpbiIsIk5leHRTY3JpcHQiLCJEb2N1bWVudCIsImxhbmciLCJtZXRhIiwiY2hhclNldCIsImxpbmsiLCJyZWwiLCJocmVmIiwibmFtZSIsImNvbnRlbnQiLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/_document.js\n");

/***/ }),

/***/ "./src/components/Button/index.jsx":
/*!*****************************************!*\
  !*** ./src/components/Button/index.jsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _shared_buttons_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared/buttons.js */ \"./src/components/shared/buttons.js\");\n/* harmony import */ var _shared_inputs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../shared/inputs */ \"./src/components/shared/inputs.js\");\n\n\n\n\nfunction Button({ children, className = \"\", disabled = false, variant = \"primary\", ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        disabled: disabled,\n        className: `${(0,_shared_buttons_js__WEBPACK_IMPORTED_MODULE_2__.buttonClasses)({\n            variant\n        })} ${_shared_inputs__WEBPACK_IMPORTED_MODULE_3__.focusWithinStyles} ${className}`,\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\Button\\\\index.jsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9CdXR0b24vaW5kZXguanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXlCO0FBQzJCO0FBQ0E7QUFFN0MsU0FBU0csT0FBTyxFQUNyQkMsUUFBUSxFQUNSQyxZQUFZLEVBQUUsRUFDZEMsV0FBVyxLQUFLLEVBQ2hCQyxVQUFVLFNBQVMsRUFDbkIsR0FBR0MsT0FDSjtJQUNDLHFCQUNFLDhEQUFDQztRQUNDSCxVQUFVQTtRQUNWRCxXQUFXLENBQUMsRUFBRUosaUVBQWFBLENBQUM7WUFDMUJNO1FBQ0YsR0FBRyxDQUFDLEVBQUVMLDZEQUFpQkEsQ0FBQyxDQUFDLEVBQUVHLFVBQVUsQ0FBQztRQUNyQyxHQUFHRyxLQUFLO2tCQUVSSjs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9maWxlLWNzcy1hcHAvLi9zcmMvY29tcG9uZW50cy9CdXR0b24vaW5kZXguanN4P2M2ZGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiXHJcbmltcG9ydCB7IGJ1dHRvbkNsYXNzZXMgfSBmcm9tIFwiLi4vc2hhcmVkL2J1dHRvbnMuanNcIlxyXG5pbXBvcnQgeyBmb2N1c1dpdGhpblN0eWxlcyB9IGZyb20gXCIuLi9zaGFyZWQvaW5wdXRzXCJcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBCdXR0b24oe1xyXG4gIGNoaWxkcmVuLFxyXG4gIGNsYXNzTmFtZSA9IFwiXCIsXHJcbiAgZGlzYWJsZWQgPSBmYWxzZSxcclxuICB2YXJpYW50ID0gXCJwcmltYXJ5XCIsXHJcbiAgLi4ucHJvcHNcclxufSkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8YnV0dG9uXHJcbiAgICAgIGRpc2FibGVkPXtkaXNhYmxlZH1cclxuICAgICAgY2xhc3NOYW1lPXtgJHtidXR0b25DbGFzc2VzKHtcclxuICAgICAgICB2YXJpYW50LFxyXG4gICAgICB9KX0gJHtmb2N1c1dpdGhpblN0eWxlc30gJHtjbGFzc05hbWV9YH1cclxuICAgICAgey4uLnByb3BzfVxyXG4gICAgPlxyXG4gICAgICB7Y2hpbGRyZW59XHJcbiAgICA8L2J1dHRvbj5cclxuICApXHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiYnV0dG9uQ2xhc3NlcyIsImZvY3VzV2l0aGluU3R5bGVzIiwiQnV0dG9uIiwiY2hpbGRyZW4iLCJjbGFzc05hbWUiLCJkaXNhYmxlZCIsInZhcmlhbnQiLCJwcm9wcyIsImJ1dHRvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/Button/index.jsx\n");

/***/ }),

/***/ "./src/components/header.js":
/*!**********************************!*\
  !*** ./src/components/header.js ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_storeApp__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/storeApp */ \"./src/utils/storeApp.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_utils_storeApp__WEBPACK_IMPORTED_MODULE_4__]);\n_utils_storeApp__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nfunction Header({ ...props }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [skip, SetSkip] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)( false ? 0 : \"\");\n    const [isFocused, setIsFocused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isOpen, SetIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { isUserLogedIn, userForm, setUserLogedIn, setUserForm, setpageFormUrl, setpageUrl, setBackPageButton, setPageIndex, setMoneyObj, hideLogin } = (0,_utils_storeApp__WEBPACK_IMPORTED_MODULE_4__[\"default\"])((state)=>({\n            isUserLogedIn: state.isUserLogedIn,\n            userForm: state.userForm,\n            setUserLogedIn: state.setUserLogedIn,\n            setUserForm: state.setUserForm,\n            setpageFormUrl: state.setpageFormUrl,\n            setpageUrl: state.setpageUrl,\n            setBackPageButton: state.setBackPageButton,\n            setPageIndex: state.setPageIndex,\n            setMoneyObj: state.setMoneyObj,\n            hideLogin: state.hideLogin\n        }));\n    const handleClose = (e)=>{\n        let clickedInsideMenu = e.target.closest(\"#menu\") === undefined;\n        if (!clickedInsideMenu) {\n            SetIsOpen(false);\n        }\n    };\n    const handleLogin = ()=>{\n        let AppId = userForm.ApplicationId;\n        setUserLogedIn(false);\n        setUserForm({});\n        setBackPageButton([]);\n        setMoneyObj([]);\n        setpageFormUrl(\"\");\n        setPageIndex(\"\");\n        setpageUrl(\"\");\n        window.location.href = \"https://account.collegeboard.org/login/logout?appId=7&DURL=https://student.collegeboard.org/css-financial-aid-profile\";\n    };\n    const activeStyles = {\n        color: \"#e9d5ff\",\n        textDecorationLine: \"underline\",\n        textUnderlineOffset: \"5px\"\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        document.addEventListener(\"click\", handleClose, true);\n        return ()=>{\n            document.removeEventListener(\"click\", handleClose, true);\n        };\n    }, []);\n    // Static assets for Next.js\n    const data = {\n        logo: {\n            publicURL: \"/logo.svg\"\n        },\n        profile: {\n            publicURL: \"/profile.jpg\"\n        }\n    };\n    const goto = (url)=>{\n        router.push(url);\n    };\n    const handleSkip = ()=>{\n        let btn = document.getElementById(\"GoBackBtn1\");\n        if (btn) {\n            btn.focus();\n            btn.checked = true;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"fixed z-50 flex mb-8 w-full flex-row flex-nowrap items-center justify-center bg-black text-sm font-medium text-white md:text-lg\",\n                ...props,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        className: \"text-sm\",\n                        href: \"#nolinkID\",\n                        onClick: handleSkip,\n                        onFocus: ()=>setIsFocused(true),\n                        onBlur: ()=>setIsFocused(false),\n                        children: isFocused ? \"Skip to main content\" : null\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        className: \"pl-2 hidden grow pr-4 md:visible md:flex hover:text-gray-300\",\n                        href: \"https://www.collegeboard.org\",\n                        target: \"_blank\",\n                        id: \"CollegeBoardId\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex flex-row flex-nowrap items-center justify-center space-x-2 pr-1 hover:w-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: process.env.IMG_URL + data.logo.publicURL,\n                                    alt: \"College Board\",\n                                    className: \"inline\",\n                                    width: 32,\n                                    height: 32\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"CollegeBoard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex w-full flex-row flex-nowrap items-center justify-start bg-program-core-higher-ed p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"CSS Profile\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: process.env.IMG_URL + data.profile.publicURL,\n                                    alt: \"CSS Profile\",\n                                    className: \"inline\",\n                                    width: 116,\n                                    height: 36\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"ml-auto md:hidden\",\n                                onClick: ()=>SetIsOpen(true),\n                                children: \"Menu\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-auto hidden grow flex-row flex-nowrap items-center justify-end space-x-9 md:visible md:flex\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://privacy.collegeboard.org\",\n                                        target: \"_blank\",\n                                        className: `hover:text-purple-300`,\n                                        children: \"Privacy Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"https://CSSAPIProfile.collegeboard.org/contact-us\",\n                                        target: \"_blank\",\n                                        className: `hover:text-purple-300`,\n                                        children: \"Contact Us\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this),\n                                    hideLogin ? \"\" : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                to: \"/dashboard\",\n                                                activeStyle: activeStyles,\n                                                className: `hover:text-purple-300 cursor-pointer`,\n                                                children: \"Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this),\n                                            isUserLogedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#nolinkID\",\n                                                className: `hover:text-purple-300 cursor-pointer`,\n                                                onClick: ()=>handleLogin(),\n                                                children: \"Log out\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                                lineNumber: 159,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                to: \"/login\",\n                                                className: `hover:text-purple-300 cursor-pointer`,\n                                                activeStyle: activeStyles,\n                                                children: \"Login\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${isOpen ? \"block\" : \"hidden\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed bottom-0 left-0 right-0 top-0 z-10 bg-black opacity-20\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        id: \"menu\",\n                        className: \"duration-600 fixed top-0 right-0 z-30 z-20 h-full w-64 transform overflow-auto bg-white bg-white text-black transition-all ease-in-out md:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between p-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold\",\n                                        children: \"Menu\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 188,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-20 cursor-pointer font-bold text-sky-700 underline\",\n                                        onClick: ()=>SetIsOpen(false),\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"flex flex-col gap-y-3 p-5 font-bold\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://privacy.collegeboard.org\",\n                                            target: \"_blank\",\n                                            className: `text-program-core-higher-ed underline underline-offset-2`,\n                                            children: \"Privacy Policy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>goto(\"/dashboard\"),\n                                            className: `text-program-core-higher-ed underline underline-offset-2 cursor-pointer`,\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"https://CSSAPIProfile.collegeboard.org/contact-us\",\n                                            target: \"_blank\",\n                                            className: `text-program-core-higher-ed underline underline-offset-2`,\n                                            children: \"Contact Us\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: isUserLogedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#nolinkID\",\n                                            className: \"cursor-pointer text-program-core-higher-ed underline underline-offset-2\",\n                                            onClick: ()=>handleLogin(),\n                                            children: \"Log out\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>goto(\"/login\"),\n                                            className: `text-program-core-higher-ed underline underline-offset-2`,\n                                            children: \"Login\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\header.js\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/header.js\n");

/***/ }),

/***/ "./src/components/layout.js":
/*!**********************************!*\
  !*** ./src/components/layout.js ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prop-types */ \"prop-types\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./header */ \"./src/components/header.js\");\n/* harmony import */ var react_idle_timer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-idle-timer */ \"react-idle-timer\");\n/* harmony import */ var react_idle_timer__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react_idle_timer__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _utils_storeApp__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/storeApp */ \"./src/utils/storeApp.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_header__WEBPACK_IMPORTED_MODULE_4__, _utils_storeApp__WEBPACK_IMPORTED_MODULE_6__]);\n([_header__WEBPACK_IMPORTED_MODULE_4__, _utils_storeApp__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nfunction Layout({ children }) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const timeout = 60000 * 30;\n    const [remaining, setRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(timeout);\n    const [elapsed, setElapsed] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [lastActive, setLastActive] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(+new Date());\n    const [isIdle, setIsIdle] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const handleOnActive = ()=>setIsIdle(false);\n    const { isUserLogedIn, userForm, setUserLogedIn, setUserForm, setpageFormUrl, setpageUrl, setBackPageButton, setPageIndex, appPool } = (0,_utils_storeApp__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((state)=>({\n            isUserLogedIn: state.isUserLogedIn,\n            userForm: state.userForm,\n            setUserLogedIn: state.setUserLogedIn,\n            setUserForm: state.setUserForm,\n            setpageFormUrl: state.setpageFormUrl,\n            setpageUrl: state.setpageUrl,\n            setBackPageButton: state.setBackPageButton,\n            setPageIndex: state.setPageIndex,\n            appPool: state.appPool\n        }));\n    const handleOnIdle = ()=>{\n        let AppId = userForm.ApplicationId;\n        setUserLogedIn(false);\n        setUserForm({});\n        setpageFormUrl(\"\");\n        setpageUrl(\"\");\n        setBackPageButton([]);\n        setPageIndex(\"\");\n        window.location.href = \"https://account.collegeboard.org/login/logout?appId=\" + AppId + \"&DURL=https://student.collegeboard.org/css-financial-aid-profile\";\n    };\n    const { getRemainingTime, getLastActiveTime, getElapsedTime } = (0,react_idle_timer__WEBPACK_IMPORTED_MODULE_5__.useIdleTimer)({\n        timeout,\n        onActive: handleOnActive,\n        onIdle: handleOnIdle\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setRemaining(getRemainingTime());\n        setLastActive(getLastActiveTime());\n        setElapsed(getElapsedTime());\n        const intervalId = setInterval(()=>{\n            setRemaining(getRemainingTime());\n            setLastActive(getLastActiveTime());\n            setElapsed(getElapsedTime());\n        }, 1000);\n        // Cleanup function to clear the interval\n        return ()=>{\n            clearInterval(intervalId);\n        };\n    //adobeAnalyticsPush();\n    }, []);\n    const adobeAnalyticsPush = ()=>{\n        window.adobeDataLayer = window.adobeDataLayer || [];\n        window.adobeDataLayer.push({\n            page: {\n                flowCode: userForm.MenuName,\n                pageCode: userForm.PageName,\n                appViewCode: \"\"\n            }\n        });\n        try {\n            window._satellite.track(\"cbTrack.viewInDom\");\n        } catch (errSatelliteTrack) {}\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col min-h-screen font-sans text-gray-900 md:text-lg text-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\layout.js\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                id: \"scrollToTop\",\n                className: \"flex-1 w-full max-w-4xl px-4 py-8 mx-auto md:px-8 md:py-16\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\layout.js\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\CSharp\\\\GitHub\\\\ProfileCSS\\\\ClientApp\\\\src\\\\components\\\\layout.js\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\nLayout.propTypes = {\n    children: (prop_types__WEBPACK_IMPORTED_MODULE_1___default().node).isRequired\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/layout.js\n");

/***/ }),

/***/ "./src/components/shared/buttons.js":
/*!******************************************!*\
  !*** ./src/components/shared/buttons.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buttonClasses: () => (/* binding */ buttonClasses)\n/* harmony export */ });\nconst primaryButtonColors = \"text-white bg-sky-700 hover:bg-sky-600 disabled:border-gray-500 disabled:bg-gray-400\";\nconst secondaryButtonColors = \"text-black bg-gray-300 hover:bg-gray-400 disabled:border-gray-500 disabled:bg-gray-100 disabled:text-gray-500\";\nconst greenButtonColors = \"bg-green-100 text-green-800 hover:bg-green-200\";\nconst whiteButtonColors = \"hover:bg-gray-100\";\nconst baseButtonClasses = `py-3 px-6 border-2 border-b-4 border-black rounded-sm text-center font-bold\r\nnot(:disabled):hover:cursor-pointer not(:disabled):active:outline active:outline-offset-2 active:outline-4 active:outline-yellow-600`;\nfunction buttonClasses({ variant }) {\n    switch(variant){\n        case \"primary\":\n            return `${baseButtonClasses} ${primaryButtonColors}`;\n        case \"secondary\":\n            return `${baseButtonClasses} ${secondaryButtonColors}`;\n        case \"green\":\n            return `${baseButtonClasses} ${greenButtonColors}`;\n        case \"white\":\n            return `${baseButtonClasses} ${whiteButtonColors}`;\n        default:\n            return baseButtonClasses;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/shared/buttons.js\n");

/***/ }),

/***/ "./src/components/shared/inputs.js":
/*!*****************************************!*\
  !*** ./src/components/shared/inputs.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   borderStyles: () => (/* binding */ borderStyles),\n/* harmony export */   focusStyles: () => (/* binding */ focusStyles),\n/* harmony export */   focusWithinStyles: () => (/* binding */ focusWithinStyles),\n/* harmony export */   inputStyles: () => (/* binding */ inputStyles)\n/* harmony export */ });\nconst borderStyles = (error)=>error ? \"border-red-600\" : \"border-gray-500\";\nconst inputStyles = \"w-full appearance-none border-2 py-3.5 px-2.5 rounded-none bg-white shadow-inner\";\nconst focusStyles = \"focus:outline focus:outline-offset-2 focus:outline-4 focus:outline-sky-600\";\nconst focusWithinStyles = \"focus-within:outline focus-within:outline-offset-2 focus-within:outline-4 focus-within:outline-sky-600\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9zaGFyZWQvaW5wdXRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBTyxNQUFNQSxlQUFlLENBQUNDLFFBQVdBLFFBQVEsbUJBQW1CLGtCQUFtQjtBQUUvRSxNQUFNQyxjQUNYLG1GQUFtRjtBQUU5RSxNQUFNQyxjQUNYLDZFQUE2RTtBQUV4RSxNQUFNQyxvQkFDWCx5R0FBeUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcm9maWxlLWNzcy1hcHAvLi9zcmMvY29tcG9uZW50cy9zaGFyZWQvaW5wdXRzLmpzP2JlNjkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGJvcmRlclN0eWxlcyA9IChlcnJvcikgPT4gKGVycm9yID8gXCJib3JkZXItcmVkLTYwMFwiIDogXCJib3JkZXItZ3JheS01MDBcIik7XHJcblxyXG5leHBvcnQgY29uc3QgaW5wdXRTdHlsZXMgPVxyXG4gIFwidy1mdWxsIGFwcGVhcmFuY2Utbm9uZSBib3JkZXItMiBweS0zLjUgcHgtMi41IHJvdW5kZWQtbm9uZSBiZy13aGl0ZSBzaGFkb3ctaW5uZXJcIjtcclxuXHJcbmV4cG9ydCBjb25zdCBmb2N1c1N0eWxlcyA9XHJcbiAgXCJmb2N1czpvdXRsaW5lIGZvY3VzOm91dGxpbmUtb2Zmc2V0LTIgZm9jdXM6b3V0bGluZS00IGZvY3VzOm91dGxpbmUtc2t5LTYwMFwiO1xyXG5cclxuZXhwb3J0IGNvbnN0IGZvY3VzV2l0aGluU3R5bGVzID1cclxuICBcImZvY3VzLXdpdGhpbjpvdXRsaW5lIGZvY3VzLXdpdGhpbjpvdXRsaW5lLW9mZnNldC0yIGZvY3VzLXdpdGhpbjpvdXRsaW5lLTQgZm9jdXMtd2l0aGluOm91dGxpbmUtc2t5LTYwMFwiO1xyXG4iXSwibmFtZXMiOlsiYm9yZGVyU3R5bGVzIiwiZXJyb3IiLCJpbnB1dFN0eWxlcyIsImZvY3VzU3R5bGVzIiwiZm9jdXNXaXRoaW5TdHlsZXMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/components/shared/inputs.js\n");

/***/ }),

/***/ "./src/utils/storeApp.js":
/*!*******************************!*\
  !*** ./src/utils/storeApp.js ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"zustand\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"zustand/middleware\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([zustand__WEBPACK_IMPORTED_MODULE_0__, zustand_middleware__WEBPACK_IMPORTED_MODULE_1__]);\n([zustand__WEBPACK_IMPORTED_MODULE_0__, zustand_middleware__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nconst userStore = (set)=>({\n        isUserLogedIn: false,\n        formURL: \"\",\n        appPool: \"\",\n        currentPageURL: \"\",\n        userForm: {},\n        backPageButton: [],\n        collegeObj: [],\n        moneyObj: [],\n        userObject: {},\n        pageIndex: \"\",\n        backButtonMode: false,\n        addCollegeFlag: false,\n        dollarFormat: false,\n        hideLogin: false,\n        SSNValue: \"\",\n        AppReviewNr: 0,\n        setAppReviewNr: (val)=>{\n            set((state)=>({\n                    AppReviewNr: val === 0 ? 0 : state.AppReviewNr + 1\n                }));\n        },\n        setHideLogin: (val)=>{\n            set((state)=>({\n                    hideLogin: val\n                }));\n        },\n        setSSNValue: (val)=>{\n            set((state)=>({\n                    SSNValue: val\n                }));\n        },\n        setDollarFormat: (flag)=>{\n            set((state)=>({\n                    dollarFormat: flag\n                }));\n        },\n        setMoneyObj: (obj)=>{\n            set((state)=>({\n                    moneyObj: obj.length === 0 ? [] : [\n                        ...state.moneyObj,\n                        obj\n                    ]\n                }));\n        },\n        setCollegeObj: (obj)=>{\n            set((state)=>({\n                    collegeObj: obj\n                }));\n        },\n        setAaddCollegeFlag: (flg)=>{\n            set((state)=>({\n                    addCollegeFlag: flg\n                }));\n        },\n        setAppPool: (site)=>{\n            set((state)=>({\n                    appPool: site\n                }));\n        },\n        setBackButtonMode: (mode)=>{\n            set((state)=>({\n                    backButtonMode: mode\n                }));\n        },\n        setPageIndex: (idx)=>{\n            set((state)=>({\n                    pageIndex: idx\n                }));\n        },\n        setBackPageButton: (obj)=>{\n            set((state)=>({\n                    backPageButton: obj.length === 0 ? [] : obj\n                }));\n        },\n        setUserLogedIn: (flag)=>{\n            set((state)=>({\n                    isUserLogedIn: flag\n                }));\n        },\n        setpageUrl: (page)=>{\n            set((state)=>({\n                    currentPageURL: page\n                }));\n        },\n        setpageFormUrl: (page)=>{\n            set((state)=>({\n                    formURL: page\n                }));\n        },\n        setUserForm: (form)=>{\n            set((state)=>({\n                    userForm: form\n                }));\n        },\n        updateUserForm: (key, val)=>{\n            set((state)=>({\n                    userForm: {\n                        ...state.userForm,\n                        [key]: val\n                    }\n                }));\n        }\n    });\nconst useUserStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.devtools)((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)(userStore, {\n    name: \"user\"\n})));\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useUserStore);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/storeApp.js\n");

/***/ }),

/***/ "./src/styles/global.css":
/*!*******************************!*\
  !*** ./src/styles/global.css ***!
  \*******************************/
/***/ (() => {



/***/ }),

/***/ "next-query-params":
/*!************************************!*\
  !*** external "next-query-params" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-query-params");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "prop-types":
/*!*****************************!*\
  !*** external "prop-types" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("prop-types");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react-idle-timer":
/*!***********************************!*\
  !*** external "react-idle-timer" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-idle-timer");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "use-query-params":
/*!***********************************!*\
  !*** external "use-query-params" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("use-query-params");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "zustand":
/*!**************************!*\
  !*** external "zustand" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("zustand");;

/***/ }),

/***/ "zustand/middleware":
/*!*************************************!*\
  !*** external "zustand/middleware" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = import("zustand/middleware");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F404&preferredRegion=&absolutePagePath=.%2Fpages%5C404.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();