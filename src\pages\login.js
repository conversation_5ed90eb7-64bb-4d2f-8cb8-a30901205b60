import React, { useContext, useEffect, useState } from "react";
import { useRouter } from "next/router";
import RadioButton from "../components/inputs/RadioButton";
import { Label } from "../components/inputs/Label";
import { TextInput } from "../components/inputs/TextInput";
import { Button } from "../components/Button";
import Link from "next/link";
import Layout from "../components/layout";
import axios from "axios";
import Head from "next/head";
import { ValidationError } from "../components/ValidationError";
import { ProcessingModal } from "../components/processingModal";
import useUserStore from "../utils/storeApp";
import WithLocation from "../components/withLocation";

//const goToNextPage = page => router.push(`/${page}`)
const NextLink = ({ children, href, ...props }) => {
  return (
    <Link href={href} legacyBehavior>
      <a {...props}>{children}</a>
    </Link>
  );
};
const Section = ({ title, children }) => {
  return (
    <section className="flex w-full flex-col gap-2">
      <h2 id={title} className="group text-2xl font-bold underline">
        <span className="float-left -ml-5 hidden text-gray-500 group-hover:inline">
          #
        </span>
        {title}
      </h2>
      {children}
    </section>
  );
};
const Component = ({ name, children, isRequired, ...props }) => {
  return (
    <>
      <Label required={isRequired} {...props}>
        {name}
      </Label>
      {children}
    </>
  );
};

const Login = () => {
  const router = useRouter();
  const {
    setUserLogedIn,
    setpageFormUrl,
    setUserForm,
    setpageUrl,
    isUserLogedIn,
    appPool,
  } = useUserStore((state) => ({
    setUserLogedIn: state.setUserLogedIn,
    setpageFormUrl: state.setpageFormUrl,
    setUserForm: state.setUserForm,
    setpageUrl: state.setpageUrl,
    isUserLogedIn: state.isUserLogedIn,
    appPool: state.appPool,
  }));

  const [isChecked, setIsChecked] = useState(false);
  const [answers, setAnswers] = useState();
  const [open, setOpen] = useState(false);
  const [flag, SetFlag] = useState(false);
  const [msg, SetMsg] = useState("Invalid User Login. Please try again.");
  const [showModal, setShowModal] = useState(false);
  const [disableBtn, SetdisableBtn] = useState(false);

  const setAnswer = (questionId, answer) => {
    setAnswers({ ...answers, [questionId]: answer });
  };

  const onFormSubmition = async (event, flag) => {
    event.preventDefault();
    SetdisableBtn(true);
    const user = await axios({
      method: "post",
      url: process.env.API_URL + "Login/LoginUser",
      data: answers,
      headers: { "Content-Type": "application/json" },
      withCredentials: true,
    })
      .then((res) => {
        setShowModal(false);
        if (res.data?.ReturnMessage?.toUpperCase() === "SUCCESS") {
          SetdisableBtn(false);
          setUserLogedIn(true);
          setUserForm(res.data);
          setpageFormUrl(res.data.PageName);
          setpageUrl(null);
          SetFlag(false);
          navigate(`/dashboard`);
        } else if (
          res.data?.ReturnMessage === "Failed" ||
          res.data?.returnMessage === "SERVERERROR" ||
          res.data?.ReturnMessage === ""
        ) {
          SetdisableBtn(false);
          SetMsg("Invalid User Login. Please try again.");
          SetFlag(true);
          setShowModal(false);
          return;
        } else {
          SetdisableBtn(false);
          SetMsg("Server error has occured.");
          SetFlag(true);
          return;
        }
      })
      .catch((err) => {
        SetdisableBtn(false);
        SetMsg("Server error has occured.");
        SetFlag(true);
        return;
      });
  };
  return (
    <Layout>
      <Helmet
        htmlAttributes={{
          lang: "en",
        }}
        title={"Login Page"}
      />
      <main className="x-auto space-y-5 bg-white p-7 md:p-11 md:max-w-lg md:shadow-md md:shadow-light-purple mt-12 md:mt-6">
        {/* <p>
          Some students may be experiencing problems logging into CSS Profile.
          Please note that access will be restored in 24 hours. Return to{" "}
          <NextLink
            className="cursor-pointer font-bold text-sky-700 underline"
            href={"https://account.collegeboard.org"}
          >
            CSSAPIProfile.org
          </NextLink>{" "}
          at that time to log in again.{" "}
        </p> */}
        {showModal ? <ProcessingModal /> : null}
        {open ? (
          <>
            <p className="md:text-xl text-lg font-bold">
              Forgot Your Password?
            </p>
            <Component name="">
              <div className="space-y-3 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50  p-5 text-base">
                <strong>{"Note: "}</strong> Please enter your email address to
                have instructions on resetting your password sent to you.
              </div>
            </Component>
            <form onSubmit={(event) => onFormSubmition(event, true)}>
              <Section>
                <Component name="Email" for="EmailId" isRequired={true}>
                  <TextInput
                    onChange={(event) => setAnswer("Username", event)}
                    type="email"
                    id="EmailId"
                    required
                  />
                </Component>
                <Button className="w-full md:w-fit">Submit</Button>
                <Component name="">
                  <a
                    href="#nolinkID"
                    className="cursor-pointer font-bold text-program-core-higher-ed underline"
                    onClick={() => setOpen(false)}
                  >
                    Back to Login
                  </a>
                </Component>
              </Section>
            </form>
          </>
        ) : (
          <>
            <p className="md:text-xl text-lg font-bold">
              Sign on to manage your accounts.
            </p>
            <Component name="">
              <div className="space-y-3 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50  p-5">
                <p>
                  <strong>{"Note: "}</strong>If you created a CSS Profile
                  account using your email address as your user name sign in
                  here:
                </p>
              </div>
            </Component>
            <form onSubmit={(event) => onFormSubmition(event, false)}>
              {" "}
              <Section>
                <Component name="Profile Year" isRequired={true}>
                  <RadioButton
                    onChange={(checked) => {
                      setIsChecked(checked.value);
                      setAnswer("AwardYear", checked.value);
                    }}
                    id="GoBackBtn"
                    name="profile_year"
                    value="2027"
                    label="2026-2027"
                    checked={"2027" === isChecked}
                    required
                  />
                  <RadioButton
                    onChange={(checked) => {
                      setIsChecked(checked.value);
                      setAnswer("AwardYear", checked.value);
                    }}
                    id="GoBackBtn1"
                    name="profile_year"
                    value="2026"
                    label="2025-2026"
                    checked={"2026" === isChecked}
                    required
                  />
                </Component>
                <Component name="Email" for="Email" isRequired={true}>
                  <TextInput
                    onChange={(event) => setAnswer("Username", event)}
                    type="email"
                    id="Email"
                    required
                  />
                </Component>
                <Component name="Password" for="Password" isRequired={true}>
                  <TextInput
                    type="password"
                    onChange={(event) => setAnswer("Password", event)}
                    required
                    id="Password"
                    minLength={9}
                    maxLength={40}
                  />
                </Component>

                <Button disabled={disableBtn} className="w-full md:w-fit">
                  Log On
                </Button>
                {flag && (
                  <Component name="">
                    {flag && <ValidationError message={msg} />}
                  </Component>
                )}
                {/* <Component name="">
                  <NextLink
                    className="cursor-pointer font-bold text-sky-700 underline"
                    href={"/createAccount"}
                    disabled={disableBtn}
                  >
                    Create an account
                  </NextLink>
                </Component> */}
              </Section>
            </form>
          </>
        )}
      </main>
    </Layout>
  );
};

export default WithLocation(Login);
