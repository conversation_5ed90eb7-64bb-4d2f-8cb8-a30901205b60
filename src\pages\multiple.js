import React, { useEffect, useState } from "react";
import { navigate, useStaticQuery, graphql } from "gatsby";
import {
  saveFormPage,
  multiplePageFromJSON,
  saveFormPageMultiple,
  multiRequiredHiddenFields,
} from "../components/NewComp";
import Layout from "../components/layout";
import axios from "axios";
import Question from "../components/Question";
import Helmet from "react-helmet";
import { Button } from "../components/Button";
import { ValidationError } from "../components/ValidationError";
import { ProcessingModal } from "../components/processingModal";
import useUserStore from "../utils/storeApp";
import WithLocation from "../components/withLocation";
import parse from "html-react-parser";

const MultiReviewPage = () => {
  const {
    setUserLogedIn,
    setpageFormUrl,
    userForm,
    isUserLogedIn,
    formURL,
    setUserForm,
    setpageUrl,
    currentPageURL,
    backPageButton,
    setBackPageButton,
    pageIndex,
    setPageIndex,
    backButtonMode,
    setBackButtonMode,
    addCollegeFlag,
    setAaddCollegeFlag,
  } = useUserStore((state) => ({
    setUserLogedIn: state.setUserLogedIn,
    setpageFormUrl: state.setpageFormUrl,
    setUserForm: state.setUserForm,
    userForm: state.userForm,
    formURL: state.formURL,
    isUserLogedIn: state.isUserLogedIn,
    setpageUrl: state.setpageUrl,
    currentPageURL: state.currentPageURL,
    backPageButton: state.backPageButton,
    setBackPageButton: state.setBackPageButton,
    pageIndex: state.pageIndex,
    setPageIndex: state.setPageIndex,
    backButtonMode: state.backButtonMode,
    setBackButtonMode: state.setBackButtonMode,
    addCollegeFlag: state.addCollegeFlag,
    setAaddCollegeFlag: state.setAaddCollegeFlag,
  }));

  const [flag, SetFlag] = useState(false);
  const [urlPage, SetUrlPage] = useState(formURL);
  const [msg, SetMsg] = useState("");
  const [editAddMsg, SetEditAddMsg] = useState("Add");
  const [editAddMsgTitle, SetEditAddMsgTitle] = useState("");
  const [addOrEdit, SetAddOrEdit] = useState(false);
  const [obj, SetObj] = useState({});
  const [objFlag, SetObjFlag] = useState(Date.now());
  const [showBtnFlag, SetShowBtnFlag] = useState("");
  const [objQuest, SetObjQuest] = useState({});
  const [answers, setAnswers] = useState();
  const [showModal, setShowModal] = useState(false);
  const [isYesNoPage, SetIsYesNoPage] = useState(false);
  const [academicWarnMessage, setacademicWarnMessage] = useState(null);
  const [WarningInstitutionName, setWarningInstitutionName] = useState(null);
  const [disableBtn, SetdisableBtn] = useState(false);
  const setAnswer = async (questionId, answ, req, dis) => {
    let answer = Number.isInteger(answ) ? `${answ}` : answ;
    setAnswers({ ...answers, [questionId]: answer });
    if (req && req.length > 0) {
      req.map((r) => {
        let arr = r.condReqParVal.split(";");
        let cond = r.condOperator;
        objQuest?.FWFormItemList.map((q) => {
          if (r.requiredQuestionId === q.id) {
            if (cond === "=") {
              if (arr.includes(answer)) {
                q.required = "Y";
              } else {
                q.required = "N";
              }
            } else if (cond === ">") {
              if (arr.includes(answer)) {
                q.required = "Y";
              } else {
                q.required = "N";
              }
            }
          }
        });
      });
    }
    if (dis && dis.length > 0) {
      dis.map((d) => {
        let empty = "";
        let notin = "";
        if (d.condDisableParValEQ) {
          empty = d.condDisableParValEQ.split(";");
        }
        if (d.condDisableParValNOTEQ) {
          notin = d.condDisableParValNOTEQ.split(";");
        }

        objQuest?.FWFormItemList.map((o) => {
          var nm = o.id;
          if (d.disableQuestionId === o.id) {
            if (empty && empty.includes(answer)) {
              o.hideElem = true;
            } else if (notin && !notin.includes(answer)) {
              o.hideElem = true;
            } else {
              o.hideElem = false;
            }
          }
        });
      });
    }
  };
  const [yesNoErr, SetYesNoErr] = useState(false);
  const onFormSubmition = async (event) => {
    event.preventDefault();
    const obj = {};
    objQuest?.FWFormItemList.map((o) => {
      var nm = o.id;
      if (!o.hideElem && o.itemType !== "text") {
        if (o.format === "money") {
          let newVal = answers[nm].replace(/ /g, "");
          obj[nm] = newVal;
        } else {
          obj[nm] = answers[nm];
        }
      }
    });

    SetdisableBtn(true);
    if (isYesNoPage && answers.yes_no_button === "") {
      SetYesNoErr(
        "This field is required. Please select a response to continue."
      );
      return;
    }
    SetYesNoErr(null);
    setacademicWarnMessage(null);
    setWarningInstitutionName(null);
    const res = await saveFormPage(userForm, obj);
    if (res.status === "SUCCESS") {
      SetdisableBtn(false);
      SetFlag(false);
      const formUser = {
        Username: userForm.Username,
        UserId: userForm.UserId,
        AwardYear: userForm.AwardYear,
        ApplicationId: userForm.ApplicationId,
        OrgFormId: userForm.OrgFormId,
        FormId: userForm.FormId,
        PageName: "AcademicInfo4",
        MultipleParentName: userForm.MultipleParentName,
        Multiple: "N",
      };

      //setpageFormUrl(res.data.PageName);
      setacademicWarnMessage(res.data.AcademicWarningList);
      setWarningInstitutionName(res.data.WarningInstitutionName);
      if (addCollegeFlag) {
        setUserForm(formUser);
        navigate("/program");
      } else if (res.data.PageName === "AcademicInfo3a") {
        navigate("/school");
      } else if (res.data.PageName === "ApplicationChecklist") {
        navigate("/checkList");
      } else if (res.data.PageName === "AcademicInfo4") {
        navigate("/program");
      } else if (res.data.Multiple === "N") {
        navigate("/formpage");
      } else {
        SetObjFlag(Date.now());
        SetAddOrEdit(false);
      }
    } else if (res.status === "FAILED") {
      SetdisableBtn(false);
      if (res.data.ErrorMessage) {
        SetFlag(true);
        SetMsg(res.data.ErrorMessage);
      } else {
        if (res.data.QuestionErrorList.length > 0) {
          ValErrors(res.data.QuestionErrorList);
        }
      }
    } else if (res.status === "SERVERERROR") {
      SetdisableBtn(false);
      SetFlag(true);
      SetMsg(res.data);
    }
  };
  const ValErrors = (flObj) => {
    const failObj = obj.MultipleItemList.ItemList.map((el, idx) => {
      if (el.ItemNumber === itemNumberCheck) {
        el.FWFormItemList.map((elx) => {
          flObj.map((elem, ind) => {
            if (elem.Id === elx.id) {
              elx.validateErrMsg = elem.ErrorMsg;
              //elx.required = "Y"
            }
          });
        });
      }
      return el;
    });
    SetObj({ ...obj, questions: failObj });
  };
  const SaveContinue = async () => {
    setBackButtonMode(false);
    SetdisableBtn(true);
    const res = await saveFormPageMultiple(
      "FormPage/PostMultiplePageContinue",
      userForm
    );
    if (res.status === "SUCCESS") {
      SetdisableBtn(false);
      SetFlag(false);
      setUserForm({ ...res.data, Token: userForm.Token });
      setpageFormUrl(res.data.PageName);
      if (res.data.PageName === "AcademicInfo4") {
        navigate("/program");
      } else if (res.data.PageName === "ApplicationChecklist") {
        navigate("/checkList");
      } else if (res.data.Multiple === "N") {
        navigate("/formpage");
      } else {
        SetObjFlag(Date.now());
        SetAddOrEdit(false);
      }
    } else if (res.status === "FAILED") {
      SetdisableBtn(false);
      if (res.data.ErrorMessage) {
        SetFlag(true);
        SetMsg(res.data.ErrorMessage);
      } else {
        if (res.data.QuestionErrorList.length > 0) {
          ValErrors(res.data.QuestionErrorList);
        }
      }
    } else if (res.status === "SERVERERROR") {
      SetdisableBtn(false);
      SetFlag(true);
      SetMsg(res.data);
    }
  };
  useEffect(() => {
    const fetchData = async () => {
      if (!isUserLogedIn) {
        navigate("https://account.collegeboard.org");
      }
      setpageUrl("multiple");
      setAnswers({});
      setShowModal(true);
      const formUser = userForm;
      backPageButton.push(formUser);
      if (!backButtonMode) {
        setPageIndex(backPageButton.length - 2);
      }
      await axios({
        method: "post",
        url: process.env.API_URL + "FormPage/GetFormPageItems",
        data: formUser,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userForm.Token}`,
        },
        withCredentials: true,
      })
        .then((res) => {
          setShowModal(false);
          if (res.status === 200) {
            let str = `${res.data.PageHeading.split("(")[0]}`;
            SetEditAddMsgTitle(str);
            SetObj(res.data);
            SetShowBtnFlag(parseInt(res.data.MultipleItemCount));
            SetFlag(false);
            if (addCollegeFlag) {
              for (let el of res.data?.MultipleItemList?.ItemList) {
                if (el.ItemIdentifier === addCollegeFlag) {
                  EditItem(
                    el.ItemNumber,
                    addCollegeFlag,
                    el,
                    res.data.FWDictsList
                  );
                  return;
                }
              }
            }
            let frm = {
              ...formUser,
              MultipleParentName: res.data.MultipleParentName,
              StudentFirstName: res.data.StudentFirstName,
            };
            setUserForm(frm);
          } else if (res.data.returnMessage === "SERVERERROR") {
            SetFlag(true);
            SetMsg("Server error has occured.");
            return;
          } else if (res.data.ReturnMessage === "FAILED") {
            SetFlag(true);
            SetMsg("No data found.");
            return;
          }
        })
        .catch((err) => {
          SetMsg("Server error has occured.");
          setShowModal(false);
          return;
        });
    };
    fetchData();
    document.getElementById("scrollToTop").scrollIntoView();
    document.getElementById("CollegeBoardId").focus();
  }, [objFlag, urlPage]);

  const data = useStaticQuery(graphql`
    query {
      userEmpty: file(relativePath: { eq: "userEmpty.svg" }) {
        name
        publicURL
      }
      user: file(relativePath: { eq: "user.svg" }) {
        name
        publicURL
      }
    }
  `);
  const [itemNumberCheck, setitemNumberCheck] = useState(null);
  const EditItem = async (el, ItemIdentifier, elemObj, fwList) => {
    setitemNumberCheck(el);
    const fm = { ...userForm };
    delete fm.AddMultipleItemCount;
    delete fm.RemoveItemNumber;
    fm.EditMultipleItemNumber = el;
    fm.ItemIdentifier = ItemIdentifier;
    setUserForm(fm);
    //SetEditAddMsgTitle(`${obj.PageHeading.split("(")[0]}`)
    SetEditAddMsg("Update");
    let ob = [];
    if (elemObj) {
      ob.push(elemObj);
    } else {
      obj?.MultipleItemList?.ItemList.map((item, ind) => {
        if (item.ItemNumber === el) {
          ob.push(item);
        }
      });
    }
    const tmpUpdate = multiplePageFromJSON(
      ob[0],
      elemObj ? fwList.Dicts : obj.FWDictsList?.Dicts,
      true
    );
    setAnswers(tmpUpdate.questionAnswers);
    tmpUpdate?.FWFormItemListField?.map((question) => {
      if (question.dict === "YesNo_YN") {
        question.fieldType = "yesnobutton";
        if (question.required) SetIsYesNoPage(true);
      }
    });
    multiRequiredHiddenFields(tmpUpdate);
    SetObjQuest(tmpUpdate);
    SetAddOrEdit(true);
    document.getElementById("scrollToTop").focus();
    document.getElementById("CollegeBoardId").focus();
  };
  const goToNextPage = async (act, url, id) => {
    setShowModal(true);
    SetdisableBtn(true);
    const fm = { ...userForm };
    delete fm.EditMultipleItemNumber;
    if (act === "add") {
      delete fm.RemoveItemNumber;
      fm.AddMultipleItemCount = `${id}`;
    } else {
      delete fm.AddMultipleItemCount;
      fm.RemoveItemNumber = id;
    }
    const res = await saveFormPageMultiple("FormPage/" + url, fm);
    if (res.status === "SUCCESS") {
      SetdisableBtn(false);
      SetFlag(false);
      //setpageFormUrl(res.data.PageName);
      SetObj(res.data);
    } else if (res.status === "FAILED") {
      SetdisableBtn(false);
      SetFlag(true);
      SetMsg(res.data.ErrorMessage);
    } else if (res.status === "SERVERERROR") {
      SetdisableBtn(false);
      SetFlag(true);
      SetMsg(res.data);
    }
    let tellMore = document.getElementById("TellUsMoreAdded");
    setShowModal(false);
    if (act === "add" && tellMore) {
      tellMore.focus();
    } else {
      document.getElementById("CollegeBoardId").focus();
    }
  };
  const goToChecklist = async () => {
    //await dispatch({ type: "UPDATE_PAGE", payload: url })
    navigate("/checkList");
  };
  const GoBack = () => {
    SetAddOrEdit(false);
    SetObjFlag(Date.now());
  };
  const goBackToPage = async () => {
    setBackButtonMode(true);
    const goBack = backPageButton[pageIndex];
    setPageIndex(pageIndex - 1);
    setUserForm(goBack);
    setpageFormUrl(goBack.PageName);
    if (
      // goBack.PageName === "AcademicInfo3" ||
      goBack.PageName === "AcademicInfo3a"
    ) {
      navigate("/school");
    } else if (goBack.PageName === "AcademicInfo4") {
      navigate("/program");
    } else if (goBack.PageName === "Correction") {
      navigate("/payment");
    } else if (goBack.PageName === "AppCheckList") {
      navigate("/checkList");
    } else if (goBack.PageName === "ApplicationReview") {
      navigate("/appReview");
    } else if (goBack.PageName === "DataChecks") {
      navigate("/dataChecks");
    } else if (goBack.Multiple === "Y") {
      navigate("/multiple");
    } else {
      navigate("/formpage");
    }
  };
  return (
    <Layout>
      <Helmet
        htmlAttributes={{
          lang: "en",
        }}
        title={obj.PageHeading}
      />
      <div className="mx-auto space-y-5 bg-white p-7 md:max-w-lg md:p-11 md:shadow-md md:shadow-gray-500 mt-12 md:mt-6">
        <div className="font-bold md:text-sm text-md">
          <div tabindex="-1">
            <a
              href="javascript:void(0);"
              className="aStyle"
              onClick={() => goToChecklist()}
              id={`GoBackBtn1`}
            >
              ←Sections
            </a>{" "}
            <span>{` / ${obj.PageHeading}`}</span>
          </div>
        </div>
        <hr />
        {showModal ? <ProcessingModal /> : null}
        {flag && <ValidationError message={msg} />}
        {addOrEdit ? (
          <form
            onSubmit={(event) => onFormSubmition(event)}
            className="space-y-5"
          >
            {yesNoErr && <ValidationError message={yesNoErr} />}
            {objQuest.SectionHeading ? (
              <div className=" md:text-lg text-xl font-bold">
                {parse(objQuest.SectionHeading)}
              </div>
            ) : null}
            {objQuest?.heading?.textContent && (
              <h2 className=" md:text-xl text-lg">
                {parse(objQuest.heading.textContent)}
              </h2>
            )}
            {objQuest?.FWFormItemList.map((question, currentPosition) => {
              const qs =
                question.itemType !== "text" && !question.hideElem ? (
                  <Question
                    key={question.id}
                    {...question}
                    label={question.textContent}
                    dicts={objQuest.dicts}
                    error={question.validateErrMsg}
                    value={answers ? answers[question.id] : ""}
                    onChange={(value) =>
                      setAnswer(
                        question.id,
                        value,
                        question.conditionalRequireQuestions,
                        question.conditionalDisableQuestions
                      )
                    }
                    onClick={(value) =>
                      setAnswer(
                        question.id,
                        value,
                        question.conditionalRequireQuestions,
                        question.conditionalDisableQuestions
                      )
                    }
                  />
                ) : null;
              return qs;
            })}
            <Button
              key={`${objQuest.ItemDesc}`}
              disabled={disableBtn}
              className="w-full md:w-fit"
            >{`Save and Continue`}</Button>
            {pageIndex > 0 ? (
              <>
                <hr />
                <a
                  href="javascript:void(0);"
                  className="aStyle md:text-sm text-md grid justify-items-end"
                  onClick={() => SetAddOrEdit(false)}
                >
                  ←Go Back
                </a>{" "}
              </>
            ) : null}
          </form>
        ) : (
          <>
            <form className="space-y-5">
              {obj.PageHeading && (
                <h2 className="font-bold text-1xl md:text-lg">
                  {obj.PageHeading}
                </h2>
              )}
              {obj?.StudentFirstName &&
              obj?.PageName == "HouseholdDetermination1" ? (
                <h3 className="font-semibold text-lg md:text-md">
                  Who are {parse(obj.StudentFirstName)} parents?
                </h3>
              ) : null}
              {obj.SectionHeading && (
                <div className="space-y-3 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50 p-3">
                  {parse(obj.SectionHeading)}
                </div>
              )}
              {obj?.MultipleItemList?.ItemList?.map((question, idx) => {
                const qs =
                  question.Action !== "deleted" ? (
                    <div
                      key={question.ItemNumber}
                      className={[
                        "border-gray  rounded-md border-2 border-solid bg-slate-50",
                      ].join(" ")}
                    >
                      <p className="p-3 flex flex-row ">
                        <img
                          className="text-black inline"
                          src={
                            question.ItemComplete === "N"
                              ? process.env.IMG_URL + data.userEmpty.publicURL
                              : process.env.IMG_URL + data.user.publicURL
                          }
                          alt="User Icon"
                          height={16}
                          width={16}
                        />
                        <div className="w-96">
                          <h2
                            className="ml-2 font-bold text-black"
                            // style={{ wordBreak: "break-all" }}
                          >
                            {question.ItemDescription
                              ? question.ItemDescription
                              : `${obj.PageHeading} ${question.ItemNumber}`}
                          </h2>
                        </div>
                      </p>
                      <div className={"p-3"}>
                        <Button
                          type="button"
                          id={`${
                            question.ItemComplete === "N"
                              ? "TellUsMoreAdded"
                              : null
                          }`}
                          className="px-5"
                          onClick={() =>
                            EditItem(
                              question.ItemNumber,
                              question.ItemIdentifier
                            )
                          }
                          aria-label={`Tell us more about ${question.ItemDescription}`}
                        >
                          Tell us more
                        </Button>
                        {academicWarnMessage && academicWarnMessage.length > 0
                          ? academicWarnMessage.map((el, idx) => {
                              if (
                                el.WarningInstitutionName ===
                                question.ItemDescription
                              ) {
                                return (
                                  <div
                                    key={idx}
                                    className="space-y-3 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50 p-5 mt-3"
                                  >
                                    <p>
                                      <strong>
                                        {el.WarningInstitutionName}
                                      </strong>
                                    </p>
                                    <p>{parse(el.AcademicWarningMessage)}</p>
                                  </div>
                                );
                              }
                            })
                          : false}
                        {obj.MultipleParentName === "APPLICATIONSCHOOLS" ||
                        obj.AllowMultipleItemRemove !== "Y" ? null : (
                          <Button
                            type="button"
                            className="ml-3 px-5"
                            variant="secondary"
                            disabled={disableBtn}
                            onClick={() =>
                              goToNextPage(
                                "remove",
                                "PostRemoveMultipleItem",
                                question.ItemNumber
                              )
                            }
                            aria-label={`Remove ${question.ItemDescription}`}
                          >
                            Remove
                          </Button>
                        )}
                        {question.ItemComplete === "N" && (
                          <h3 className="font-semibold text-lg md:text-md">
                            {obj.IncompleteErrorMessage}{" "}
                            {question.ItemDescription}
                          </h3>
                        )}
                      </div>
                    </div>
                  ) : null;
                return qs;
              })}
              {obj.AllowMultipleItemAdd === "Y" ? (
                <p>
                  <Button
                    type="button"
                    variant="secondary"
                    className="w-full md:w-fit break-words"
                    onClick={() =>
                      goToNextPage("add", "PostAddMultipleItem", 1)
                    }
                  >
                    {obj.AddMultipleItemButtonLabel}
                  </Button>
                </p>
              ) : null}

              {Object.keys(obj).length === 0 ||
              obj.MultipleItemIncomplete === "Y" ? null : (
                <>
                  {formURL === "HouseholdDetermination1" ? (
                    <div className="space-y-3 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50  p-5">
                      <p>
                        <strong>{"Warning: "}</strong>Changing parents may make
                        some information invalid. You may have to answer some
                        questions again.
                      </p>
                    </div>
                  ) : (
                    false
                  )}
                  <Button
                    type="button"
                    onClick={() => SaveContinue()}
                    className="w-full md:w-fit"
                    disabled={disableBtn}
                  >
                    {"Save and Continue"}
                  </Button>
                </>
              )}
            </form>
            <hr />
            {pageIndex > 0 ? (
              <a
                href="javascript:void(0);"
                className="aStyle md:text-sm text-md grid justify-items-end"
                onClick={() => goBackToPage()}
              >
                ←Go Back
              </a>
            ) : null}
          </>
        )}
      </div>
    </Layout>
  );
};

export default WithLocation(MultiReviewPage);
