import React, { use<PERSON>ontext, useEffect, useState } from "react";
import { navigate } from "gatsby";
import { GetPdfUrl } from "../components/NewComp";
import { Label } from "../components/inputs/Label";
import { TextInput } from "../components/inputs/TextInput";
import Layout from "../components/layout";
import axios from "axios";
import Helmet from "react-helmet";
import { Button } from "../components/Button";
import { ValidationError } from "../components/ValidationError";
import { ProcessingModal } from "../components/processingModal";
import { ConfirmModal } from "../components/ConfirmModal";
import CollegeTable from "../components/CollegeTable";
import DocTable from "../components/DocTable";
import useUserStore from "../utils/storeApp";
import WithLocation from "../components/withLocation";
import parse from "html-react-parser";

const Dashboard = () => {
  const {
    setUserLogedIn,
    setpageFormUrl,
    userForm,
    isUserLogedIn,
    formURL,
    setUserForm,
    updateUserForm,
    setpageUrl,
    appPool,
    setBackPageButton,
    setPageIndex,
    setDollarFormat,
  } = useUserStore((state) => ({
    setUserLogedIn: state.setUserLogedIn,
    setpageFormUrl: state.setpageFormUrl,
    setUserForm: state.setUserForm,
    userForm: state.userForm,
    formURL: state.formURL,
    isUserLogedIn: state.isUserLogedIn,
    updateUserForm: state.updateUserForm,
    setpageUrl: state.setpageUrl,
    appPool: state.appPool,
    setBackPageButton: state.setBackPageButton,
    setPageIndex: state.setPageIndex,
    setDollarFormat: state.setDollarFormat,
  }));

  const [flag, SetFlag] = useState(false);
  const [urlPage, SetUrlPage] = useState("");
  const [msg, SetMsg] = useState("");
  const [formData, SetFormData] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [showModalConf, setShowModalConf] = useState(false);
  const [answers, setAnswers] = useState();
  const [actionUrl, SetactionUrl] = useState("");
  const [modalType, SetmodalType] = useState("");
  const [objFlag, SetObjFlag] = useState(Date.now());
  const [disableBtn, SetdisableBtn] = useState(false);
  const [openConfirm, setOpenConfirm] = React.useState(false);
  const [optOutConfirm, setOptOutConfirm] = React.useState(true);
  const [confirmFlag, setConfirmFlag] = React.useState(true);
  const [showColoradoMsg, setShowColoradoMsg] = React.useState(false);

  const setAnswer = (questionId, answer) => {
    setAnswers({ ...answers, [questionId]: answer });
  };
  useEffect(() => {
    const fetchData = async () => {
      if (!isUserLogedIn) {
        navigate("https://account.collegeboard.org");
      }
      setpageUrl("dashboard");
      setShowModal(true);
      const formUser = {
        Username: userForm.Username,
        UserId: userForm.UserId,
        AwardYear: userForm.AwardYear,
        ApplicationId: userForm.ApplicationId,
        OrgFormId: userForm.OrgFormId,
        FormId: userForm.FormId,
        PageName: "Dashboard",
      };
      document.getElementById("scrollToTop").scrollIntoView();
      document.getElementById("scrollToTop").focus();
      await axios({
        method: "post",
        url: process.env.API_URL + "dashboard/GetDashboard",
        data: formUser,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userForm.Token}`,
        },
        withCredentials: true,
      })
        .then((res) => {
          setShowModal(false);
          if (res.data.ReturnMessage.toUpperCase() === "SUCCESS") {
            const js = res.data;
            SetFormData(js);
            SetFlag(false);
            setDollarFormat(js.CurrencyUSDorCA === "Y" ? true : false);
          } else if (res.data.ReturnMessage.toUpperCase() === "SERVERERROR") {
            SetFlag(true);
            SetMsg("Server error has occured.");
            return;
          } else if (res.data.ReturnMessage.toUpperCase() === "FAILED") {
            SetFlag(true);
            SetMsg("No data found.");
            return;
          }
        })
        .catch((err) => {
          SetMsg("Server error has occured.");
          setShowModal(false);
          return;
        });
      setShowModal(false);
    };
    fetchData();
    setConfirmFlag(true);
  }, [objFlag]);
  const handleApiCalls = async (req) => {
    setShowModalConf(false);
    SetdisableBtn(true);
    setShowModal(true);
    const formUser = {
      Username: userForm.Username,
      UserId: userForm.UserId,
      AwardYear: userForm.AwardYear,
      ApplicationId: userForm.ApplicationId,
      OrgFormId: userForm.OrgFormId,
      FormId: userForm.FormId,
      PageName: "Dashboard",
    };
    if (req === "PostSendParentEmail") {
      formUser.ParentEmail = document.getElementById("ParentEmail").value;
    } else if (req === "PostCancelCorrection") {
      formUser.CorrectionInProgressId = formData.CorrectionInProgressId;
    }
    await axios({
      method: "post",
      url: process.env.API_URL + "dashboard/" + req,
      data: formUser,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userForm.Token}`,
      },
    })
      .then((res) => {
        setShowModal(false);
        if (res.data.ReturnMessage.toUpperCase() === "SUCCESS") {
          SetdisableBtn(false);
          SetFlag(false);
          const js = res.data;
          setpageFormUrl(js.PageName);
          setUserForm(js);
          if (js.PageName === "AcademicInfo4") {
            navigate("/program");
          } else if (js.PageName === "Dashboard") {
            SetObjFlag(Date.now());
          }
        } else if (res.data.returnMessage.toUpperCase() === "SERVERERROR") {
          SetdisableBtn(false);
          SetFlag(true);
          SetMsg("Server error has occured.");
          return;
        } else if (res.data.ReturnMessage.toUpperCase() === "FAILED") {
          SetdisableBtn(false);
          SetFlag(true);
          SetMsg(res.data.ErrorMessage);
          return;
        }
      })
      .catch((err) => {
        SetMsg("Server error has occured.");
        SetdisableBtn(false);
        setShowModal(false);
        return;
      });
    setShowModal(false);
    document.getElementById("scrollToTop").scrollIntoView();
    document.getElementById("scrollToTop").focus();
  };
  const goToNextPage = (page, menu, url) => {
    const formUser = {
      ...userForm,
      MenuName: menu,
      PageName: page,
      Multiple: "N",
    };
    setUserForm(formUser);
    setpageFormUrl(page);
    navigate("/" + url);
  };
  const Component = ({ name, id, children, isRequired }) => {
    return (
      <>
        <Label required={isRequired} for={id}>
          {name}
        </Label>
        {children}
      </>
    );
  };
  const getIDOCFunc = async () => {
    const urlID = await axios({
      method: "get",
      url: process.env.IDOC_URL,
    }).then((res) => {
      SetactionUrl(res.data);
      document.getElementById("IDOCForm").action = res.data;
      document.forms["IDOCForm"].submit();
    });
  };
  const handleOptOutCall = async () => {
    setShowColoradoMsg(false);
    SetdisableBtn(true);
    setShowModal(true);
    SetFlag(false);
    const formUser = {
      Username: userForm.Username,
      UserId: userForm.UserId,
      AwardYear: userForm.AwardYear,
      ApplicationId: userForm.ApplicationId,
      OrgFormId: userForm.OrgFormId,
      FormId: userForm.FormId,
    };
    await axios({
      method: "post",
      url: process.env.API_URL + "dashboard/PostSendOptOutEmail",
      data: formUser,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userForm.Token}`,
      },
    })
      .then((res) => {
        setShowModal(false);
        if (res.data.ReturnMessage.toUpperCase() === "SUCCESS") {
          SetdisableBtn(false);
          setShowColoradoMsg(true);
        } else if (res.data.ReturnMessage.toUpperCase() === "SERVERERROR") {
          SetdisableBtn(false);
          SetFlag(true);
          SetMsg("Server error has occured.");
          return;
        } else if (res.data.ReturnMessage.toUpperCase() === "FAILED") {
          SetdisableBtn(false);
          SetFlag(true);
          SetMsg(res.data.ErrorMessage);
          return;
        }
      })
      .catch((err) => {
        SetMsg("Server error has occured.");
        SetdisableBtn(false);
        setShowModal(false);
        return;
      });
    setShowModal(false);
  };
  const handleLogin = () => {
    let AppId = userForm.ApplicationId;
    setUserLogedIn(false);
    setUserForm({});
    setBackPageButton([]);
    setpageFormUrl("");
    setPageIndex("");
    setpageUrl("");
    navigate(
      "https://account.collegeboard.org/login/logout?appId=" +
        AppId +
        "&DURL=https://student.collegeboard.org/css-financial-aid-profile"
    );
  };
  const onFormSubmition = async (event) => {
    event.preventDefault();
    handleApiCalls("PostSendParentEmail");
  };

  const getModalConfirm = (txt) => {
    let msg = "";
    if (txt === "PostCancelCorrection") {
      msg =
        "If you cancel your correction before submitting your changes, the information you have entered will be lost.  Do you wish to continue with your cancellation?";
    } else {
      msg =
        'Do you wish to continue with your cancellation and remove all colleges or programs in "Selected Colleges or Programs"? ';
    }
    SetMsg(msg);
    SetmodalType(txt);
    setShowModalConf(true);
  };
  const CloseMdl = (ev) => {
    if (ev === "cancel") {
      setShowModalConf(false);
    } else {
      handleApiCalls(ev);
    }
  };
  const GetProfilePDF = async () => {
    setShowModal(true);
    const frm = {
      Username: userForm.Username,
      UserId: userForm.UserId,
      AwardYear: userForm.AwardYear,
      ApplicationId: userForm.ApplicationId,
      OrgFormId: userForm.OrgFormId,
      FormId: userForm.FormId,
      PageName: "Dashboard",
    };
    GetPdfUrl("GetProfilePDF", frm);
    setShowModal(false);
  };
  return (
    <Layout>
      <Helmet
        htmlAttributes={{
          lang: "en",
        }}
        title={formData.PageName}
      />
      <div
        className="mx-auto space-y-5 bg-white p-7 text-md md:max-w-3xl md:p-11  mt-12 md:mt-6"
        id="dashboard"
      >
        {showModal ? <ProcessingModal /> : null}
        {showModalConf ? (
          <ConfirmModal
            msg={msg}
            type={modalType}
            onClick={(ev) => CloseMdl(ev)}
          />
        ) : null}
        {flag && <ValidationError message={msg} />}
        <div className="space-y-1">
          {/* <h1>Not Cached Site</h1> */}
          <h1
            className="font-bold md:text-xl text-lg"
            id="GoBackBtn1"
            tabindex="-1"
          >
            {/* <a
              href="#nolinkID"
              id="GoBackBtn1"
              className="cursor-text"
            > */}
            {formData.PageName}
            {/* </a> */}
          </h1>
          <p>{`Welcome ${formData.FirstName} ${formData.LastName}!`}</p>
          <p>{parse(`Academic Year <b>${formData.AcademicYear}</b>`)}</p>
          <p>
            Your CBFinAid ID: <b>{formData.CBFinAidID}</b>
          </p>
        </div>
        <div className="space-y-5">
          <div
            key={"AppStatus"}
            className="mx-auto space-y-2 rounded-sm border-2 md:shadow-md md:shadow-gray-500 bg-white p-3"
          >
            <div>
              <h2 className="text-xl md:text-lg font-bold">
                Application Status
              </h2>
            </div>
            <div>
              {formData.ApplicationStatus === "Inprogress"
                ? "In Progress"
                : null}
              {formData.ApplicationStatus === "Submitted" ? (
                <>
                  <p>
                    Submitted on <b>{formData.SubmitDate}</b>
                  </p>
                  {formData.CorrectionSubmitDate ? (
                    <p>
                      Correction Submitted on{" "}
                      <b>{formData.CorrectionSubmitDate}</b>
                    </p>
                  ) : null}
                  <p>
                    <a
                      href="#nolinkID"
                      className="aStyle text-md"
                      onClick={() => GetProfilePDF()}
                    >
                      Save a Copy
                    </a>
                  </p>
                  {formData.AwardYear === "2025" ? (
                    <p>
                      <a
                        href="https://education.co1.qualtrics.com/jfe/form/SV_3mYWeS6jzOMPUJE"
                        className="aStyle text-md"
                        target="_blank"
                      >
                        Click here 
                      </a>{" "}
                      to complete a 5-minute survey to tell us about your
                      experience.
                    </p>
                  ) : formData.AwardYear === "2026" ? (
                    <p>
                      <a
                        href="https://education.co1.qualtrics.com/jfe/form/SV_cNDiQgEQyHdieV0"
                        className="aStyle text-md"
                        target="_blank"
                      >
                        Click here 
                      </a>{" "}
                      to complete a 5-minute survey to tell us about your
                      experience.
                    </p>
                  ) : null}
                </>
              ) : null}
            </div>
            {formData.ApplicationStatus === "Inprogress" ? (
              <Button
                className="text-center w-full md:w-fit"
                onClick={() => navigate("/checkList")}
              >
                {"Continue Application"}
              </Button>
            ) : null}
          </div>
          {formData.DisplayFeeWaiverEligible === "Y" ? (
            <div
              key={"DisplayFeeWaiverEligible"}
              className="mx-auto space-y-2 rounded-sm border-2 md:shadow-md md:shadow-gray-500 bg-white p-3"
            >
              <div>
                <p className="text-xl md:text-lg font-bold">
                  Fee Waiver Eligible
                </p>
              </div>
              <div>{formData.FeeWaiverText}</div>
            </div>
          ) : null}
          {parseInt(formData.CreditCardPaymentCount) > 0 ? (
            <div
              key={"Payment"}
              className="mx-auto space-y-2 rounded-sm border-2 md:shadow-md md:shadow-gray-500 bg-white p-3"
            >
              <div>
                <p className="text-xl md:text-lg font-bold">Payment</p>
              </div>
              {formData.CreditCardPaymentList?.map((elx) => (
                <div key={elx.TransactionStatus}>
                  {parse(`Payment Received: <b>$${elx.TransactionAmount}</b>`)}
                </div>
              ))}
            </div>
          ) : null}
          {formData.DisplayCorrection === "Y" ? (
            <div
              key={"DisplayCorrection"}
              className="mx-auto space-y-2 rounded-sm border-2 md:shadow-md md:shadow-gray-500 bg-white p-3"
            >
              <h2 className="text-xl md:text-lg font-bold">
                {"Correct Your CSS Profile"}
              </h2>
              <div>
                {formData.CorrectionStatus ? (
                  <p className="text-xl md:text-lg font-bold">
                    {formData.CorrectionStatus}
                  </p>
                ) : null}
              </div>
              <div>
                <Button
                  className="text-center w-full md:w-fit"
                  onClick={() => goToNextPage("Correction", "", "payment")}
                >
                  {"Correct Your CSS Profile"}
                </Button>
              </div>
              {formData.DisplayCorrectionCancel === "Y" ? (
                <div>
                  <Button
                    className="text-center w-full md:w-fit"
                    variant="secondary"
                    onClick={() => getModalConfirm("PostCancelCorrection")}
                  >
                    {"Cancel Correction"}
                  </Button>
                </div>
              ) : null}
            </div>
          ) : null}

          {formData.CollegeList ? (
            <div
              key={"CollegePrograms"}
              className="mx-auto space-y-5 rounded-sm border-2 md:shadow-md md:shadow-gray-500 bg-white p-3"
            >
              <h2 className="text-xl md:text-lg font-bold">
                {"Colleges & Programs"}
              </h2>
              {parseInt(formData.CollegeCount) > 0 ? (
                <CollegeTable CollegeList={formData?.CollegeList} />
              ) : null}
              {formData.DisplayAddCollege === "Y" ? (
                <>
                  {!openConfirm ? (
                    <div>
                      <Button
                        className="text-center w-full md:w-fit"
                        disabled={disableBtn}
                        onClick={() => {
                          setOpenConfirm(true);
                        }}
                      >
                        {"Add a College or Program"}
                      </Button>
                    </div>
                  ) : (
                    <>
                      {" "}
                      <div className="space-y-3 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50  p-5 text-base">
                        Next, you'll be selecting which schools you want to send
                        your CSS Profile to. Once your selections are added,
                        your application will be tailored to ask questions
                        required by each college or program. Be sure to review
                        all sections and resubmit your application to any new
                        colleges or programs you have added.
                      </div>
                      <div>
                        <Button
                          type="button"
                          className="w-full md:w-fit"
                          onClick={(el) => {
                            handleApiCalls("PostAddCollege");
                          }}
                        >
                          Confirm
                        </Button>
                        <Button
                          type="button"
                          className="w-full md:w-fit ml-2"
                          onClick={(el) => {
                            setOpenConfirm(false);
                          }}
                        >
                          Cancel
                        </Button>
                      </div>
                    </>
                  )}
                </>
              ) : null}
              {formData.DisplayAddCollegeCancel === "Y" ? (
                <div>
                  <Button
                    className="text-center w-full md:w-fit"
                    variant="secondary"
                    onClick={() => getModalConfirm("PostCancelAddCollege")}
                  >
                    {"Cancel College/Program Selection"}
                  </Button>
                </div>
              ) : null}
            </div>
          ) : null}
          {formData.DisplayIDOCSchoolApplied === "Y" && (
            <div
              key={"NextSteps"}
              className="mx-auto space-y-5 rounded-sm border-2 md:shadow-md md:shadow-gray-500 bg-white p-3"
            >
              <div className="space-y-2">
                <h2 className="text-xl md:text-lg font-bold">
                  Institutional Documentation Service (IDOC)
                </h2>
                <div class="accordion-content-inner">
                  <Button
                    disabled={disableBtn}
                    className="text-center w-full md:w-fit"
                    onClick={() => getIDOCFunc()}
                  >
                    {"Submit your Documents"}
                  </Button>
                </div>
                <div className="space-y-3 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50  p-5 text-base">
                  <p>
                    <strong>{"Please note: "}</strong> After you submit your CSS
                    Profile, we start the preparation of your IDOC dashboard.
                    During peak times it may take up to a few hours for your
                    dashboard to be created. You will be sent an email
                    notification when it is available.
                  </p>
                  <p>
                    <ValidationError message={`IDOC Processing Alert:`} />
                  </p>
                  <p>
                    IDOC processing takes{" "}
                    <strong>{"3-5 business days "}</strong>after you upload.
                    <br />
                    Your IDOC dashboard reflects the status of your uploaded
                    documents. Once a document is processed, it moves into the
                    “Processed Documents” section of your dashboard and is no
                    longer listed as an outstanding required document.
                    <br />
                    <strong>
                      {
                        "Please do not upload documents multiple times while you wait for them to process."
                      }
                    </strong>{" "}
                    Schools are provided the date you uploaded materials.
                  </p>
                </div>
                <div>
                  <p>
                    Your CBFinAid ID: <b>{formData.IDOCPostIDOCID}</b>
                  </p>
                </div>
              </div>
            </div>
          )}
          {formData?.NonIDOCCollegeList?.length > 0 ? (
            <div
              key={"DisplayFeeWaiverEligible"}
              className="mx-auto space-y-2 rounded-sm border-2 md:shadow-md md:shadow-gray-500 bg-white p-3"
            >
              <div>
                <p className="text-xl md:text-lg font-bold">
                  Colleges and Programs Not Utilizing IDOC
                </p>
              </div>
              <div>
                <p>
                  The colleges and programs listed below may require additional
                  information to complete your financial aid application but do
                  not utilize IDOC.{" "}
                  <span className="font-bold">
                    Submitting documents to the College Board through IDOC will
                    not meet requirements for the schools listed below.
                  </span>
                </p>
                <p>
                  Students should visit each website for the college or
                  program(s) listed below for further information and
                  instructions about any application requirements and the
                  process to submit.
                </p>
              </div>
              {formData.NonIDOCCollegeList.map((el) => (
                <div className="space-x-60 font-bold">
                  <span>{el.InstitutionName}</span>
                  <span>{el.CSSCode}</span>
                </div>
              ))}
            </div>
          ) : null}
          {formData.DisplayEmailOtherParent === "Y" ? (
            <div
              key={"NextSteps3"}
              className="mx-auto space-y-5 rounded-sm border-2 md:shadow-md md:shadow-gray-500 bg-white p-3"
            >
              <div className="space-y-2">
                <h2 className="text-xl md:text-lg font-bold">Other Parent</h2>
                <p>
                  At least one of your colleges or programs requires that both
                  of your biological/adoptive parents, not just the one who
                  completed this application, submit the CSS Profile
                  application.
                </p>
                <div className="space-y-3 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50  p-5 text-base">
                  <strong>{"Please note: "}</strong>If you would like College
                  Board to send your other parent an email to let them know,
                  please provide their email address.
                </div>
                <form
                  onSubmit={(event) => onFormSubmition(event)}
                  className="space-y-5"
                >
                  <Component
                    name="Enter parent’s email address"
                    id="ParentEmail"
                  >
                    <TextInput
                      id="ParentEmail"
                      onChange={(event) => console.log()}
                      type="email"
                      required
                    />
                  </Component>
                  <Button
                    type="submit"
                    disabled={disableBtn}
                    className="w-full md:w-fit"
                  >
                    Submit Email Address
                  </Button>
                </form>
              </div>
            </div>
          ) : null}
        </div>
        {formData.DisplayColoradoOptOut === "Y" ? (
          <div className="space-y-5">
            <div
              key={"OptOut"}
              className="mx-auto space-y-2 rounded-sm border-2 md:shadow-md md:shadow-gray-500 bg-white p-3"
            >
              <div>
                <h2 className="text-xl md:text-lg font-bold">
                  Attention Colorado Residents
                </h2>
              </div>
              <div>
                COLORADO RESIDENTS: If you're a resident of CO who submitted a
                CSS Profile application and provided your consent for College
                Board to process your citizenship status, you can withdraw your
                consent at any time by clicking on the “Withdraw Consent” button
                below. This will result in deletion of your CSS Profile
                application and the data you provided through the online
                application. In addition, you have the right to request College
                Board to delete your personally identifiable information, with
                further information including how to submit a request for
                deletion available in College Board’s Privacy Statement at{" "}
                <a
                  href="https://privacy.collegeboard.org/privacy-statement"
                  className="cursor-pointer text-sky-700 underline font-bold"
                  target="_blank"
                >
                  https://privacy.collegeboard.org/privacy-statement.
                </a>
              </div>
              {showColoradoMsg ? (
                <div className="space-y-3 rounded-md border-2 border-solid border-green-600 bg-green-50  p-5 text-base">
                  Your request to delete data submitted to CSS Profile has been
                  received. Deletion will take place within 45 days and you will
                  receive a confirmation via email upon completion.{" "}
                </div>
              ) : null}
              {formData.DisplayColoradoOptOutButton === "Y" ? (
                <div>
                  {optOutConfirm ? (
                    !confirmFlag ? null : (
                      <Button
                        type="button"
                        className="w-full md:w-fit"
                        onClick={(el) => {
                          setOptOutConfirm(false);
                          setShowColoradoMsg(false);
                        }}
                      >
                        Withdraw Consent
                      </Button>
                    )
                  ) : (
                    <div className="space-y-2">
                      <div className="space-y-3 rounded-md border-2 border-solid border-yellow-600 bg-yellow-50  p-5 text-base">
                        By clicking "Confirm" I acknowledge that I am a Colorado
                        resident requesting that all information I submitted to
                        CSS Profile be deleted. I understand that once deleted
                        CSS Profile data cannot be restored. Deletion will take
                        place within 45 days and a confirmation will be sent via
                        email after deletion.
                      </div>
                      <div>
                        <Button
                          type="button"
                          className="w-full md:w-fit"
                          disabled={disableBtn}
                          onClick={(el) => {
                            handleOptOutCall("PostAddCollege");
                            setOptOutConfirm(true);
                            setConfirmFlag(false);
                          }}
                        >
                          Confirm
                        </Button>
                        <Button
                          type="button"
                          className="w-full md:w-fit ml-2"
                          onClick={(el) => {
                            setOptOutConfirm(true);
                          }}
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-3 rounded-md border-2 border-solid border-green-600 bg-green-50  p-5 text-base">
                  Your request to delete data submitted to CSS Profile has been
                  received. Deletion will take place within 45 days and you will
                  receive a confirmation via email upon completion.{" "}
                </div>
              )}
            </div>
          </div>
        ) : null}
      </div>
      <div className="hidden">
        <form
          name="IDOCForm"
          id="IDOCForm"
          target="_blank"
          action={
            "https://idocbeta.collegeboard.org/IDOCBeta/IDOC/IndexIDOC.aspx"
          }
          method="Post"
          type="hidden"
        >
          <input type="text" name="dob" value={formData.IDOCPostDOB} />
          <input type="text" name="idocid" value={formData.IDOCPostIDOCID} />
          <input type="text" name="user" value={formData.Username} />
          <input type="text" name="IDOCYear" value={formData.AwardYear} />
          <input type="text" name="origin" value="PROFILE" />
          <input type="text" name="SSO" value="Y" />
        </form>
      </div>
    </Layout>
  );
};

export default WithLocation(Dashboard);
