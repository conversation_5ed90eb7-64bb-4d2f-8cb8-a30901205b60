import React from "react";
import parse from "html-react-parser";
export function ReviewCard({
  currentPosition,
  pageTo,
  heading,
  showLink,
  list,
  multiList,
  onClick,
  format,
}) {
  const hideSSN = (vl) => {
    let st = vl.replace(/.(?=.{4})/g, "X");
    let str = st.substring(0, 3) + "-" + st.substring(3, st.length);
    let newStr = str.substring(0, 6) + "-" + str.substring(6, str.length);
    return newStr;
  };
  return (
    <div className="mx-auto space-y-5 rounded-md  border-2 md:shadow-md md:shadow-gray-500 bg-white p-5">
      <div>
        <h2 className="text-xl md:text-lg font-bold">{heading}</h2>
      </div>
      {list.map((el) => (
        <div key={el.id}>
          <div className="font-bold text-lg md:text-md">
            {parse(el.textContent)}
          </div>
          <div className="font-bold text-sky-700 break-words">
            {el.questionValue
              ? el.format === "ssn"
                ? hideSSN(el.questionValue)
                : el.questionValue === "Y"
                ? "Yes"
                : el.questionValue === "N"
                ? "No"
                : el.questionValue
              : "-"}
          </div>
        </div>
      ))}
      {multiList.ItemList.map((el) => (
        <div key={el.ItemNumber}>
          <div>
            <p className="text-xl md:text-lg font-bold text-program-core-higher-ed mb-4">
              {el.ItemDescription}
            </p>
          </div>
          {el?.FWFormItemList?.map((elx) =>
            elx.itemType !== "text" ? (
              <div key={elx.id}>
                <div className="font-bold text-lg md:text-md">
                  {parse(elx.textContent)}
                </div>
                <div className="font-bold text-sky-700 break-words">
                  {elx.questionValue
                    ? elx.format === "ssn"
                      ? hideSSN(elx.questionValue)
                      : elx.questionValue
                    : "-"}
                </div>
              </div>
            ) : null
          )}
        </div>
      ))}
      {showLink === "Y" ? (
        <div onClick={() => onClick()} className="text-center">
          <a
            href="javascript:void(0);"
            className="aStyle"
            aria-label={`Edit this section - ${heading}`}
            // id={`GoBackBtn${currentPosition}`}
          >
            Edit this section
          </a>
        </div>
      ) : null}
    </div>
  );
}
