<svg width="71" height="71" viewBox="0 0 71 71" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="35.5" cy="35.5" r="35.5" fill="#49B2FA"/>
<circle cx="35.6983" cy="35.6983" r="26.9721" stroke="#098EE9" stroke-width="3.17318"/>
<g filter="url(#filter0_di_1413_7495)">
<path d="M34.7456 16.998C34.9831 16.2671 36.017 16.2671 36.2545 16.998L39.9972 28.5169C40.1034 28.8437 40.408 29.065 40.7517 29.065H52.8634C53.6318 29.065 53.9514 30.0484 53.3296 30.5001L43.5311 37.6192C43.2531 37.8212 43.1367 38.1792 43.2429 38.5061L46.9856 50.025C47.2231 50.7558 46.3866 51.3636 45.7649 50.9119L35.9663 43.7928C35.6883 43.5908 35.3118 43.5908 35.0338 43.7928L25.2352 50.9119C24.6135 51.3636 23.777 50.7558 24.0145 50.025L27.7572 38.5061C27.8634 38.1792 27.747 37.8212 27.469 37.6192L17.6704 30.5001C17.0487 30.0484 17.3683 29.065 18.1367 29.065H30.2484C30.5921 29.065 30.8967 28.8437 31.0029 28.5169L34.7456 16.998Z" fill="#F2C831"/>
</g>
<defs>
<filter id="filter0_di_1413_7495" x="17.342" y="16.4498" width="38.2994" height="36.6004" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.98324" dy="1.98324"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.526533 0 0 0 0 0.518438 0 0 0 0 0.923229 0 0 0 0.28 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1413_7495"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1413_7495" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.793296" dy="1.58659"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.51 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1413_7495"/>
</filter>
</defs>
</svg>
