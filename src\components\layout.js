import PropTypes, { string } from "prop-types";
import { navigate, Script } from "gatsby";
import React, { useState, useEffect } from "react";
import { globalHistory } from "@reach/router";
import Header from "./header";
import { useIdleTimer } from "react-idle-timer";
import Helmet from "react-helmet";
import useUserStore from "../utils/storeApp";

function Layout({ children }) {
  const timeout = 60000 * 30;
  const [remaining, setRemaining] = useState(timeout);
  const [elapsed, setElapsed] = useState(0);
  const [lastActive, setLastActive] = useState(+new Date());
  const [isIdle, setIsIdle] = useState(false);
  const handleOnActive = () => setIsIdle(false);

  const {
    isUserLogedIn,
    userForm,
    setUserLogedIn,
    setUserForm,
    setpageFormUrl,
    setpageUrl,
    setBackPageButton,
    setPageIndex,
    appPool,
  } = useUserStore((state) => ({
    isUserLogedIn: state.isUserLogedIn,
    userForm: state.userForm,
    setUserLogedIn: state.setUserLogedIn,
    setUserForm: state.setUserForm,
    setpageFormUrl: state.setpageFormUrl,
    setpageUrl: state.setpageUrl,
    setBackPageButton: state.setBackPageButton,
    setPageIndex: state.setPageIndex,
    appPool: state.appPool,
  }));
  const handleOnIdle = () => {
    let AppId = userForm.ApplicationId;
    setUserLogedIn(false);
    setUserForm({});
    setpageFormUrl("");
    setpageUrl("");
    setBackPageButton([]);
    setPageIndex("");
    navigate(
      "https://account.collegeboard.org/login/logout?appId=" +
        AppId +
        "&DURL=https://student.collegeboard.org/css-financial-aid-profile"
    );
  };
  const { getRemainingTime, getLastActiveTime, getElapsedTime } = useIdleTimer({
    timeout,
    onActive: handleOnActive,
    onIdle: handleOnIdle,
  });

  useEffect(() => {
    setRemaining(getRemainingTime());
    setLastActive(getLastActiveTime());
    setElapsed(getElapsedTime());

    const intervalId = setInterval(() => {
      setRemaining(getRemainingTime());
      setLastActive(getLastActiveTime());
      setElapsed(getElapsedTime());
    }, 1000);

    // Cleanup function to clear the interval
    return () => {
      clearInterval(intervalId);
    };
    //adobeAnalyticsPush();
  }, [userForm.PageName, getRemainingTime, getLastActiveTime, getElapsedTime]);
  const adobeAnalyticsPush = () => {
    window.adobeDataLayer = window.adobeDataLayer || [];
    window.adobeDataLayer.push({
      page: {
        flowCode: userForm.MenuName,
        pageCode: userForm.PageName,
        appViewCode: "",
      },
    });
    try {
      window._satellite.track("cbTrack.viewInDom");
    } catch (errSatelliteTrack) {}
  };
  return (
    <div className="flex flex-col min-h-screen font-sans text-gray-900 md:text-lg text-sm">
      <Helmet>
        <script src="//kiwi.collegeboard.org/embed.js" async />
      </Helmet>
      <Header />
      <main
        id="scrollToTop"
        className="flex-1 w-full max-w-4xl px-4 py-8 mx-auto md:px-8 md:py-16"
      >
        {children}
      </main>
    </div>
  );
}

Layout.propTypes = {
  children: PropTypes.node.isRequired,
};

export default Layout;
