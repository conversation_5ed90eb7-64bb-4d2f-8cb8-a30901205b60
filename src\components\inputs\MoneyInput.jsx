import React, { useEffect, useState } from "react";
import { borderStyles, inputStyles, focusWithinStyles } from "../shared/inputs";
import useUserStore from "../../utils/storeApp";

export function MoneyInput({ error, onChange, rangeLow, ...moneyInputProps }) {
  const [spaceCount, setSpaceCount] = useState(moneyInputProps.maxLength);
  const [val, setVal] = useState(moneyInputProps.value);
  useEffect(() => {
    if (moneyInputProps.value) {
      setVal(formatDollar(moneyInputProps.value, moneyInputProps.maxLength));
    }
  }, [moneyInputProps.value, moneyInputProps.maxLength]);
  const preventUnwantedCharacters = (event, id) => {
    const allowMinusChar = [
      "q_par_agi",
      "ParentsCapitalGainLoss",
      "ParentsBusIncomeLoss",
      "ParentsOtherGainLoss",
      "ParentsRentalRoyalties",
      "ParentsFarmIncomeLoss",
      "ParentsOtherIncome",
      "q_par_recent_oth_tax_inc",
      "q_par_anticipated_oth_tax_inc",
    ];
    if (rangeLow.charAt(0) === "-") {
      const ignoredCharactersId = ["e", "E", "+", ".", ","];
      ignoredCharactersId.includes(event.key) && event.preventDefault();
    } else {
      const ignoredCharacters = ["e", "E", "+", "-", ".", ","];
      ignoredCharacters.includes(event.key) && event.preventDefault();
    }
  };

  function formatDollar(num, maxLength) {
    var p = num.replace(/ /g, "");
    let nm = 0;
    let negative = false;
    const str = p
      .split("")
      .reverse()
      .reduce(function (acc, num, i, orig) {
        if (i && !(i % 3)) {
          nm = nm + 1;
        }
        return num + (num != "-" && i && !(i % 3) ? " " : "") + acc;
      }, "");
    let nr = parseInt(maxLength) + nm;
    setSpaceCount(nr.toString());
    setVal(str);
    return str;
  }
  return (
    <div
      className={`flex flex-row items-center justify-center
      ${inputStyles} ${focusWithinStyles} ${borderStyles(error)}`}
    >
      <input
        className="w-full appearance-none outline-0"
        type="text"
        onChange={(event) =>
          onChange(formatDollar(event.target.value, moneyInputProps.maxLength))
        }
        onKeyDown={(el) => preventUnwantedCharacters(el, moneyInputProps.id)}
        {...moneyInputProps}
        displayFormat={(event) => formatDollar(event.target.value)}
        value={val}
        maxLength={spaceCount}
      />
    </div>
  );
}
