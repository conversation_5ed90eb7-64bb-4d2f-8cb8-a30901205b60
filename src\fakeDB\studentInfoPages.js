export const studentInfoPages = [
  {
    PageName: "StudentInfoIntro",
    PageHeading: "Getting Started",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent:
          "First, we'll collect some basic information about you, the student.",
      },
    ],
    FWDictsList: {
      Dicts: [],
      Count: 0,
    },
  },
  {
    PageName: "StudentInfo1",
    PageHeading: "Getting Started",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent:
          "Provide your legal name. Schools will use this to link your application to your school record.",
      },
      {
        itemType: "question",
        id: "f_stu_first_name",
        questionValue: "Mary",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "First name",
      },
      {
        itemType: "question",
        id: "f_stu_middle_name",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Middle name",
      },
      {
        itemType: "question",
        id: "f_stu_last_name",
        questionValue: "Test",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Last name",
      },
      {
        itemType: "question",
        id: "f_stu_preferred_name",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Preferred Name</b> - The preferred name you enter here will be shared with the colleges you select within the application and can be seen <b>by your parent(s)/guardian(s) who have access to your CSS Profile.</b>  <br /><br />If you would like College Board to use your preferred name outside of CSS Profile, you will need to updated your College Board account.",
        helpStyle: "Notice",
        helpTitle: "",
        textContent: "Preferred name",
      },
    ],
    FWDictsList: {
      Dicts: [],
      Count: 0,
    },
  },
  {
    PageName: "StudentInfo2",
    PageHeading: "Getting Started",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "What is the best way to reach you?",
      },
      {
        itemType: "question",
        id: "f_stu_phone",
        questionValue: "1232339911",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "15",
        minLength: "10",
        maxLength: "15",
        format: "number",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Phone number",
      },
      {
        itemType: "question",
        id: "f_stu_email",
        questionValue: "<EMAIL>",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "45",
        minLength: "",
        maxLength: "50",
        format: "email",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Email Address</b> - Use an email address you check regularly so we can update you on the status of your application and so schools can reach you.<br /><br />Be sure the student\\'s email address can receive email from the College Board by adding our domain name, collegeboard.org, to the list of enabled mail senders. <br />",
        helpStyle: "Notice",
        helpTitle: "",
        textContent: "Email address",
      },
      {
        itemType: "question",
        id: "f_stu_answer",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "45",
        minLength: "",
        maxLength: "50",
        format: "textarea",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "error msg",
        helpContent: "",
        helpTitle: "",
        textContent: "Answer",
      },
    ],
    FWDictsList: {
      Dicts: [],
      Count: 0,
    },
  },
  {
    PageName: "StudentInfo3",
    PageHeading: "Getting Started",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent:
          "Provide additional information about yourself, Mary. This helps us figure out what other questions we should ask.",
      },
      {
        itemType: "question",
        id: "f_stu_dob",
        questionValue: "07/12/2002",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "10",
        minLength: "",
        maxLength: "10",
        format: "date",
        displayFormat: "mm/dd/yyyy",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg:
          "Error: The date of birth you entered is either an invalid date or does not fall within the valid date range.  Please re-enter.",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Date of birth",
      },
      {
        itemType: "question",
        id: "f_stu_mar_status",
        questionValue: "1",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "select",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "StudentMaritalStatus",
        checkboxCheckedValue: "",
        questionMsg: "f_stu_mar_statusMsg",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Marital Status</b> - Make sure to answer for the student\\'s marital status.<br />",
        helpStyle: "Notice",
        helpTitle: "",
        textContent: "Mary's marital status",
      },
    ],
    FWDictsList: {
      Dicts: [
        {
          Display: "StudentMaritalStatus",
          Name: "StudentMaritalStatus",
          DictItems: [
            {
              Value: "2",
              Display: "Married or in a domestic partnership",
              Order: "002",
            },
            {
              Value: "1",
              Display: "Never married",
              Order: "001",
            },
            {
              Value: "4",
              Display: "Separated",
              Order: "004",
            },
            {
              Value: "5",
              Display: "Divorced or no longer in a domestic partnership",
              Order: "005",
            },
            {
              Value: "6",
              Display: "Widowed",
              Order: "006",
            },
          ],
        },
      ],
      Count: 0,
    },
  },
  {
    PageName: "StudentInfo4",
    PageHeading: "Student Status",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Dependency </b> - Financial aid offices may require supporting documentation to make sure these are accurate. <br />",
        helpStyle: "Notice",
        helpTitle: "",
        textContent:
          "Indicate if any of these are true for Mary. These questions help us understand your situation better.",
      },
      {
        itemType: "question",
        id: "f_stu_legal_dependents",
        questionValue: "N",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "radio",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "YesNo_YN",
        checkboxCheckedValue: "",
        questionMsg: "f_stu_legal_dependentsMsg",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          '<b>Legal Dependents </b> - Select "Yes" only if:  <br />  <br /> - the student has children for whom the student provides most (more than 50%) of their support or <br /> <br /> - the student provides most of the support (more than 50%) for people who live with the student (other than the student\\\'s spouse) and will continue to provide this level of support during the 2022-23 academic year. <br />',
        helpStyle: "Expandable Hint",
        helpTitle: "What is a legal dependent?",
        textContent: "Mary has legal dependents",
      },
      {
        itemType: "question",
        id: "f_stu_legal_dependents_married",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "radio",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "YesNo_YN",
        checkboxCheckedValue: "",
        questionMsg: "f_stu_legal_dependentsMsg",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Legal Dependents (Married) </b> - A legal dependent is someone who receives more than 50% of their support from you, the student.  This can be a child or another person (other than your spouse) who lives with you, and will continue to receive this level of support for the 2023-24 school year. <br />",
        helpStyle: "Expandable Hint",
        helpTitle: "What is a legal dependent?",
        textContent: "Mary has legal dependents (not including their spouse)",
      },
      {
        itemType: "question",
        id: "f_stu_vet_status",
        questionValue: "N",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "radio",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "YesNo_YN",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "f_stu_citizenship_status",
        condDsblParVal: ";BLANK;3;4",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "f_stu_citizenship_status",
        condReqParVal: ";1;2;",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Veteran Status </b> - ROTC students, students at service academies, and National Guard/Reserves enlistees are not considered veterans. <br />",
        helpStyle: "Expandable Hint",
        helpTitle: "What is a legal dependent?",
        textContent:
          "Mary is a veteran of the U.S. Armed Forces or currently serving on active duty",
      },
      {
        itemType: "question",
        id: "StuEmancipated",
        questionValue: "N",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "radio",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "YesNo_YN",
        checkboxCheckedValue: "",
        questionMsg: "StuEmancipatedMsg",
        condDsblChild: "",
        condDsblParFld: "f_stu_citizenship_status",
        condDsblParVal: ";BLANK;3;",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "f_stu_citizenship_status",
        condReqParVal: ";1;2;4;",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Emanicipated Minor  </b> - Answer \"Yes\" if the student is legally emancipated, that is the relationship between the student and their biological or adoptive parents has been terminated and the student is free to make legal and personal decisions on their own behalf, and you can provide a copy of a court\\'s decision that the student is today, or was until the age of being an adult in the student\\'s state, an emancipated minor.  The court issuing the decision must have been located in the student\\'s state of legal residence at the time the court\\'s decision was issued. <br /> <br />Financial aid administrators may require a copy of the court\\'s decision. <br />",
        helpStyle: "Expandable Hint",
        helpTitle: "Tell me more",
        textContent:
          "Mary was determined to be an emancipated minor by a court in their state of legal residence",
      },
      {
        itemType: "question",
        id: "q_stu_homeless",
        questionValue: "N",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "radio",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "YesNo_YN",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "f_stu_citizenship_status",
        condDsblParVal: ";BLANK;3;",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "f_stu_citizenship_status",
        condReqParVal: ";1;2;4;",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          '<b>Homeless Status</b> - Answer "Yes" if the student is an unaccompanied youth who is or was homeless or at risk of being homeless.  <br /> <br />  -- "Homeless" means the student lacks fixed, regular and adequate housing (living in shelters, parks, motels or cars or living temporarily with other people because the student has nowhere else to go) <br />  -- "Unaccompanied" means the student is not living in the physical custody of the student\\\'s parents <br />  -- "Youth" means that the student is 23 years of age or younger or the student is still enrolled in high school as of the date of the application <br /> <br /> Financial aid administrators may require documentation of this status. <br />',
        helpStyle: "Expandable Hint",
        helpTitle: "Tell me more",
        textContent: "Mary is homeless, or at risk of becoming homeless.",
      },
    ],
    FWDictsList: {
      Dicts: [
        {
          Display: "YesNo_YN",
          Name: "YesNo_YN",
          DictItems: [
            {
              Value: "Y",
              Display: "Yes",
              Order: "001",
            },
            {
              Value: "N",
              Display: "No",
              Order: "002",
            },
          ],
        },
      ],
      Count: 0,
    },
  },
  {
    PageName: "StudentInfo5",
    PageHeading: "Homeless",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent:
          "At any time since July 1, 2022 Mary was determined to be an unaccompanied youth who was homeless or at risk of being homeless by:",
      },
      {
        itemType: "question",
        id: "StuHomelessHS",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "select",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "YesNo_YN",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Mary's high school or school district homeless liaison",
      },
      {
        itemType: "question",
        id: "StuHomelessShelter",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "select",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "YesNo_YN",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent:
          "The director of an emergency shelter or transitional housing program funded by the U.S. Department of Housing and Urban Development",
      },
      {
        itemType: "question",
        id: "StuHomelessRunaway",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "select",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "YesNo_YN",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent:
          "The director of a runaway or homeless youth basic center or transitional living program",
      },
    ],
    FWDictsList: {
      Dicts: [
        {
          Display: "YesNo_YN",
          Name: "YesNo_YN",
          DictItems: [
            {
              Value: "Y",
              Display: "Yes",
              Order: "001",
            },
            {
              Value: "N",
              Display: "No",
              Order: "002",
            },
          ],
        },
      ],
      Count: 0,
    },
  },
  {
    PageName: "StudentInfo6",
    PageHeading: "Foster Care or Ward of the Court",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent:
          "Indicate if any of these are true for Mary. These questions help us understand your situation better.",
      },
      {
        itemType: "question",
        id: "f_stu_ward_court_cur",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "radio",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "YesNo_YN",
        checkboxCheckedValue: "",
        questionMsg: "f_stu_ward_court_curMsg",
        condDsblChild: "",
        condDsblParFld: "VarCitizenLT18",
        condDsblParVal: ";BLANK;N;",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "Y",
        condReqChild: "",
        condReqParFld: "VarCitizenLT18",
        condReqParVal: "Y",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          '<b>Ward of Court</b> - Answer "Yes" if the student is currently a ward of the court because a court of law has assumed legal custody of the student.  Do not answer yes if the student\\\'s parents arranged a legal guardian for the student, even if a court of law was involved. <br /> <br /> Financial aid administrators may require documentation of this status.<br />',
        helpStyle: "Expandable Hint",
        helpTitle: "Tell me more",
        textContent: "Mary is a ward of the court",
      },
      {
        itemType: "question",
        id: "StuFosterCare",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "radio",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "YesNo_YN",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "VarCitizenLT18",
        condDsblParVal: ";BLANK;N;",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "Y",
        condReqChild: "",
        condReqParFld: "VarCitizenLT18",
        condReqParVal: "Y",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Mary is in foster care",
      },
      {
        itemType: "question",
        id: "f_stu_ward_court_over18",
        questionValue: "N",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "radio",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "YesNo_YN",
        checkboxCheckedValue: "",
        questionMsg: "f_stu_ward_court_over18Msg",
        condDsblChild: "",
        condDsblParFld: "VarCitizenOver18",
        condDsblParVal: ";BLANK;N;",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "VarCitizenOver18",
        condReqParVal: "Y",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          '<b>Ward of Court</b> - Answer "Yes" if the student was, until the age of 18, a ward of the court because a court of law assume legal custody of the student before the student turned 18.  Do not answer yes if the student\\\'s parents arranged a legal guardian for the student, even if a court of law was involved. <br /> <br /> Financial aid administrators may require documentation of this status.<br />',
        helpStyle: "Expandable Hint",
        helpTitle: "Tell me more",
        textContent: "Mary was (until the age of 18) in foster care",
      },
      {
        itemType: "question",
        id: "StuFosterCareOver18",
        questionValue: "N",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "radio",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "YesNo_YN",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "VarCitizenOver18",
        condDsblParVal: ";BLANK;N;",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "VarCitizenOver18",
        condReqParVal: "Y",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Mary was (until the age of 18) a ward of the court",
      },
    ],
    FWDictsList: {
      Dicts: [
        {
          Display: "YesNo_YN",
          Name: "YesNo_YN",
          DictItems: [
            {
              Value: "Y",
              Display: "Yes",
              Order: "001",
            },
            {
              Value: "N",
              Display: "No",
              Order: "002",
            },
          ],
        },
      ],
      Count: 0,
    },
  },
  {
    PageName: "StudentInfo7",
    PageHeading: "Student's Citizenship",
    FWFormItemList: [
      {
        itemType: "question",
        id: "f_stu_citizenship_status",
        questionValue: "1",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "radio",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "CitizenshipStatus",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          '<b>Citizenship </b> - Select "U.S. eligible noncitizen" if the student: <br /> <br /> -is a U.S. permanent resident (I-551) <br /> -is a conditional permanent resident (I-551C) <br /> -holds an I-94 with a designation of "Refugee," "Asylum Granted," "Parolee," T-Visa holder, or "Cuban-Haitian Entrant," "Victim of human trafficking," or <br /> -is a citizen of the Republic of Palau, the Republic of the Marshall Islands, or the Federated States of Micronesia. <br /> <br /> Select "other" if the student is not a citizen or permanent resident of the U.S. or Canada and does not meet the above criteria, including having been granted Deferred Action for Childhood Arrivals (DACA) status or having an F1, F2, J1, J2, or G series visa. <br /> <br /> If the student has dual citizenship, select U.S. citizen.',
        helpStyle: "Expandable Hint",
        helpTitle: "What if I'm not sure?",
        textContent: "Citizenship status",
      },
      {
        itemType: "question",
        id: "f_stu_perm_country",
        questionValue: "US",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "select",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "Country",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Country Where Student Lives</b> - Select the country where you live. This should be your permanent address, and may be different from where you receive your mail.<br />",
        helpStyle: "Notice",
        helpTitle: "",
        textContent: "Country where Mary lives",
      },
    ],
    FWDictsList: {
      Dicts: [
        {
          Display: "CitizenshipStatus",
          Name: "CitizenshipStatus",
          DictItems: [
            {
              Value: "1",
              Display: "U.S. citizen",
              Order: "001",
            },
            {
              Value: "2",
              Display: "U.S. eligible noncitizen",
              Order: "002",
            },
            {
              Value: "4",
              Display: "Canadian citizen",
              Order: "003",
            },
            {
              Value: "3",
              Display: "Other",
              Order: "004",
            },
          ],
        },
        {
          Display: "Country",
          Name: "Country",
          DictItems: [
            {
              Value: "AF",
              Display: "Afghanistan",
              Order: "002",
            },
            {
              Value: "AX",
              Display: "Åland Islands",
              Order: "003",
            },
            {
              Value: "AL",
              Display: "Albania",
              Order: "004",
            },
            {
              Value: "DZ",
              Display: "Algeria",
              Order: "005",
            },
            {
              Value: "AD",
              Display: "Andorra",
              Order: "006",
            },
            {
              Value: "AO",
              Display: "Angola",
              Order: "007",
            },
            {
              Value: "AI",
              Display: "Anguilla",
              Order: "008",
            },
            {
              Value: "AG",
              Display: "Antigua and Barbuda",
              Order: "009",
            },
            {
              Value: "AR",
              Display: "Argentina",
              Order: "010",
            },
            {
              Value: "AM",
              Display: "Armenia",
              Order: "011",
            },
            {
              Value: "AW",
              Display: "Aruba",
              Order: "012",
            },
            {
              Value: "AU",
              Display: "Australia",
              Order: "013",
            },
            {
              Value: "AT",
              Display: "Austria",
              Order: "014",
            },
            {
              Value: "AZ",
              Display: "Azerbaijan",
              Order: "015",
            },
            {
              Value: "BS",
              Display: "Bahamas",
              Order: "016",
            },
            {
              Value: "BH",
              Display: "Bahrain",
              Order: "017",
            },
            {
              Value: "BD",
              Display: "Bangladesh",
              Order: "018",
            },
            {
              Value: "BB",
              Display: "Barbados",
              Order: "019",
            },
            {
              Value: "BY",
              Display: "Belarus",
              Order: "020",
            },
            {
              Value: "BE",
              Display: "Belgium",
              Order: "021",
            },
            {
              Value: "BZ",
              Display: "Belize",
              Order: "022",
            },
            {
              Value: "BJ",
              Display: "Benin",
              Order: "023",
            },
            {
              Value: "BM",
              Display: "Bermuda",
              Order: "024",
            },
            {
              Value: "BT",
              Display: "Bhutan",
              Order: "025",
            },
            {
              Value: "BO",
              Display: "Bolivia (Plurinational State of)",
              Order: "026",
            },
            {
              Value: "BQ",
              Display: "Bonaire, Sint Eustatius and Saba",
              Order: "027",
            },
            {
              Value: "BA",
              Display: "Bosnia and Herzegovina",
              Order: "028",
            },
            {
              Value: "BW",
              Display: "Botswana",
              Order: "029",
            },
            {
              Value: "BR",
              Display: "Brazil",
              Order: "030",
            },
            {
              Value: "BN",
              Display: "Brunei Darussalam",
              Order: "031",
            },
            {
              Value: "BG",
              Display: "Bulgaria",
              Order: "032",
            },
            {
              Value: "BF",
              Display: "Burkina Faso",
              Order: "033",
            },
            {
              Value: "BI",
              Display: "Burundi",
              Order: "034",
            },
            {
              Value: "CV",
              Display: "Cabo Verde",
              Order: "035",
            },
            {
              Value: "KH",
              Display: "Cambodia",
              Order: "036",
            },
            {
              Value: "CM",
              Display: "Cameroon",
              Order: "037",
            },
            {
              Value: "CA",
              Display: "Canada",
              Order: "038",
            },
            {
              Value: "KY",
              Display: "Cayman Islands",
              Order: "039",
            },
            {
              Value: "CF",
              Display: "Central African Republic",
              Order: "040",
            },
            {
              Value: "TD",
              Display: "Chad",
              Order: "041",
            },
            {
              Value: "CL",
              Display: "Chile",
              Order: "042",
            },
            {
              Value: "CN",
              Display: "China",
              Order: "043",
            },
            {
              Value: "CO",
              Display: "Colombia",
              Order: "044",
            },
            {
              Value: "KM",
              Display: "Comoros",
              Order: "045",
            },
            {
              Value: "CG",
              Display: "Congo",
              Order: "046",
            },
            {
              Value: "CD",
              Display: "Congo (the Democratic Republic of the)",
              Order: "047",
            },
            {
              Value: "CK",
              Display: "Cook Islands",
              Order: "048",
            },
            {
              Value: "CR",
              Display: "Costa Rica",
              Order: "049",
            },
            {
              Value: "CI",
              Display: "Côte d'Ivoire",
              Order: "050",
            },
            {
              Value: "HR",
              Display: "Croatia",
              Order: "051",
            },
            {
              Value: "CU",
              Display: "Cuba",
              Order: "052",
            },
            {
              Value: "CW",
              Display: "Curaçao",
              Order: "053",
            },
            {
              Value: "CY",
              Display: "Cyprus",
              Order: "054",
            },
            {
              Value: "CZ",
              Display: "Czechia",
              Order: "055",
            },
            {
              Value: "DK",
              Display: "Denmark",
              Order: "056",
            },
            {
              Value: "DJ",
              Display: "Djibouti",
              Order: "057",
            },
            {
              Value: "DM",
              Display: "Dominica",
              Order: "058",
            },
            {
              Value: "DO",
              Display: "Dominican Republic",
              Order: "059",
            },
            {
              Value: "EC",
              Display: "Ecuador",
              Order: "060",
            },
            {
              Value: "EG",
              Display: "Egypt",
              Order: "061",
            },
            {
              Value: "SV",
              Display: "El Salvador",
              Order: "062",
            },
            {
              Value: "GQ",
              Display: "Equatorial Guinea",
              Order: "063",
            },
            {
              Value: "ER",
              Display: "Eritrea",
              Order: "064",
            },
            {
              Value: "EE",
              Display: "Estonia",
              Order: "065",
            },
            {
              Value: "SZ",
              Display: "Eswatini",
              Order: "066",
            },
            {
              Value: "ET",
              Display: "Ethiopia",
              Order: "067",
            },
            {
              Value: "FK",
              Display: "Falkland Islands [Malvinas]",
              Order: "068",
            },
            {
              Value: "FO",
              Display: "Faroe Islands",
              Order: "069",
            },
            {
              Value: "FJ",
              Display: "Fiji",
              Order: "070",
            },
            {
              Value: "FI",
              Display: "Finland",
              Order: "071",
            },
            {
              Value: "FR",
              Display: "France",
              Order: "072",
            },
            {
              Value: "GF",
              Display: "French Guiana",
              Order: "073",
            },
            {
              Value: "PF",
              Display: "French Polynesia",
              Order: "074",
            },
            {
              Value: "GA",
              Display: "Gabon",
              Order: "075",
            },
            {
              Value: "GM",
              Display: "Gambia",
              Order: "076",
            },
            {
              Value: "GZ",
              Display: "Gaza Strip",
              Order: "077",
            },
            {
              Value: "GE",
              Display: "Georgia",
              Order: "078",
            },
            {
              Value: "DE",
              Display: "Germany",
              Order: "079",
            },
            {
              Value: "GH",
              Display: "Ghana",
              Order: "080",
            },
            {
              Value: "GI",
              Display: "Gibraltar",
              Order: "081",
            },
            {
              Value: "GR",
              Display: "Greece",
              Order: "082",
            },
            {
              Value: "GL",
              Display: "Greenland",
              Order: "083",
            },
            {
              Value: "GD",
              Display: "Grenada",
              Order: "084",
            },
            {
              Value: "GP",
              Display: "Guadeloupe",
              Order: "085",
            },
            {
              Value: "GT",
              Display: "Guatemala",
              Order: "086",
            },
            {
              Value: "GG",
              Display: "Guernsey",
              Order: "087",
            },
            {
              Value: "GN",
              Display: "Guinea",
              Order: "088",
            },
            {
              Value: "GW",
              Display: "Guinea-Bissau",
              Order: "089",
            },
            {
              Value: "GY",
              Display: "Guyana",
              Order: "090",
            },
            {
              Value: "HT",
              Display: "Haiti",
              Order: "091",
            },
            {
              Value: "VA",
              Display: "Holy See [Vatican City State]",
              Order: "092",
            },
            {
              Value: "HN",
              Display: "Honduras",
              Order: "093",
            },
            {
              Value: "HK",
              Display: "Hong Kong",
              Order: "094",
            },
            {
              Value: "HU",
              Display: "Hungary",
              Order: "095",
            },
            {
              Value: "IS",
              Display: "Iceland",
              Order: "096",
            },
            {
              Value: "IN",
              Display: "India",
              Order: "097",
            },
            {
              Value: "ID",
              Display: "Indonesia",
              Order: "098",
            },
            {
              Value: "IR",
              Display: "Iran (Islamic Republic of)",
              Order: "099",
            },
            {
              Value: "IQ",
              Display: "Iraq",
              Order: "100",
            },
            {
              Value: "IE",
              Display: "Ireland",
              Order: "101",
            },
            {
              Value: "IM",
              Display: "Isle of Man",
              Order: "102",
            },
            {
              Value: "IL",
              Display: "Israel",
              Order: "103",
            },
            {
              Value: "IT",
              Display: "Italy",
              Order: "104",
            },
            {
              Value: "JM",
              Display: "Jamaica",
              Order: "105",
            },
            {
              Value: "JP",
              Display: "Japan",
              Order: "106",
            },
            {
              Value: "JE",
              Display: "Jersey",
              Order: "107",
            },
            {
              Value: "JO",
              Display: "Jordan",
              Order: "108",
            },
            {
              Value: "KZ",
              Display: "Kazakhstan",
              Order: "109",
            },
            {
              Value: "KE",
              Display: "Kenya",
              Order: "110",
            },
            {
              Value: "KI",
              Display: "Kiribati",
              Order: "111",
            },
            {
              Value: "KP",
              Display: "Korea (the Democratic People's Republic of)",
              Order: "112",
            },
            {
              Value: "KR",
              Display: "Korea (the Republic of)",
              Order: "113",
            },
            {
              Value: "KS",
              Display: "Kosovo",
              Order: "114",
            },
            {
              Value: "KW",
              Display: "Kuwait",
              Order: "115",
            },
            {
              Value: "KG",
              Display: "Kyrgyzstan",
              Order: "116",
            },
            {
              Value: "LA",
              Display: "Lao People's Democratic Republic (the)",
              Order: "117",
            },
            {
              Value: "LV",
              Display: "Latvia",
              Order: "118",
            },
            {
              Value: "LB",
              Display: "Lebanon",
              Order: "119",
            },
            {
              Value: "LS",
              Display: "Lesotho",
              Order: "120",
            },
            {
              Value: "LR",
              Display: "Liberia",
              Order: "121",
            },
            {
              Value: "LY",
              Display: "Libya",
              Order: "122",
            },
            {
              Value: "LI",
              Display: "Liechtenstein",
              Order: "123",
            },
            {
              Value: "LT",
              Display: "Lithuania",
              Order: "124",
            },
            {
              Value: "LU",
              Display: "Luxembourg",
              Order: "125",
            },
            {
              Value: "MO",
              Display: "Macao",
              Order: "126",
            },
            {
              Value: "MG",
              Display: "Madagascar",
              Order: "127",
            },
            {
              Value: "MW",
              Display: "Malawi",
              Order: "128",
            },
            {
              Value: "MY",
              Display: "Malaysia",
              Order: "129",
            },
            {
              Value: "MV",
              Display: "Maldives",
              Order: "130",
            },
            {
              Value: "ML",
              Display: "Mali",
              Order: "131",
            },
            {
              Value: "MT",
              Display: "Malta",
              Order: "132",
            },
            {
              Value: "MQ",
              Display: "Martinique",
              Order: "133",
            },
            {
              Value: "MR",
              Display: "Mauritania",
              Order: "134",
            },
            {
              Value: "MU",
              Display: "Mauritius",
              Order: "135",
            },
            {
              Value: "YT",
              Display: "Mayotte",
              Order: "136",
            },
            {
              Value: "MX",
              Display: "Mexico",
              Order: "137",
            },
            {
              Value: "MD",
              Display: "Moldova (the Republic of)",
              Order: "138",
            },
            {
              Value: "MC",
              Display: "Monaco",
              Order: "139",
            },
            {
              Value: "MN",
              Display: "Mongolia",
              Order: "140",
            },
            {
              Value: "ME",
              Display: "Montenegro",
              Order: "141",
            },
            {
              Value: "MS",
              Display: "Montserrat",
              Order: "142",
            },
            {
              Value: "MA",
              Display: "Morocco",
              Order: "143",
            },
            {
              Value: "MZ",
              Display: "Mozambique",
              Order: "144",
            },
            {
              Value: "MM",
              Display: "Myanmar",
              Order: "145",
            },
            {
              Value: "NA",
              Display: "Namibia",
              Order: "146",
            },
            {
              Value: "NR",
              Display: "Nauru",
              Order: "147",
            },
            {
              Value: "NP",
              Display: "Nepal",
              Order: "148",
            },
            {
              Value: "NL",
              Display: "Netherlands",
              Order: "149",
            },
            {
              Value: "NC",
              Display: "New Caledonia",
              Order: "150",
            },
            {
              Value: "NZ",
              Display: "New Zealand",
              Order: "151",
            },
            {
              Value: "NI",
              Display: "Nicaragua",
              Order: "152",
            },
            {
              Value: "NE",
              Display: "Niger",
              Order: "153",
            },
            {
              Value: "NG",
              Display: "Nigeria",
              Order: "154",
            },
            {
              Value: "NU",
              Display: "Niue",
              Order: "155",
            },
            {
              Value: "MK",
              Display: "North Macedonia",
              Order: "156",
            },
            {
              Value: "NO",
              Display: "Norway",
              Order: "157",
            },
            {
              Value: "OM",
              Display: "Oman",
              Order: "158",
            },
            {
              Value: "PK",
              Display: "Pakistan",
              Order: "159",
            },
            {
              Value: "PS",
              Display: "Palestine, State of",
              Order: "160",
            },
            {
              Value: "PA",
              Display: "Panama",
              Order: "161",
            },
            {
              Value: "PG",
              Display: "Papua New Guinea",
              Order: "162",
            },
            {
              Value: "PY",
              Display: "Paraguay",
              Order: "163",
            },
            {
              Value: "PE",
              Display: "Peru",
              Order: "164",
            },
            {
              Value: "PH",
              Display: "Philippines",
              Order: "165",
            },
            {
              Value: "PL",
              Display: "Poland",
              Order: "166",
            },
            {
              Value: "PT",
              Display: "Portugal",
              Order: "167",
            },
            {
              Value: "QA",
              Display: "Qatar",
              Order: "168",
            },
            {
              Value: "RE",
              Display: "Réunion",
              Order: "169",
            },
            {
              Value: "RO",
              Display: "Romania",
              Order: "170",
            },
            {
              Value: "RU",
              Display: "Russian Federation",
              Order: "171",
            },
            {
              Value: "RW",
              Display: "Rwanda",
              Order: "172",
            },
            {
              Value: "BL",
              Display: "Saint Barthélemy",
              Order: "173",
            },
            {
              Value: "SH",
              Display: "Saint Helena, Ascension and Tristan da Cunha",
              Order: "174",
            },
            {
              Value: "KN",
              Display: "Saint Kitts and Nevis",
              Order: "175",
            },
            {
              Value: "LC",
              Display: "Saint Lucia",
              Order: "176",
            },
            {
              Value: "MF",
              Display: "Saint Martin (French part)",
              Order: "177",
            },
            {
              Value: "PM",
              Display: "Saint Pierre and Miquelon",
              Order: "178",
            },
            {
              Value: "VC",
              Display: "Saint Vincent and the Grenadines",
              Order: "179",
            },
            {
              Value: "WS",
              Display: "Samoa",
              Order: "180",
            },
            {
              Value: "SM",
              Display: "San Marino",
              Order: "181",
            },
            {
              Value: "ST",
              Display: "Sao Tome and Principe",
              Order: "182",
            },
            {
              Value: "SA",
              Display: "Saudi Arabia",
              Order: "183",
            },
            {
              Value: "SN",
              Display: "Senegal",
              Order: "184",
            },
            {
              Value: "RS",
              Display: "Serbia",
              Order: "185",
            },
            {
              Value: "SC",
              Display: "Seychelles",
              Order: "186",
            },
            {
              Value: "SL",
              Display: "Sierra Leone",
              Order: "187",
            },
            {
              Value: "SG",
              Display: "Singapore",
              Order: "188",
            },
            {
              Value: "SX",
              Display: "Sint Maarten (Dutch part)",
              Order: "189",
            },
            {
              Value: "SK",
              Display: "Slovakia",
              Order: "190",
            },
            {
              Value: "SI",
              Display: "Slovenia",
              Order: "191",
            },
            {
              Value: "SB",
              Display: "Solomon Islands",
              Order: "192",
            },
            {
              Value: "SO",
              Display: "Somalia",
              Order: "193",
            },
            {
              Value: "ZA",
              Display: "South Africa",
              Order: "194",
            },
            {
              Value: "SS",
              Display: "South Sudan",
              Order: "195",
            },
            {
              Value: "ES",
              Display: "Spain",
              Order: "196",
            },
            {
              Value: "LK",
              Display: "Sri Lanka",
              Order: "197",
            },
            {
              Value: "SD",
              Display: "Sudan",
              Order: "198",
            },
            {
              Value: "SR",
              Display: "Suriname",
              Order: "199",
            },
            {
              Value: "SE",
              Display: "Sweden",
              Order: "200",
            },
            {
              Value: "CH",
              Display: "Switzerland",
              Order: "201",
            },
            {
              Value: "SY",
              Display: "Syrian Arab Republic",
              Order: "202",
            },
            {
              Value: "TW",
              Display: "Taiwan (Province of China)",
              Order: "203",
            },
            {
              Value: "TJ",
              Display: "Tajikistan",
              Order: "204",
            },
            {
              Value: "TZ",
              Display: "Tanzania, United Republic of",
              Order: "205",
            },
            {
              Value: "TH",
              Display: "Thailand",
              Order: "206",
            },
            {
              Value: "TL",
              Display: "Timor-Leste",
              Order: "207",
            },
            {
              Value: "TG",
              Display: "Togo",
              Order: "208",
            },
            {
              Value: "TK",
              Display: "Tokelau",
              Order: "209",
            },
            {
              Value: "TO",
              Display: "Tonga",
              Order: "210",
            },
            {
              Value: "TT",
              Display: "Trinidad and Tobago",
              Order: "211",
            },
            {
              Value: "TN",
              Display: "Tunisia",
              Order: "212",
            },
            {
              Value: "TR",
              Display: "Turkey",
              Order: "213",
            },
            {
              Value: "TM",
              Display: "Turkmenistan",
              Order: "214",
            },
            {
              Value: "TC",
              Display: "Turks and Caicos Islands",
              Order: "215",
            },
            {
              Value: "TV",
              Display: "Tuvalu",
              Order: "216",
            },
            {
              Value: "UG",
              Display: "Uganda",
              Order: "217",
            },
            {
              Value: "UA",
              Display: "Ukraine",
              Order: "218",
            },
            {
              Value: "AE",
              Display: "United Arab Emirates",
              Order: "219",
            },
            {
              Value: "GB",
              Display: "United Kingdom ",
              Order: "220",
            },
            {
              Value: "US",
              Display: "United States",
              Order: "001",
            },
            {
              Value: "UY",
              Display: "Uruguay",
              Order: "221",
            },
            {
              Value: "UZ",
              Display: "Uzbekistan",
              Order: "222",
            },
            {
              Value: "VU",
              Display: "Vanuatu",
              Order: "223",
            },
            {
              Value: "VE",
              Display: "Venezuela (Bolivarian Republic of)",
              Order: "224",
            },
            {
              Value: "VN",
              Display: "Viet Nam",
              Order: "225",
            },
            {
              Value: "VG",
              Display: "Virgin Islands (British)",
              Order: "226",
            },
            {
              Value: "WF",
              Display: "Wallis and Futuna",
              Order: "227",
            },
            {
              Value: "WE",
              Display: "West Bank",
              Order: "228",
            },
            {
              Value: "EH",
              Display: "Western Sahara",
              Order: "229",
            },
            {
              Value: "YE",
              Display: "Yemen",
              Order: "230",
            },
            {
              Value: "ZM",
              Display: "Zambia",
              Order: "231",
            },
            {
              Value: "ZW",
              Display: "Zimbabwe",
              Order: "232",
            },
          ],
        },
      ],
      Count: 0,
    },
  },
  {
    PageName: "StudentInfo8",
    PageHeading: "Student's Citizenship",
    FWFormItemList: [
      {
        itemType: "question",
        id: "q_stu_citizenship_country",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "select",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "Country",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "f_stu_citizenship_status",
        condDsblParVal: ";BLANK;1;2;4;",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "Y",
        condReqChild: "",
        condReqParFld: "f_stu_citizenship_status",
        condReqParVal: "3",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Country of Citizenship</b> - Select the country of citizenship that is or will be reported on the student\\'s I-20 or DS-2020, or the citizenship of the passport the student will be using to travel to the United States. <br />",
        helpStyle: "Hint",
        helpTitle: "",
        textContent: "Country of citizenship",
      },
      {
        itemType: "question",
        id: "q_stu_visa_type",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "select",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "VisaType",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "f_stu_citizenship_status",
        condDsblParVal: ";BLANK;1;2;4;",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "Y",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Visa type",
      },
    ],
    FWDictsList: {
      Dicts: [
        {
          Display: "Country",
          Name: "Country",
          DictItems: [
            {
              Value: "AF",
              Display: "Afghanistan",
              Order: "002",
            },
            {
              Value: "AX",
              Display: "Åland Islands",
              Order: "003",
            },
            {
              Value: "AL",
              Display: "Albania",
              Order: "004",
            },
            {
              Value: "DZ",
              Display: "Algeria",
              Order: "005",
            },
            {
              Value: "AD",
              Display: "Andorra",
              Order: "006",
            },
            {
              Value: "AO",
              Display: "Angola",
              Order: "007",
            },
            {
              Value: "AI",
              Display: "Anguilla",
              Order: "008",
            },
            {
              Value: "AG",
              Display: "Antigua and Barbuda",
              Order: "009",
            },
            {
              Value: "AR",
              Display: "Argentina",
              Order: "010",
            },
            {
              Value: "AM",
              Display: "Armenia",
              Order: "011",
            },
            {
              Value: "AW",
              Display: "Aruba",
              Order: "012",
            },
            {
              Value: "AU",
              Display: "Australia",
              Order: "013",
            },
            {
              Value: "AT",
              Display: "Austria",
              Order: "014",
            },
            {
              Value: "AZ",
              Display: "Azerbaijan",
              Order: "015",
            },
            {
              Value: "BS",
              Display: "Bahamas",
              Order: "016",
            },
            {
              Value: "BH",
              Display: "Bahrain",
              Order: "017",
            },
            {
              Value: "BD",
              Display: "Bangladesh",
              Order: "018",
            },
            {
              Value: "BB",
              Display: "Barbados",
              Order: "019",
            },
            {
              Value: "BY",
              Display: "Belarus",
              Order: "020",
            },
            {
              Value: "BE",
              Display: "Belgium",
              Order: "021",
            },
            {
              Value: "BZ",
              Display: "Belize",
              Order: "022",
            },
            {
              Value: "BJ",
              Display: "Benin",
              Order: "023",
            },
            {
              Value: "BM",
              Display: "Bermuda",
              Order: "024",
            },
            {
              Value: "BT",
              Display: "Bhutan",
              Order: "025",
            },
            {
              Value: "BO",
              Display: "Bolivia (Plurinational State of)",
              Order: "026",
            },
            {
              Value: "BQ",
              Display: "Bonaire, Sint Eustatius and Saba",
              Order: "027",
            },
            {
              Value: "BA",
              Display: "Bosnia and Herzegovina",
              Order: "028",
            },
            {
              Value: "BW",
              Display: "Botswana",
              Order: "029",
            },
            {
              Value: "BR",
              Display: "Brazil",
              Order: "030",
            },
            {
              Value: "BN",
              Display: "Brunei Darussalam",
              Order: "031",
            },
            {
              Value: "BG",
              Display: "Bulgaria",
              Order: "032",
            },
            {
              Value: "BF",
              Display: "Burkina Faso",
              Order: "033",
            },
            {
              Value: "BI",
              Display: "Burundi",
              Order: "034",
            },
            {
              Value: "CV",
              Display: "Cabo Verde",
              Order: "035",
            },
            {
              Value: "KH",
              Display: "Cambodia",
              Order: "036",
            },
            {
              Value: "CM",
              Display: "Cameroon",
              Order: "037",
            },
            {
              Value: "CA",
              Display: "Canada",
              Order: "038",
            },
            {
              Value: "KY",
              Display: "Cayman Islands",
              Order: "039",
            },
            {
              Value: "CF",
              Display: "Central African Republic",
              Order: "040",
            },
            {
              Value: "TD",
              Display: "Chad",
              Order: "041",
            },
            {
              Value: "CL",
              Display: "Chile",
              Order: "042",
            },
            {
              Value: "CN",
              Display: "China",
              Order: "043",
            },
            {
              Value: "CO",
              Display: "Colombia",
              Order: "044",
            },
            {
              Value: "KM",
              Display: "Comoros",
              Order: "045",
            },
            {
              Value: "CG",
              Display: "Congo",
              Order: "046",
            },
            {
              Value: "CD",
              Display: "Congo (the Democratic Republic of the)",
              Order: "047",
            },
            {
              Value: "CK",
              Display: "Cook Islands",
              Order: "048",
            },
            {
              Value: "CR",
              Display: "Costa Rica",
              Order: "049",
            },
            {
              Value: "CI",
              Display: "Côte d'Ivoire",
              Order: "050",
            },
            {
              Value: "HR",
              Display: "Croatia",
              Order: "051",
            },
            {
              Value: "CU",
              Display: "Cuba",
              Order: "052",
            },
            {
              Value: "CW",
              Display: "Curaçao",
              Order: "053",
            },
            {
              Value: "CY",
              Display: "Cyprus",
              Order: "054",
            },
            {
              Value: "CZ",
              Display: "Czechia",
              Order: "055",
            },
            {
              Value: "DK",
              Display: "Denmark",
              Order: "056",
            },
            {
              Value: "DJ",
              Display: "Djibouti",
              Order: "057",
            },
            {
              Value: "DM",
              Display: "Dominica",
              Order: "058",
            },
            {
              Value: "DO",
              Display: "Dominican Republic",
              Order: "059",
            },
            {
              Value: "EC",
              Display: "Ecuador",
              Order: "060",
            },
            {
              Value: "EG",
              Display: "Egypt",
              Order: "061",
            },
            {
              Value: "SV",
              Display: "El Salvador",
              Order: "062",
            },
            {
              Value: "GQ",
              Display: "Equatorial Guinea",
              Order: "063",
            },
            {
              Value: "ER",
              Display: "Eritrea",
              Order: "064",
            },
            {
              Value: "EE",
              Display: "Estonia",
              Order: "065",
            },
            {
              Value: "SZ",
              Display: "Eswatini",
              Order: "066",
            },
            {
              Value: "ET",
              Display: "Ethiopia",
              Order: "067",
            },
            {
              Value: "FK",
              Display: "Falkland Islands [Malvinas]",
              Order: "068",
            },
            {
              Value: "FO",
              Display: "Faroe Islands",
              Order: "069",
            },
            {
              Value: "FJ",
              Display: "Fiji",
              Order: "070",
            },
            {
              Value: "FI",
              Display: "Finland",
              Order: "071",
            },
            {
              Value: "FR",
              Display: "France",
              Order: "072",
            },
            {
              Value: "GF",
              Display: "French Guiana",
              Order: "073",
            },
            {
              Value: "PF",
              Display: "French Polynesia",
              Order: "074",
            },
            {
              Value: "GA",
              Display: "Gabon",
              Order: "075",
            },
            {
              Value: "GM",
              Display: "Gambia",
              Order: "076",
            },
            {
              Value: "GZ",
              Display: "Gaza Strip",
              Order: "077",
            },
            {
              Value: "GE",
              Display: "Georgia",
              Order: "078",
            },
            {
              Value: "DE",
              Display: "Germany",
              Order: "079",
            },
            {
              Value: "GH",
              Display: "Ghana",
              Order: "080",
            },
            {
              Value: "GI",
              Display: "Gibraltar",
              Order: "081",
            },
            {
              Value: "GR",
              Display: "Greece",
              Order: "082",
            },
            {
              Value: "GL",
              Display: "Greenland",
              Order: "083",
            },
            {
              Value: "GD",
              Display: "Grenada",
              Order: "084",
            },
            {
              Value: "GP",
              Display: "Guadeloupe",
              Order: "085",
            },
            {
              Value: "GT",
              Display: "Guatemala",
              Order: "086",
            },
            {
              Value: "GG",
              Display: "Guernsey",
              Order: "087",
            },
            {
              Value: "GN",
              Display: "Guinea",
              Order: "088",
            },
            {
              Value: "GW",
              Display: "Guinea-Bissau",
              Order: "089",
            },
            {
              Value: "GY",
              Display: "Guyana",
              Order: "090",
            },
            {
              Value: "HT",
              Display: "Haiti",
              Order: "091",
            },
            {
              Value: "VA",
              Display: "Holy See [Vatican City State]",
              Order: "092",
            },
            {
              Value: "HN",
              Display: "Honduras",
              Order: "093",
            },
            {
              Value: "HK",
              Display: "Hong Kong",
              Order: "094",
            },
            {
              Value: "HU",
              Display: "Hungary",
              Order: "095",
            },
            {
              Value: "IS",
              Display: "Iceland",
              Order: "096",
            },
            {
              Value: "IN",
              Display: "India",
              Order: "097",
            },
            {
              Value: "ID",
              Display: "Indonesia",
              Order: "098",
            },
            {
              Value: "IR",
              Display: "Iran (Islamic Republic of)",
              Order: "099",
            },
            {
              Value: "IQ",
              Display: "Iraq",
              Order: "100",
            },
            {
              Value: "IE",
              Display: "Ireland",
              Order: "101",
            },
            {
              Value: "IM",
              Display: "Isle of Man",
              Order: "102",
            },
            {
              Value: "IL",
              Display: "Israel",
              Order: "103",
            },
            {
              Value: "IT",
              Display: "Italy",
              Order: "104",
            },
            {
              Value: "JM",
              Display: "Jamaica",
              Order: "105",
            },
            {
              Value: "JP",
              Display: "Japan",
              Order: "106",
            },
            {
              Value: "JE",
              Display: "Jersey",
              Order: "107",
            },
            {
              Value: "JO",
              Display: "Jordan",
              Order: "108",
            },
            {
              Value: "KZ",
              Display: "Kazakhstan",
              Order: "109",
            },
            {
              Value: "KE",
              Display: "Kenya",
              Order: "110",
            },
            {
              Value: "KI",
              Display: "Kiribati",
              Order: "111",
            },
            {
              Value: "KP",
              Display: "Korea (the Democratic People's Republic of)",
              Order: "112",
            },
            {
              Value: "KR",
              Display: "Korea (the Republic of)",
              Order: "113",
            },
            {
              Value: "KS",
              Display: "Kosovo",
              Order: "114",
            },
            {
              Value: "KW",
              Display: "Kuwait",
              Order: "115",
            },
            {
              Value: "KG",
              Display: "Kyrgyzstan",
              Order: "116",
            },
            {
              Value: "LA",
              Display: "Lao People's Democratic Republic (the)",
              Order: "117",
            },
            {
              Value: "LV",
              Display: "Latvia",
              Order: "118",
            },
            {
              Value: "LB",
              Display: "Lebanon",
              Order: "119",
            },
            {
              Value: "LS",
              Display: "Lesotho",
              Order: "120",
            },
            {
              Value: "LR",
              Display: "Liberia",
              Order: "121",
            },
            {
              Value: "LY",
              Display: "Libya",
              Order: "122",
            },
            {
              Value: "LI",
              Display: "Liechtenstein",
              Order: "123",
            },
            {
              Value: "LT",
              Display: "Lithuania",
              Order: "124",
            },
            {
              Value: "LU",
              Display: "Luxembourg",
              Order: "125",
            },
            {
              Value: "MO",
              Display: "Macao",
              Order: "126",
            },
            {
              Value: "MG",
              Display: "Madagascar",
              Order: "127",
            },
            {
              Value: "MW",
              Display: "Malawi",
              Order: "128",
            },
            {
              Value: "MY",
              Display: "Malaysia",
              Order: "129",
            },
            {
              Value: "MV",
              Display: "Maldives",
              Order: "130",
            },
            {
              Value: "ML",
              Display: "Mali",
              Order: "131",
            },
            {
              Value: "MT",
              Display: "Malta",
              Order: "132",
            },
            {
              Value: "MQ",
              Display: "Martinique",
              Order: "133",
            },
            {
              Value: "MR",
              Display: "Mauritania",
              Order: "134",
            },
            {
              Value: "MU",
              Display: "Mauritius",
              Order: "135",
            },
            {
              Value: "YT",
              Display: "Mayotte",
              Order: "136",
            },
            {
              Value: "MX",
              Display: "Mexico",
              Order: "137",
            },
            {
              Value: "MD",
              Display: "Moldova (the Republic of)",
              Order: "138",
            },
            {
              Value: "MC",
              Display: "Monaco",
              Order: "139",
            },
            {
              Value: "MN",
              Display: "Mongolia",
              Order: "140",
            },
            {
              Value: "ME",
              Display: "Montenegro",
              Order: "141",
            },
            {
              Value: "MS",
              Display: "Montserrat",
              Order: "142",
            },
            {
              Value: "MA",
              Display: "Morocco",
              Order: "143",
            },
            {
              Value: "MZ",
              Display: "Mozambique",
              Order: "144",
            },
            {
              Value: "MM",
              Display: "Myanmar",
              Order: "145",
            },
            {
              Value: "NA",
              Display: "Namibia",
              Order: "146",
            },
            {
              Value: "NR",
              Display: "Nauru",
              Order: "147",
            },
            {
              Value: "NP",
              Display: "Nepal",
              Order: "148",
            },
            {
              Value: "NL",
              Display: "Netherlands",
              Order: "149",
            },
            {
              Value: "NC",
              Display: "New Caledonia",
              Order: "150",
            },
            {
              Value: "NZ",
              Display: "New Zealand",
              Order: "151",
            },
            {
              Value: "NI",
              Display: "Nicaragua",
              Order: "152",
            },
            {
              Value: "NE",
              Display: "Niger",
              Order: "153",
            },
            {
              Value: "NG",
              Display: "Nigeria",
              Order: "154",
            },
            {
              Value: "NU",
              Display: "Niue",
              Order: "155",
            },
            {
              Value: "MK",
              Display: "North Macedonia",
              Order: "156",
            },
            {
              Value: "NO",
              Display: "Norway",
              Order: "157",
            },
            {
              Value: "OM",
              Display: "Oman",
              Order: "158",
            },
            {
              Value: "PK",
              Display: "Pakistan",
              Order: "159",
            },
            {
              Value: "PS",
              Display: "Palestine, State of",
              Order: "160",
            },
            {
              Value: "PA",
              Display: "Panama",
              Order: "161",
            },
            {
              Value: "PG",
              Display: "Papua New Guinea",
              Order: "162",
            },
            {
              Value: "PY",
              Display: "Paraguay",
              Order: "163",
            },
            {
              Value: "PE",
              Display: "Peru",
              Order: "164",
            },
            {
              Value: "PH",
              Display: "Philippines",
              Order: "165",
            },
            {
              Value: "PL",
              Display: "Poland",
              Order: "166",
            },
            {
              Value: "PT",
              Display: "Portugal",
              Order: "167",
            },
            {
              Value: "QA",
              Display: "Qatar",
              Order: "168",
            },
            {
              Value: "RE",
              Display: "Réunion",
              Order: "169",
            },
            {
              Value: "RO",
              Display: "Romania",
              Order: "170",
            },
            {
              Value: "RU",
              Display: "Russian Federation",
              Order: "171",
            },
            {
              Value: "RW",
              Display: "Rwanda",
              Order: "172",
            },
            {
              Value: "BL",
              Display: "Saint Barthélemy",
              Order: "173",
            },
            {
              Value: "SH",
              Display: "Saint Helena, Ascension and Tristan da Cunha",
              Order: "174",
            },
            {
              Value: "KN",
              Display: "Saint Kitts and Nevis",
              Order: "175",
            },
            {
              Value: "LC",
              Display: "Saint Lucia",
              Order: "176",
            },
            {
              Value: "MF",
              Display: "Saint Martin (French part)",
              Order: "177",
            },
            {
              Value: "PM",
              Display: "Saint Pierre and Miquelon",
              Order: "178",
            },
            {
              Value: "VC",
              Display: "Saint Vincent and the Grenadines",
              Order: "179",
            },
            {
              Value: "WS",
              Display: "Samoa",
              Order: "180",
            },
            {
              Value: "SM",
              Display: "San Marino",
              Order: "181",
            },
            {
              Value: "ST",
              Display: "Sao Tome and Principe",
              Order: "182",
            },
            {
              Value: "SA",
              Display: "Saudi Arabia",
              Order: "183",
            },
            {
              Value: "SN",
              Display: "Senegal",
              Order: "184",
            },
            {
              Value: "RS",
              Display: "Serbia",
              Order: "185",
            },
            {
              Value: "SC",
              Display: "Seychelles",
              Order: "186",
            },
            {
              Value: "SL",
              Display: "Sierra Leone",
              Order: "187",
            },
            {
              Value: "SG",
              Display: "Singapore",
              Order: "188",
            },
            {
              Value: "SX",
              Display: "Sint Maarten (Dutch part)",
              Order: "189",
            },
            {
              Value: "SK",
              Display: "Slovakia",
              Order: "190",
            },
            {
              Value: "SI",
              Display: "Slovenia",
              Order: "191",
            },
            {
              Value: "SB",
              Display: "Solomon Islands",
              Order: "192",
            },
            {
              Value: "SO",
              Display: "Somalia",
              Order: "193",
            },
            {
              Value: "ZA",
              Display: "South Africa",
              Order: "194",
            },
            {
              Value: "SS",
              Display: "South Sudan",
              Order: "195",
            },
            {
              Value: "ES",
              Display: "Spain",
              Order: "196",
            },
            {
              Value: "LK",
              Display: "Sri Lanka",
              Order: "197",
            },
            {
              Value: "SD",
              Display: "Sudan",
              Order: "198",
            },
            {
              Value: "SR",
              Display: "Suriname",
              Order: "199",
            },
            {
              Value: "SE",
              Display: "Sweden",
              Order: "200",
            },
            {
              Value: "CH",
              Display: "Switzerland",
              Order: "201",
            },
            {
              Value: "SY",
              Display: "Syrian Arab Republic",
              Order: "202",
            },
            {
              Value: "TW",
              Display: "Taiwan (Province of China)",
              Order: "203",
            },
            {
              Value: "TJ",
              Display: "Tajikistan",
              Order: "204",
            },
            {
              Value: "TZ",
              Display: "Tanzania, United Republic of",
              Order: "205",
            },
            {
              Value: "TH",
              Display: "Thailand",
              Order: "206",
            },
            {
              Value: "TL",
              Display: "Timor-Leste",
              Order: "207",
            },
            {
              Value: "TG",
              Display: "Togo",
              Order: "208",
            },
            {
              Value: "TK",
              Display: "Tokelau",
              Order: "209",
            },
            {
              Value: "TO",
              Display: "Tonga",
              Order: "210",
            },
            {
              Value: "TT",
              Display: "Trinidad and Tobago",
              Order: "211",
            },
            {
              Value: "TN",
              Display: "Tunisia",
              Order: "212",
            },
            {
              Value: "TR",
              Display: "Turkey",
              Order: "213",
            },
            {
              Value: "TM",
              Display: "Turkmenistan",
              Order: "214",
            },
            {
              Value: "TC",
              Display: "Turks and Caicos Islands",
              Order: "215",
            },
            {
              Value: "TV",
              Display: "Tuvalu",
              Order: "216",
            },
            {
              Value: "UG",
              Display: "Uganda",
              Order: "217",
            },
            {
              Value: "UA",
              Display: "Ukraine",
              Order: "218",
            },
            {
              Value: "AE",
              Display: "United Arab Emirates",
              Order: "219",
            },
            {
              Value: "GB",
              Display: "United Kingdom ",
              Order: "220",
            },
            {
              Value: "US",
              Display: "United States",
              Order: "001",
            },
            {
              Value: "UY",
              Display: "Uruguay",
              Order: "221",
            },
            {
              Value: "UZ",
              Display: "Uzbekistan",
              Order: "222",
            },
            {
              Value: "VU",
              Display: "Vanuatu",
              Order: "223",
            },
            {
              Value: "VE",
              Display: "Venezuela (Bolivarian Republic of)",
              Order: "224",
            },
            {
              Value: "VN",
              Display: "Viet Nam",
              Order: "225",
            },
            {
              Value: "VG",
              Display: "Virgin Islands (British)",
              Order: "226",
            },
            {
              Value: "WF",
              Display: "Wallis and Futuna",
              Order: "227",
            },
            {
              Value: "WE",
              Display: "West Bank",
              Order: "228",
            },
            {
              Value: "EH",
              Display: "Western Sahara",
              Order: "229",
            },
            {
              Value: "YE",
              Display: "Yemen",
              Order: "230",
            },
            {
              Value: "ZM",
              Display: "Zambia",
              Order: "231",
            },
            {
              Value: "ZW",
              Display: "Zimbabwe",
              Order: "232",
            },
          ],
        },
        {
          Display: "VisaType",
          Name: "VisaType",
          DictItems: [
            {
              Value: "F1",
              Display: "F-1",
              Order: "001",
            },
            {
              Value: "F2",
              Display: "F-2",
              Order: "002",
            },
            {
              Value: "J1",
              Display: "J-1",
              Order: "003",
            },
            {
              Value: "J2",
              Display: "J-2",
              Order: "004",
            },
            {
              Value: "G1",
              Display: "G-1",
              Order: "005",
            },
            {
              Value: "G2",
              Display: "G-2",
              Order: "006",
            },
            {
              Value: "G3",
              Display: "G-3",
              Order: "007",
            },
            {
              Value: "G4",
              Display: "G-4",
              Order: "008",
            },
            {
              Value: "H",
              Display: "H",
              Order: "009",
            },
            {
              Value: "Other",
              Display: "Other",
              Order: "010",
            },
          ],
        },
      ],
      Count: 0,
    },
  },
  {
    PageName: "StudentInfo9",
    PageHeading: "Student's Permanent Address",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Tell us your full address",
      },
      {
        itemType: "question",
        id: "f_stu_perm_addr1",
        questionValue: "5 Main St",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "",
        maxLength: "40",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Street address",
      },
      {
        itemType: "question",
        id: "f_stu_perm_addr2",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "",
        maxLength: "40",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Street address (line 2)",
      },
      {
        itemType: "question",
        id: "f_stu_perm_city",
        questionValue: "Westford",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "",
        maxLength: "30",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "City",
      },
      {
        itemType: "question",
        id: "f_stu_perm_country2",
        questionValue: "US",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "select",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "Country",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "Y",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "Y",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Country",
      },
      {
        itemType: "question",
        id: "f_stu_perm_st",
        questionValue: "MA",
        required: "N",
        requiredErrMsg: "",
        fieldType: "select",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "State",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "f_stu_perm_country2",
        condDsblParVal: "",
        condDsblParValNOTEQ: ";US;CA;",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "f_stu_perm_country2",
        condReqParVal: ";US;CA;",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "State/Province",
      },
      {
        itemType: "question",
        id: "f_stu_perm_zip",
        questionValue: "01886",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "20",
        minLength: "",
        maxLength: "17",
        format: "zip",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "f_stu_perm_country2",
        condReqParVal: ";US;CA;",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "ZIP/Postal Code",
      },
    ],
    FWDictsList: {
      Dicts: [
        {
          Display: "Country",
          Name: "Country",
          DictItems: [
            {
              Value: "AF",
              Display: "Afghanistan",
              Order: "002",
            },
            {
              Value: "AX",
              Display: "Åland Islands",
              Order: "003",
            },
            {
              Value: "AL",
              Display: "Albania",
              Order: "004",
            },
            {
              Value: "DZ",
              Display: "Algeria",
              Order: "005",
            },
            {
              Value: "AD",
              Display: "Andorra",
              Order: "006",
            },
            {
              Value: "AO",
              Display: "Angola",
              Order: "007",
            },
            {
              Value: "AI",
              Display: "Anguilla",
              Order: "008",
            },
            {
              Value: "AG",
              Display: "Antigua and Barbuda",
              Order: "009",
            },
            {
              Value: "AR",
              Display: "Argentina",
              Order: "010",
            },
            {
              Value: "AM",
              Display: "Armenia",
              Order: "011",
            },
            {
              Value: "AW",
              Display: "Aruba",
              Order: "012",
            },
            {
              Value: "AU",
              Display: "Australia",
              Order: "013",
            },
            {
              Value: "AT",
              Display: "Austria",
              Order: "014",
            },
            {
              Value: "AZ",
              Display: "Azerbaijan",
              Order: "015",
            },
            {
              Value: "BS",
              Display: "Bahamas",
              Order: "016",
            },
            {
              Value: "BH",
              Display: "Bahrain",
              Order: "017",
            },
            {
              Value: "BD",
              Display: "Bangladesh",
              Order: "018",
            },
            {
              Value: "BB",
              Display: "Barbados",
              Order: "019",
            },
            {
              Value: "BY",
              Display: "Belarus",
              Order: "020",
            },
            {
              Value: "BE",
              Display: "Belgium",
              Order: "021",
            },
            {
              Value: "BZ",
              Display: "Belize",
              Order: "022",
            },
            {
              Value: "BJ",
              Display: "Benin",
              Order: "023",
            },
            {
              Value: "BM",
              Display: "Bermuda",
              Order: "024",
            },
            {
              Value: "BT",
              Display: "Bhutan",
              Order: "025",
            },
            {
              Value: "BO",
              Display: "Bolivia (Plurinational State of)",
              Order: "026",
            },
            {
              Value: "BQ",
              Display: "Bonaire, Sint Eustatius and Saba",
              Order: "027",
            },
            {
              Value: "BA",
              Display: "Bosnia and Herzegovina",
              Order: "028",
            },
            {
              Value: "BW",
              Display: "Botswana",
              Order: "029",
            },
            {
              Value: "BR",
              Display: "Brazil",
              Order: "030",
            },
            {
              Value: "BN",
              Display: "Brunei Darussalam",
              Order: "031",
            },
            {
              Value: "BG",
              Display: "Bulgaria",
              Order: "032",
            },
            {
              Value: "BF",
              Display: "Burkina Faso",
              Order: "033",
            },
            {
              Value: "BI",
              Display: "Burundi",
              Order: "034",
            },
            {
              Value: "CV",
              Display: "Cabo Verde",
              Order: "035",
            },
            {
              Value: "KH",
              Display: "Cambodia",
              Order: "036",
            },
            {
              Value: "CM",
              Display: "Cameroon",
              Order: "037",
            },
            {
              Value: "CA",
              Display: "Canada",
              Order: "038",
            },
            {
              Value: "KY",
              Display: "Cayman Islands",
              Order: "039",
            },
            {
              Value: "CF",
              Display: "Central African Republic",
              Order: "040",
            },
            {
              Value: "TD",
              Display: "Chad",
              Order: "041",
            },
            {
              Value: "CL",
              Display: "Chile",
              Order: "042",
            },
            {
              Value: "CN",
              Display: "China",
              Order: "043",
            },
            {
              Value: "CO",
              Display: "Colombia",
              Order: "044",
            },
            {
              Value: "KM",
              Display: "Comoros",
              Order: "045",
            },
            {
              Value: "CG",
              Display: "Congo",
              Order: "046",
            },
            {
              Value: "CD",
              Display: "Congo (the Democratic Republic of the)",
              Order: "047",
            },
            {
              Value: "CK",
              Display: "Cook Islands",
              Order: "048",
            },
            {
              Value: "CR",
              Display: "Costa Rica",
              Order: "049",
            },
            {
              Value: "CI",
              Display: "Côte d'Ivoire",
              Order: "050",
            },
            {
              Value: "HR",
              Display: "Croatia",
              Order: "051",
            },
            {
              Value: "CU",
              Display: "Cuba",
              Order: "052",
            },
            {
              Value: "CW",
              Display: "Curaçao",
              Order: "053",
            },
            {
              Value: "CY",
              Display: "Cyprus",
              Order: "054",
            },
            {
              Value: "CZ",
              Display: "Czechia",
              Order: "055",
            },
            {
              Value: "DK",
              Display: "Denmark",
              Order: "056",
            },
            {
              Value: "DJ",
              Display: "Djibouti",
              Order: "057",
            },
            {
              Value: "DM",
              Display: "Dominica",
              Order: "058",
            },
            {
              Value: "DO",
              Display: "Dominican Republic",
              Order: "059",
            },
            {
              Value: "EC",
              Display: "Ecuador",
              Order: "060",
            },
            {
              Value: "EG",
              Display: "Egypt",
              Order: "061",
            },
            {
              Value: "SV",
              Display: "El Salvador",
              Order: "062",
            },
            {
              Value: "GQ",
              Display: "Equatorial Guinea",
              Order: "063",
            },
            {
              Value: "ER",
              Display: "Eritrea",
              Order: "064",
            },
            {
              Value: "EE",
              Display: "Estonia",
              Order: "065",
            },
            {
              Value: "SZ",
              Display: "Eswatini",
              Order: "066",
            },
            {
              Value: "ET",
              Display: "Ethiopia",
              Order: "067",
            },
            {
              Value: "FK",
              Display: "Falkland Islands [Malvinas]",
              Order: "068",
            },
            {
              Value: "FO",
              Display: "Faroe Islands",
              Order: "069",
            },
            {
              Value: "FJ",
              Display: "Fiji",
              Order: "070",
            },
            {
              Value: "FI",
              Display: "Finland",
              Order: "071",
            },
            {
              Value: "FR",
              Display: "France",
              Order: "072",
            },
            {
              Value: "GF",
              Display: "French Guiana",
              Order: "073",
            },
            {
              Value: "PF",
              Display: "French Polynesia",
              Order: "074",
            },
            {
              Value: "GA",
              Display: "Gabon",
              Order: "075",
            },
            {
              Value: "GM",
              Display: "Gambia",
              Order: "076",
            },
            {
              Value: "GZ",
              Display: "Gaza Strip",
              Order: "077",
            },
            {
              Value: "GE",
              Display: "Georgia",
              Order: "078",
            },
            {
              Value: "DE",
              Display: "Germany",
              Order: "079",
            },
            {
              Value: "GH",
              Display: "Ghana",
              Order: "080",
            },
            {
              Value: "GI",
              Display: "Gibraltar",
              Order: "081",
            },
            {
              Value: "GR",
              Display: "Greece",
              Order: "082",
            },
            {
              Value: "GL",
              Display: "Greenland",
              Order: "083",
            },
            {
              Value: "GD",
              Display: "Grenada",
              Order: "084",
            },
            {
              Value: "GP",
              Display: "Guadeloupe",
              Order: "085",
            },
            {
              Value: "GT",
              Display: "Guatemala",
              Order: "086",
            },
            {
              Value: "GG",
              Display: "Guernsey",
              Order: "087",
            },
            {
              Value: "GN",
              Display: "Guinea",
              Order: "088",
            },
            {
              Value: "GW",
              Display: "Guinea-Bissau",
              Order: "089",
            },
            {
              Value: "GY",
              Display: "Guyana",
              Order: "090",
            },
            {
              Value: "HT",
              Display: "Haiti",
              Order: "091",
            },
            {
              Value: "VA",
              Display: "Holy See [Vatican City State]",
              Order: "092",
            },
            {
              Value: "HN",
              Display: "Honduras",
              Order: "093",
            },
            {
              Value: "HK",
              Display: "Hong Kong",
              Order: "094",
            },
            {
              Value: "HU",
              Display: "Hungary",
              Order: "095",
            },
            {
              Value: "IS",
              Display: "Iceland",
              Order: "096",
            },
            {
              Value: "IN",
              Display: "India",
              Order: "097",
            },
            {
              Value: "ID",
              Display: "Indonesia",
              Order: "098",
            },
            {
              Value: "IR",
              Display: "Iran (Islamic Republic of)",
              Order: "099",
            },
            {
              Value: "IQ",
              Display: "Iraq",
              Order: "100",
            },
            {
              Value: "IE",
              Display: "Ireland",
              Order: "101",
            },
            {
              Value: "IM",
              Display: "Isle of Man",
              Order: "102",
            },
            {
              Value: "IL",
              Display: "Israel",
              Order: "103",
            },
            {
              Value: "IT",
              Display: "Italy",
              Order: "104",
            },
            {
              Value: "JM",
              Display: "Jamaica",
              Order: "105",
            },
            {
              Value: "JP",
              Display: "Japan",
              Order: "106",
            },
            {
              Value: "JE",
              Display: "Jersey",
              Order: "107",
            },
            {
              Value: "JO",
              Display: "Jordan",
              Order: "108",
            },
            {
              Value: "KZ",
              Display: "Kazakhstan",
              Order: "109",
            },
            {
              Value: "KE",
              Display: "Kenya",
              Order: "110",
            },
            {
              Value: "KI",
              Display: "Kiribati",
              Order: "111",
            },
            {
              Value: "KP",
              Display: "Korea (the Democratic People's Republic of)",
              Order: "112",
            },
            {
              Value: "KR",
              Display: "Korea (the Republic of)",
              Order: "113",
            },
            {
              Value: "KS",
              Display: "Kosovo",
              Order: "114",
            },
            {
              Value: "KW",
              Display: "Kuwait",
              Order: "115",
            },
            {
              Value: "KG",
              Display: "Kyrgyzstan",
              Order: "116",
            },
            {
              Value: "LA",
              Display: "Lao People's Democratic Republic (the)",
              Order: "117",
            },
            {
              Value: "LV",
              Display: "Latvia",
              Order: "118",
            },
            {
              Value: "LB",
              Display: "Lebanon",
              Order: "119",
            },
            {
              Value: "LS",
              Display: "Lesotho",
              Order: "120",
            },
            {
              Value: "LR",
              Display: "Liberia",
              Order: "121",
            },
            {
              Value: "LY",
              Display: "Libya",
              Order: "122",
            },
            {
              Value: "LI",
              Display: "Liechtenstein",
              Order: "123",
            },
            {
              Value: "LT",
              Display: "Lithuania",
              Order: "124",
            },
            {
              Value: "LU",
              Display: "Luxembourg",
              Order: "125",
            },
            {
              Value: "MO",
              Display: "Macao",
              Order: "126",
            },
            {
              Value: "MG",
              Display: "Madagascar",
              Order: "127",
            },
            {
              Value: "MW",
              Display: "Malawi",
              Order: "128",
            },
            {
              Value: "MY",
              Display: "Malaysia",
              Order: "129",
            },
            {
              Value: "MV",
              Display: "Maldives",
              Order: "130",
            },
            {
              Value: "ML",
              Display: "Mali",
              Order: "131",
            },
            {
              Value: "MT",
              Display: "Malta",
              Order: "132",
            },
            {
              Value: "MQ",
              Display: "Martinique",
              Order: "133",
            },
            {
              Value: "MR",
              Display: "Mauritania",
              Order: "134",
            },
            {
              Value: "MU",
              Display: "Mauritius",
              Order: "135",
            },
            {
              Value: "YT",
              Display: "Mayotte",
              Order: "136",
            },
            {
              Value: "MX",
              Display: "Mexico",
              Order: "137",
            },
            {
              Value: "MD",
              Display: "Moldova (the Republic of)",
              Order: "138",
            },
            {
              Value: "MC",
              Display: "Monaco",
              Order: "139",
            },
            {
              Value: "MN",
              Display: "Mongolia",
              Order: "140",
            },
            {
              Value: "ME",
              Display: "Montenegro",
              Order: "141",
            },
            {
              Value: "MS",
              Display: "Montserrat",
              Order: "142",
            },
            {
              Value: "MA",
              Display: "Morocco",
              Order: "143",
            },
            {
              Value: "MZ",
              Display: "Mozambique",
              Order: "144",
            },
            {
              Value: "MM",
              Display: "Myanmar",
              Order: "145",
            },
            {
              Value: "NA",
              Display: "Namibia",
              Order: "146",
            },
            {
              Value: "NR",
              Display: "Nauru",
              Order: "147",
            },
            {
              Value: "NP",
              Display: "Nepal",
              Order: "148",
            },
            {
              Value: "NL",
              Display: "Netherlands",
              Order: "149",
            },
            {
              Value: "NC",
              Display: "New Caledonia",
              Order: "150",
            },
            {
              Value: "NZ",
              Display: "New Zealand",
              Order: "151",
            },
            {
              Value: "NI",
              Display: "Nicaragua",
              Order: "152",
            },
            {
              Value: "NE",
              Display: "Niger",
              Order: "153",
            },
            {
              Value: "NG",
              Display: "Nigeria",
              Order: "154",
            },
            {
              Value: "NU",
              Display: "Niue",
              Order: "155",
            },
            {
              Value: "MK",
              Display: "North Macedonia",
              Order: "156",
            },
            {
              Value: "NO",
              Display: "Norway",
              Order: "157",
            },
            {
              Value: "OM",
              Display: "Oman",
              Order: "158",
            },
            {
              Value: "PK",
              Display: "Pakistan",
              Order: "159",
            },
            {
              Value: "PS",
              Display: "Palestine, State of",
              Order: "160",
            },
            {
              Value: "PA",
              Display: "Panama",
              Order: "161",
            },
            {
              Value: "PG",
              Display: "Papua New Guinea",
              Order: "162",
            },
            {
              Value: "PY",
              Display: "Paraguay",
              Order: "163",
            },
            {
              Value: "PE",
              Display: "Peru",
              Order: "164",
            },
            {
              Value: "PH",
              Display: "Philippines",
              Order: "165",
            },
            {
              Value: "PL",
              Display: "Poland",
              Order: "166",
            },
            {
              Value: "PT",
              Display: "Portugal",
              Order: "167",
            },
            {
              Value: "QA",
              Display: "Qatar",
              Order: "168",
            },
            {
              Value: "RE",
              Display: "Réunion",
              Order: "169",
            },
            {
              Value: "RO",
              Display: "Romania",
              Order: "170",
            },
            {
              Value: "RU",
              Display: "Russian Federation",
              Order: "171",
            },
            {
              Value: "RW",
              Display: "Rwanda",
              Order: "172",
            },
            {
              Value: "BL",
              Display: "Saint Barthélemy",
              Order: "173",
            },
            {
              Value: "SH",
              Display: "Saint Helena, Ascension and Tristan da Cunha",
              Order: "174",
            },
            {
              Value: "KN",
              Display: "Saint Kitts and Nevis",
              Order: "175",
            },
            {
              Value: "LC",
              Display: "Saint Lucia",
              Order: "176",
            },
            {
              Value: "MF",
              Display: "Saint Martin (French part)",
              Order: "177",
            },
            {
              Value: "PM",
              Display: "Saint Pierre and Miquelon",
              Order: "178",
            },
            {
              Value: "VC",
              Display: "Saint Vincent and the Grenadines",
              Order: "179",
            },
            {
              Value: "WS",
              Display: "Samoa",
              Order: "180",
            },
            {
              Value: "SM",
              Display: "San Marino",
              Order: "181",
            },
            {
              Value: "ST",
              Display: "Sao Tome and Principe",
              Order: "182",
            },
            {
              Value: "SA",
              Display: "Saudi Arabia",
              Order: "183",
            },
            {
              Value: "SN",
              Display: "Senegal",
              Order: "184",
            },
            {
              Value: "RS",
              Display: "Serbia",
              Order: "185",
            },
            {
              Value: "SC",
              Display: "Seychelles",
              Order: "186",
            },
            {
              Value: "SL",
              Display: "Sierra Leone",
              Order: "187",
            },
            {
              Value: "SG",
              Display: "Singapore",
              Order: "188",
            },
            {
              Value: "SX",
              Display: "Sint Maarten (Dutch part)",
              Order: "189",
            },
            {
              Value: "SK",
              Display: "Slovakia",
              Order: "190",
            },
            {
              Value: "SI",
              Display: "Slovenia",
              Order: "191",
            },
            {
              Value: "SB",
              Display: "Solomon Islands",
              Order: "192",
            },
            {
              Value: "SO",
              Display: "Somalia",
              Order: "193",
            },
            {
              Value: "ZA",
              Display: "South Africa",
              Order: "194",
            },
            {
              Value: "SS",
              Display: "South Sudan",
              Order: "195",
            },
            {
              Value: "ES",
              Display: "Spain",
              Order: "196",
            },
            {
              Value: "LK",
              Display: "Sri Lanka",
              Order: "197",
            },
            {
              Value: "SD",
              Display: "Sudan",
              Order: "198",
            },
            {
              Value: "SR",
              Display: "Suriname",
              Order: "199",
            },
            {
              Value: "SE",
              Display: "Sweden",
              Order: "200",
            },
            {
              Value: "CH",
              Display: "Switzerland",
              Order: "201",
            },
            {
              Value: "SY",
              Display: "Syrian Arab Republic",
              Order: "202",
            },
            {
              Value: "TW",
              Display: "Taiwan (Province of China)",
              Order: "203",
            },
            {
              Value: "TJ",
              Display: "Tajikistan",
              Order: "204",
            },
            {
              Value: "TZ",
              Display: "Tanzania, United Republic of",
              Order: "205",
            },
            {
              Value: "TH",
              Display: "Thailand",
              Order: "206",
            },
            {
              Value: "TL",
              Display: "Timor-Leste",
              Order: "207",
            },
            {
              Value: "TG",
              Display: "Togo",
              Order: "208",
            },
            {
              Value: "TK",
              Display: "Tokelau",
              Order: "209",
            },
            {
              Value: "TO",
              Display: "Tonga",
              Order: "210",
            },
            {
              Value: "TT",
              Display: "Trinidad and Tobago",
              Order: "211",
            },
            {
              Value: "TN",
              Display: "Tunisia",
              Order: "212",
            },
            {
              Value: "TR",
              Display: "Turkey",
              Order: "213",
            },
            {
              Value: "TM",
              Display: "Turkmenistan",
              Order: "214",
            },
            {
              Value: "TC",
              Display: "Turks and Caicos Islands",
              Order: "215",
            },
            {
              Value: "TV",
              Display: "Tuvalu",
              Order: "216",
            },
            {
              Value: "UG",
              Display: "Uganda",
              Order: "217",
            },
            {
              Value: "UA",
              Display: "Ukraine",
              Order: "218",
            },
            {
              Value: "AE",
              Display: "United Arab Emirates",
              Order: "219",
            },
            {
              Value: "GB",
              Display: "United Kingdom ",
              Order: "220",
            },
            {
              Value: "US",
              Display: "United States",
              Order: "001",
            },
            {
              Value: "UY",
              Display: "Uruguay",
              Order: "221",
            },
            {
              Value: "UZ",
              Display: "Uzbekistan",
              Order: "222",
            },
            {
              Value: "VU",
              Display: "Vanuatu",
              Order: "223",
            },
            {
              Value: "VE",
              Display: "Venezuela (Bolivarian Republic of)",
              Order: "224",
            },
            {
              Value: "VN",
              Display: "Viet Nam",
              Order: "225",
            },
            {
              Value: "VG",
              Display: "Virgin Islands (British)",
              Order: "226",
            },
            {
              Value: "WF",
              Display: "Wallis and Futuna",
              Order: "227",
            },
            {
              Value: "WE",
              Display: "West Bank",
              Order: "228",
            },
            {
              Value: "EH",
              Display: "Western Sahara",
              Order: "229",
            },
            {
              Value: "YE",
              Display: "Yemen",
              Order: "230",
            },
            {
              Value: "ZM",
              Display: "Zambia",
              Order: "231",
            },
            {
              Value: "ZW",
              Display: "Zimbabwe",
              Order: "232",
            },
          ],
        },
        {
          Display: "State",
          Name: "State",
          DictItems: [
            {
              Value: "AL",
              Display: "Alabama",
              Order: "001",
            },
            {
              Value: "AK",
              Display: "Alaska",
              Order: "002",
            },
            {
              Value: "AB",
              Display: "Alberta",
              Order: "003",
            },
            {
              Value: "AS",
              Display: "American Samoa",
              Order: "004",
            },
            {
              Value: "AZ",
              Display: "Arizona",
              Order: "005",
            },
            {
              Value: "AR",
              Display: "Arkansas",
              Order: "006",
            },
            {
              Value: "AA",
              Display: "Armed Forces Americas",
              Order: "007",
            },
            {
              Value: "AE",
              Display: "Armed Forces Europe, Canada, M.E.",
              Order: "008",
            },
            {
              Value: "AP",
              Display: "Armed Forces Pacific",
              Order: "009",
            },
            {
              Value: "BC",
              Display: "British Columbia",
              Order: "010",
            },
            {
              Value: "CA",
              Display: "California",
              Order: "011",
            },
            {
              Value: "CO",
              Display: "Colorado",
              Order: "013",
            },
            {
              Value: "CT",
              Display: "Connecticut",
              Order: "014",
            },
            {
              Value: "DE",
              Display: "Delaware",
              Order: "015",
            },
            {
              Value: "DC",
              Display: "District of Columbia",
              Order: "016",
            },
            {
              Value: "FM",
              Display: "Federated States of Micronesia",
              Order: "017",
            },
            {
              Value: "FL",
              Display: "Florida",
              Order: "018",
            },
            {
              Value: "GA",
              Display: "Georgia",
              Order: "020",
            },
            {
              Value: "GU",
              Display: "Guam",
              Order: "021",
            },
            {
              Value: "HI",
              Display: "Hawaii",
              Order: "022",
            },
            {
              Value: "ID",
              Display: "Idaho",
              Order: "023",
            },
            {
              Value: "IL",
              Display: "Illinois",
              Order: "024",
            },
            {
              Value: "IN",
              Display: "Indiana",
              Order: "025",
            },
            {
              Value: "IA",
              Display: "Iowa",
              Order: "026",
            },
            {
              Value: "KS",
              Display: "Kansas",
              Order: "027",
            },
            {
              Value: "KY",
              Display: "Kentucky",
              Order: "028",
            },
            {
              Value: "LA",
              Display: "Louisiana",
              Order: "029",
            },
            {
              Value: "ME",
              Display: "Maine",
              Order: "030",
            },
            {
              Value: "MB",
              Display: "Manitoba",
              Order: "031",
            },
            {
              Value: "MH",
              Display: "Marshall Islands",
              Order: "032",
            },
            {
              Value: "MD",
              Display: "Maryland",
              Order: "033",
            },
            {
              Value: "MA",
              Display: "Massachusetts",
              Order: "034",
            },
            {
              Value: "MI",
              Display: "Michigan",
              Order: "036",
            },
            {
              Value: "MN",
              Display: "Minnesota",
              Order: "037",
            },
            {
              Value: "MS",
              Display: "Mississippi",
              Order: "038",
            },
            {
              Value: "MO",
              Display: "Missouri",
              Order: "039",
            },
            {
              Value: "MT",
              Display: "Montana",
              Order: "040",
            },
            {
              Value: "NE",
              Display: "Nebraska",
              Order: "041",
            },
            {
              Value: "NV",
              Display: "Nevada",
              Order: "042",
            },
            {
              Value: "NB",
              Display: "New Brunswick",
              Order: "043",
            },
            {
              Value: "NH",
              Display: "New Hampshire",
              Order: "044",
            },
            {
              Value: "NJ",
              Display: "New Jersey",
              Order: "045",
            },
            {
              Value: "NM",
              Display: "New Mexico",
              Order: "046",
            },
            {
              Value: "NY",
              Display: "New York",
              Order: "047",
            },
            {
              Value: "NF",
              Display: "Newfoundland",
              Order: "048",
            },
            {
              Value: "NL",
              Display: "Newfoundland and Labrador",
              Order: "049",
            },
            {
              Value: "NC",
              Display: "North Carolina",
              Order: "050",
            },
            {
              Value: "ND",
              Display: "North Dakota",
              Order: "051",
            },
            {
              Value: "MP",
              Display: "Northern Mariana Islands",
              Order: "052",
            },
            {
              Value: "NT",
              Display: "Northwest Territories",
              Order: "053",
            },
            {
              Value: "NS",
              Display: "Nova Scotia",
              Order: "054",
            },
            {
              Value: "NU",
              Display: "Nunavut",
              Order: "055",
            },
            {
              Value: "OH",
              Display: "Ohio",
              Order: "056",
            },
            {
              Value: "OK",
              Display: "Oklahoma",
              Order: "057",
            },
            {
              Value: "ON",
              Display: "Ontario",
              Order: "058",
            },
            {
              Value: "OR",
              Display: "Oregon",
              Order: "059",
            },
            {
              Value: "PW",
              Display: "Palau",
              Order: "060",
            },
            {
              Value: "PA",
              Display: "Pennsylvania",
              Order: "061",
            },
            {
              Value: "PE",
              Display: "Prince Edward Island",
              Order: "062",
            },
            {
              Value: "PR",
              Display: "Puerto Rico",
              Order: "063",
            },
            {
              Value: "QC",
              Display: "Quebec",
              Order: "064",
            },
            {
              Value: "RI",
              Display: "Rhode Island",
              Order: "065",
            },
            {
              Value: "SK",
              Display: "Saskatchewan",
              Order: "066",
            },
            {
              Value: "SC",
              Display: "South Carolina",
              Order: "067",
            },
            {
              Value: "SD",
              Display: "South Dakota",
              Order: "068",
            },
            {
              Value: "TN",
              Display: "Tennessee",
              Order: "069",
            },
            {
              Value: "TX",
              Display: "Texas",
              Order: "070",
            },
            {
              Value: "UT",
              Display: "Utah",
              Order: "071",
            },
            {
              Value: "VT",
              Display: "Vermont",
              Order: "072",
            },
            {
              Value: "VI",
              Display: "Virgin Islands",
              Order: "073",
            },
            {
              Value: "VA",
              Display: "Virginia",
              Order: "074",
            },
            {
              Value: "WA",
              Display: "Washington",
              Order: "075",
            },
            {
              Value: "WV",
              Display: "West Virginia",
              Order: "076",
            },
            {
              Value: "WI",
              Display: "Wisconsin",
              Order: "077",
            },
            {
              Value: "WY",
              Display: "Wyoming",
              Order: "078",
            },
            {
              Value: "YT",
              Display: "Yukon",
              Order: "079",
            },
            {
              Value: "FC",
              Display: "None of the above",
              Order: "080",
            },
          ],
        },
      ],
      Count: 0,
    },
  },
  {
    PageName: "StudentInfo10",
    PageHeading: "Student's Identification",
    FWFormItemList: [
      {
        itemType: "question",
        id: "f_stu_ssn",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "text",
        size: "9",
        minLength: "9",
        maxLength: "9",
        format: "ssn",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>SSN or SIN</b> - If the student has a SSN or SIN, please enter it.  All information entered on the CSS Profile is securely collected and stored and only shared with the institutions you select.<br />",
        helpStyle: "Notice",
        helpTitle: "",
        textContent:
          "Student's Social Security Number (US) or Social Insurance Number (Canada)",
      },
    ],
    FWDictsList: {
      Dicts: [],
      Count: 0,
    },
  },
  {
    PageName: "StudentInfo11",
    PageHeading: "CBFinAidID",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent:
          "Your CBFinAidID is used to match your application data. If you have one from last year or received one via email, enter it here. You may leave this blank if you do not have your CBFinAidID.",
      },
      {
        itemType: "question",
        id: "CBFinAidIDEntered",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "text",
        size: "7",
        minLength: "7",
        maxLength: "7",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>CBFinAidID</b> - If you have the student's CBFinAId ID from last year or received one in an email, enter it here.  It will help us match this application to other data for this student.<br /> <br />If you don't have a CBFinAid ID leave this question blank.<br />",
        helpStyle: "Notice",
        helpTitle: "",
        textContent: "CBFinAidID",
      },
    ],
    FWDictsList: {
      Dicts: [],
      Count: 0,
    },
  },
  {
    PageName: "ConfirmDemographics",
    PageHeading: "Confirm Demographics",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent:
          "Please confirm this information is correct. It is used by your schools to connect your application to your school record.",
      },
      {
        itemType: "question",
        id: "f_stu_first_name",
        questionValue: "Mary",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "First name",
      },
      {
        itemType: "question",
        id: "f_stu_last_name",
        questionValue: "Test",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Last name",
      },
      {
        itemType: "question",
        id: "f_stu_dob",
        questionValue: "07/12/2002",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "10",
        minLength: "",
        maxLength: "10",
        format: "date",
        displayFormat: "mm/dd/yyyy",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg:
          "Error: The date of birth you entered is either an invalid date or does not fall within the valid date range.  Please re-enter.",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Date of birth",
      },
      {
        itemType: "question",
        id: "StudentCBFinAidID",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "text",
        size: "7",
        minLength: "7",
        maxLength: "7",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "CBFinAidID",
      },
      {
        itemType: "question",
        id: "f_stu_ssn",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "text",
        size: "9",
        minLength: "9",
        maxLength: "9",
        format: "ssn",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "f_stu_citizenship_status",
        condDsblParVal: ";BLANK;3;",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent:
          "Student's Social Security Number (US) or Social Insurance Number (Canada)",
      },
      {
        itemType: "question",
        id: "f_stu_email",
        questionValue: "<EMAIL>",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "45",
        minLength: "",
        maxLength: "50",
        format: "email",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Email address",
      },
      {
        itemType: "question",
        id: "f_stu_phone",
        questionValue: "1232339911",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "15",
        minLength: "10",
        maxLength: "15",
        format: "number",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Phone number",
      },
      {
        itemType: "question",
        id: "f_stu_perm_country2",
        questionValue: "US",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "select",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "Country",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "Y",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "Y",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Country",
      },
      {
        itemType: "question",
        id: "f_stu_perm_addr1",
        questionValue: "5 Main St",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "",
        maxLength: "40",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Street address",
      },
      {
        itemType: "question",
        id: "f_stu_perm_addr2",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "",
        maxLength: "40",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Street address (line 2)",
      },
      {
        itemType: "question",
        id: "f_stu_perm_city",
        questionValue: "Westford",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "",
        maxLength: "30",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "City",
      },
      {
        itemType: "question",
        id: "f_stu_perm_st",
        questionValue: "MA",
        required: "N",
        requiredErrMsg: "",
        fieldType: "select",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "State",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "f_stu_perm_country2",
        condDsblParVal: "",
        condDsblParValNOTEQ: ";US;CA;",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "f_stu_perm_country2",
        condReqParVal: ";US;CA;",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "State/Province",
      },
      {
        itemType: "question",
        id: "f_stu_perm_zip",
        questionValue: "01886",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "20",
        minLength: "",
        maxLength: "17",
        format: "zip",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "f_stu_perm_country2",
        condReqParVal: ";US;CA;",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "ZIP/Postal Code",
      },
      {
        itemType: "question",
        id: "MatchDataConfirmed",
        questionValue: "Y",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "checkbox",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "Y",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "This information is accurate and complete.",
      },
    ],
    FWDictsList: {
      Dicts: [
        {
          Display: "Country",
          Name: "Country",
          DictItems: [
            {
              Value: "AF",
              Display: "Afghanistan",
              Order: "002",
            },
            {
              Value: "AX",
              Display: "Åland Islands",
              Order: "003",
            },
            {
              Value: "AL",
              Display: "Albania",
              Order: "004",
            },
            {
              Value: "DZ",
              Display: "Algeria",
              Order: "005",
            },
            {
              Value: "AD",
              Display: "Andorra",
              Order: "006",
            },
            {
              Value: "AO",
              Display: "Angola",
              Order: "007",
            },
            {
              Value: "AI",
              Display: "Anguilla",
              Order: "008",
            },
            {
              Value: "AG",
              Display: "Antigua and Barbuda",
              Order: "009",
            },
            {
              Value: "AR",
              Display: "Argentina",
              Order: "010",
            },
            {
              Value: "AM",
              Display: "Armenia",
              Order: "011",
            },
            {
              Value: "AW",
              Display: "Aruba",
              Order: "012",
            },
            {
              Value: "AU",
              Display: "Australia",
              Order: "013",
            },
            {
              Value: "AT",
              Display: "Austria",
              Order: "014",
            },
            {
              Value: "AZ",
              Display: "Azerbaijan",
              Order: "015",
            },
            {
              Value: "BS",
              Display: "Bahamas",
              Order: "016",
            },
            {
              Value: "BH",
              Display: "Bahrain",
              Order: "017",
            },
            {
              Value: "BD",
              Display: "Bangladesh",
              Order: "018",
            },
            {
              Value: "BB",
              Display: "Barbados",
              Order: "019",
            },
            {
              Value: "BY",
              Display: "Belarus",
              Order: "020",
            },
            {
              Value: "BE",
              Display: "Belgium",
              Order: "021",
            },
            {
              Value: "BZ",
              Display: "Belize",
              Order: "022",
            },
            {
              Value: "BJ",
              Display: "Benin",
              Order: "023",
            },
            {
              Value: "BM",
              Display: "Bermuda",
              Order: "024",
            },
            {
              Value: "BT",
              Display: "Bhutan",
              Order: "025",
            },
            {
              Value: "BO",
              Display: "Bolivia (Plurinational State of)",
              Order: "026",
            },
            {
              Value: "BQ",
              Display: "Bonaire, Sint Eustatius and Saba",
              Order: "027",
            },
            {
              Value: "BA",
              Display: "Bosnia and Herzegovina",
              Order: "028",
            },
            {
              Value: "BW",
              Display: "Botswana",
              Order: "029",
            },
            {
              Value: "BR",
              Display: "Brazil",
              Order: "030",
            },
            {
              Value: "BN",
              Display: "Brunei Darussalam",
              Order: "031",
            },
            {
              Value: "BG",
              Display: "Bulgaria",
              Order: "032",
            },
            {
              Value: "BF",
              Display: "Burkina Faso",
              Order: "033",
            },
            {
              Value: "BI",
              Display: "Burundi",
              Order: "034",
            },
            {
              Value: "CV",
              Display: "Cabo Verde",
              Order: "035",
            },
            {
              Value: "KH",
              Display: "Cambodia",
              Order: "036",
            },
            {
              Value: "CM",
              Display: "Cameroon",
              Order: "037",
            },
            {
              Value: "CA",
              Display: "Canada",
              Order: "038",
            },
            {
              Value: "KY",
              Display: "Cayman Islands",
              Order: "039",
            },
            {
              Value: "CF",
              Display: "Central African Republic",
              Order: "040",
            },
            {
              Value: "TD",
              Display: "Chad",
              Order: "041",
            },
            {
              Value: "CL",
              Display: "Chile",
              Order: "042",
            },
            {
              Value: "CN",
              Display: "China",
              Order: "043",
            },
            {
              Value: "CO",
              Display: "Colombia",
              Order: "044",
            },
            {
              Value: "KM",
              Display: "Comoros",
              Order: "045",
            },
            {
              Value: "CG",
              Display: "Congo",
              Order: "046",
            },
            {
              Value: "CD",
              Display: "Congo (the Democratic Republic of the)",
              Order: "047",
            },
            {
              Value: "CK",
              Display: "Cook Islands",
              Order: "048",
            },
            {
              Value: "CR",
              Display: "Costa Rica",
              Order: "049",
            },
            {
              Value: "CI",
              Display: "Côte d'Ivoire",
              Order: "050",
            },
            {
              Value: "HR",
              Display: "Croatia",
              Order: "051",
            },
            {
              Value: "CU",
              Display: "Cuba",
              Order: "052",
            },
            {
              Value: "CW",
              Display: "Curaçao",
              Order: "053",
            },
            {
              Value: "CY",
              Display: "Cyprus",
              Order: "054",
            },
            {
              Value: "CZ",
              Display: "Czechia",
              Order: "055",
            },
            {
              Value: "DK",
              Display: "Denmark",
              Order: "056",
            },
            {
              Value: "DJ",
              Display: "Djibouti",
              Order: "057",
            },
            {
              Value: "DM",
              Display: "Dominica",
              Order: "058",
            },
            {
              Value: "DO",
              Display: "Dominican Republic",
              Order: "059",
            },
            {
              Value: "EC",
              Display: "Ecuador",
              Order: "060",
            },
            {
              Value: "EG",
              Display: "Egypt",
              Order: "061",
            },
            {
              Value: "SV",
              Display: "El Salvador",
              Order: "062",
            },
            {
              Value: "GQ",
              Display: "Equatorial Guinea",
              Order: "063",
            },
            {
              Value: "ER",
              Display: "Eritrea",
              Order: "064",
            },
            {
              Value: "EE",
              Display: "Estonia",
              Order: "065",
            },
            {
              Value: "SZ",
              Display: "Eswatini",
              Order: "066",
            },
            {
              Value: "ET",
              Display: "Ethiopia",
              Order: "067",
            },
            {
              Value: "FK",
              Display: "Falkland Islands [Malvinas]",
              Order: "068",
            },
            {
              Value: "FO",
              Display: "Faroe Islands",
              Order: "069",
            },
            {
              Value: "FJ",
              Display: "Fiji",
              Order: "070",
            },
            {
              Value: "FI",
              Display: "Finland",
              Order: "071",
            },
            {
              Value: "FR",
              Display: "France",
              Order: "072",
            },
            {
              Value: "GF",
              Display: "French Guiana",
              Order: "073",
            },
            {
              Value: "PF",
              Display: "French Polynesia",
              Order: "074",
            },
            {
              Value: "GA",
              Display: "Gabon",
              Order: "075",
            },
            {
              Value: "GM",
              Display: "Gambia",
              Order: "076",
            },
            {
              Value: "GZ",
              Display: "Gaza Strip",
              Order: "077",
            },
            {
              Value: "GE",
              Display: "Georgia",
              Order: "078",
            },
            {
              Value: "DE",
              Display: "Germany",
              Order: "079",
            },
            {
              Value: "GH",
              Display: "Ghana",
              Order: "080",
            },
            {
              Value: "GI",
              Display: "Gibraltar",
              Order: "081",
            },
            {
              Value: "GR",
              Display: "Greece",
              Order: "082",
            },
            {
              Value: "GL",
              Display: "Greenland",
              Order: "083",
            },
            {
              Value: "GD",
              Display: "Grenada",
              Order: "084",
            },
            {
              Value: "GP",
              Display: "Guadeloupe",
              Order: "085",
            },
            {
              Value: "GT",
              Display: "Guatemala",
              Order: "086",
            },
            {
              Value: "GG",
              Display: "Guernsey",
              Order: "087",
            },
            {
              Value: "GN",
              Display: "Guinea",
              Order: "088",
            },
            {
              Value: "GW",
              Display: "Guinea-Bissau",
              Order: "089",
            },
            {
              Value: "GY",
              Display: "Guyana",
              Order: "090",
            },
            {
              Value: "HT",
              Display: "Haiti",
              Order: "091",
            },
            {
              Value: "VA",
              Display: "Holy See [Vatican City State]",
              Order: "092",
            },
            {
              Value: "HN",
              Display: "Honduras",
              Order: "093",
            },
            {
              Value: "HK",
              Display: "Hong Kong",
              Order: "094",
            },
            {
              Value: "HU",
              Display: "Hungary",
              Order: "095",
            },
            {
              Value: "IS",
              Display: "Iceland",
              Order: "096",
            },
            {
              Value: "IN",
              Display: "India",
              Order: "097",
            },
            {
              Value: "ID",
              Display: "Indonesia",
              Order: "098",
            },
            {
              Value: "IR",
              Display: "Iran (Islamic Republic of)",
              Order: "099",
            },
            {
              Value: "IQ",
              Display: "Iraq",
              Order: "100",
            },
            {
              Value: "IE",
              Display: "Ireland",
              Order: "101",
            },
            {
              Value: "IM",
              Display: "Isle of Man",
              Order: "102",
            },
            {
              Value: "IL",
              Display: "Israel",
              Order: "103",
            },
            {
              Value: "IT",
              Display: "Italy",
              Order: "104",
            },
            {
              Value: "JM",
              Display: "Jamaica",
              Order: "105",
            },
            {
              Value: "JP",
              Display: "Japan",
              Order: "106",
            },
            {
              Value: "JE",
              Display: "Jersey",
              Order: "107",
            },
            {
              Value: "JO",
              Display: "Jordan",
              Order: "108",
            },
            {
              Value: "KZ",
              Display: "Kazakhstan",
              Order: "109",
            },
            {
              Value: "KE",
              Display: "Kenya",
              Order: "110",
            },
            {
              Value: "KI",
              Display: "Kiribati",
              Order: "111",
            },
            {
              Value: "KP",
              Display: "Korea (the Democratic People's Republic of)",
              Order: "112",
            },
            {
              Value: "KR",
              Display: "Korea (the Republic of)",
              Order: "113",
            },
            {
              Value: "KS",
              Display: "Kosovo",
              Order: "114",
            },
            {
              Value: "KW",
              Display: "Kuwait",
              Order: "115",
            },
            {
              Value: "KG",
              Display: "Kyrgyzstan",
              Order: "116",
            },
            {
              Value: "LA",
              Display: "Lao People's Democratic Republic (the)",
              Order: "117",
            },
            {
              Value: "LV",
              Display: "Latvia",
              Order: "118",
            },
            {
              Value: "LB",
              Display: "Lebanon",
              Order: "119",
            },
            {
              Value: "LS",
              Display: "Lesotho",
              Order: "120",
            },
            {
              Value: "LR",
              Display: "Liberia",
              Order: "121",
            },
            {
              Value: "LY",
              Display: "Libya",
              Order: "122",
            },
            {
              Value: "LI",
              Display: "Liechtenstein",
              Order: "123",
            },
            {
              Value: "LT",
              Display: "Lithuania",
              Order: "124",
            },
            {
              Value: "LU",
              Display: "Luxembourg",
              Order: "125",
            },
            {
              Value: "MO",
              Display: "Macao",
              Order: "126",
            },
            {
              Value: "MG",
              Display: "Madagascar",
              Order: "127",
            },
            {
              Value: "MW",
              Display: "Malawi",
              Order: "128",
            },
            {
              Value: "MY",
              Display: "Malaysia",
              Order: "129",
            },
            {
              Value: "MV",
              Display: "Maldives",
              Order: "130",
            },
            {
              Value: "ML",
              Display: "Mali",
              Order: "131",
            },
            {
              Value: "MT",
              Display: "Malta",
              Order: "132",
            },
            {
              Value: "MQ",
              Display: "Martinique",
              Order: "133",
            },
            {
              Value: "MR",
              Display: "Mauritania",
              Order: "134",
            },
            {
              Value: "MU",
              Display: "Mauritius",
              Order: "135",
            },
            {
              Value: "YT",
              Display: "Mayotte",
              Order: "136",
            },
            {
              Value: "MX",
              Display: "Mexico",
              Order: "137",
            },
            {
              Value: "MD",
              Display: "Moldova (the Republic of)",
              Order: "138",
            },
            {
              Value: "MC",
              Display: "Monaco",
              Order: "139",
            },
            {
              Value: "MN",
              Display: "Mongolia",
              Order: "140",
            },
            {
              Value: "ME",
              Display: "Montenegro",
              Order: "141",
            },
            {
              Value: "MS",
              Display: "Montserrat",
              Order: "142",
            },
            {
              Value: "MA",
              Display: "Morocco",
              Order: "143",
            },
            {
              Value: "MZ",
              Display: "Mozambique",
              Order: "144",
            },
            {
              Value: "MM",
              Display: "Myanmar",
              Order: "145",
            },
            {
              Value: "NA",
              Display: "Namibia",
              Order: "146",
            },
            {
              Value: "NR",
              Display: "Nauru",
              Order: "147",
            },
            {
              Value: "NP",
              Display: "Nepal",
              Order: "148",
            },
            {
              Value: "NL",
              Display: "Netherlands",
              Order: "149",
            },
            {
              Value: "NC",
              Display: "New Caledonia",
              Order: "150",
            },
            {
              Value: "NZ",
              Display: "New Zealand",
              Order: "151",
            },
            {
              Value: "NI",
              Display: "Nicaragua",
              Order: "152",
            },
            {
              Value: "NE",
              Display: "Niger",
              Order: "153",
            },
            {
              Value: "NG",
              Display: "Nigeria",
              Order: "154",
            },
            {
              Value: "NU",
              Display: "Niue",
              Order: "155",
            },
            {
              Value: "MK",
              Display: "North Macedonia",
              Order: "156",
            },
            {
              Value: "NO",
              Display: "Norway",
              Order: "157",
            },
            {
              Value: "OM",
              Display: "Oman",
              Order: "158",
            },
            {
              Value: "PK",
              Display: "Pakistan",
              Order: "159",
            },
            {
              Value: "PS",
              Display: "Palestine, State of",
              Order: "160",
            },
            {
              Value: "PA",
              Display: "Panama",
              Order: "161",
            },
            {
              Value: "PG",
              Display: "Papua New Guinea",
              Order: "162",
            },
            {
              Value: "PY",
              Display: "Paraguay",
              Order: "163",
            },
            {
              Value: "PE",
              Display: "Peru",
              Order: "164",
            },
            {
              Value: "PH",
              Display: "Philippines",
              Order: "165",
            },
            {
              Value: "PL",
              Display: "Poland",
              Order: "166",
            },
            {
              Value: "PT",
              Display: "Portugal",
              Order: "167",
            },
            {
              Value: "QA",
              Display: "Qatar",
              Order: "168",
            },
            {
              Value: "RE",
              Display: "Réunion",
              Order: "169",
            },
            {
              Value: "RO",
              Display: "Romania",
              Order: "170",
            },
            {
              Value: "RU",
              Display: "Russian Federation",
              Order: "171",
            },
            {
              Value: "RW",
              Display: "Rwanda",
              Order: "172",
            },
            {
              Value: "BL",
              Display: "Saint Barthélemy",
              Order: "173",
            },
            {
              Value: "SH",
              Display: "Saint Helena, Ascension and Tristan da Cunha",
              Order: "174",
            },
            {
              Value: "KN",
              Display: "Saint Kitts and Nevis",
              Order: "175",
            },
            {
              Value: "LC",
              Display: "Saint Lucia",
              Order: "176",
            },
            {
              Value: "MF",
              Display: "Saint Martin (French part)",
              Order: "177",
            },
            {
              Value: "PM",
              Display: "Saint Pierre and Miquelon",
              Order: "178",
            },
            {
              Value: "VC",
              Display: "Saint Vincent and the Grenadines",
              Order: "179",
            },
            {
              Value: "WS",
              Display: "Samoa",
              Order: "180",
            },
            {
              Value: "SM",
              Display: "San Marino",
              Order: "181",
            },
            {
              Value: "ST",
              Display: "Sao Tome and Principe",
              Order: "182",
            },
            {
              Value: "SA",
              Display: "Saudi Arabia",
              Order: "183",
            },
            {
              Value: "SN",
              Display: "Senegal",
              Order: "184",
            },
            {
              Value: "RS",
              Display: "Serbia",
              Order: "185",
            },
            {
              Value: "SC",
              Display: "Seychelles",
              Order: "186",
            },
            {
              Value: "SL",
              Display: "Sierra Leone",
              Order: "187",
            },
            {
              Value: "SG",
              Display: "Singapore",
              Order: "188",
            },
            {
              Value: "SX",
              Display: "Sint Maarten (Dutch part)",
              Order: "189",
            },
            {
              Value: "SK",
              Display: "Slovakia",
              Order: "190",
            },
            {
              Value: "SI",
              Display: "Slovenia",
              Order: "191",
            },
            {
              Value: "SB",
              Display: "Solomon Islands",
              Order: "192",
            },
            {
              Value: "SO",
              Display: "Somalia",
              Order: "193",
            },
            {
              Value: "ZA",
              Display: "South Africa",
              Order: "194",
            },
            {
              Value: "SS",
              Display: "South Sudan",
              Order: "195",
            },
            {
              Value: "ES",
              Display: "Spain",
              Order: "196",
            },
            {
              Value: "LK",
              Display: "Sri Lanka",
              Order: "197",
            },
            {
              Value: "SD",
              Display: "Sudan",
              Order: "198",
            },
            {
              Value: "SR",
              Display: "Suriname",
              Order: "199",
            },
            {
              Value: "SE",
              Display: "Sweden",
              Order: "200",
            },
            {
              Value: "CH",
              Display: "Switzerland",
              Order: "201",
            },
            {
              Value: "SY",
              Display: "Syrian Arab Republic",
              Order: "202",
            },
            {
              Value: "TW",
              Display: "Taiwan (Province of China)",
              Order: "203",
            },
            {
              Value: "TJ",
              Display: "Tajikistan",
              Order: "204",
            },
            {
              Value: "TZ",
              Display: "Tanzania, United Republic of",
              Order: "205",
            },
            {
              Value: "TH",
              Display: "Thailand",
              Order: "206",
            },
            {
              Value: "TL",
              Display: "Timor-Leste",
              Order: "207",
            },
            {
              Value: "TG",
              Display: "Togo",
              Order: "208",
            },
            {
              Value: "TK",
              Display: "Tokelau",
              Order: "209",
            },
            {
              Value: "TO",
              Display: "Tonga",
              Order: "210",
            },
            {
              Value: "TT",
              Display: "Trinidad and Tobago",
              Order: "211",
            },
            {
              Value: "TN",
              Display: "Tunisia",
              Order: "212",
            },
            {
              Value: "TR",
              Display: "Turkey",
              Order: "213",
            },
            {
              Value: "TM",
              Display: "Turkmenistan",
              Order: "214",
            },
            {
              Value: "TC",
              Display: "Turks and Caicos Islands",
              Order: "215",
            },
            {
              Value: "TV",
              Display: "Tuvalu",
              Order: "216",
            },
            {
              Value: "UG",
              Display: "Uganda",
              Order: "217",
            },
            {
              Value: "UA",
              Display: "Ukraine",
              Order: "218",
            },
            {
              Value: "AE",
              Display: "United Arab Emirates",
              Order: "219",
            },
            {
              Value: "GB",
              Display: "United Kingdom ",
              Order: "220",
            },
            {
              Value: "US",
              Display: "United States",
              Order: "001",
            },
            {
              Value: "UY",
              Display: "Uruguay",
              Order: "221",
            },
            {
              Value: "UZ",
              Display: "Uzbekistan",
              Order: "222",
            },
            {
              Value: "VU",
              Display: "Vanuatu",
              Order: "223",
            },
            {
              Value: "VE",
              Display: "Venezuela (Bolivarian Republic of)",
              Order: "224",
            },
            {
              Value: "VN",
              Display: "Viet Nam",
              Order: "225",
            },
            {
              Value: "VG",
              Display: "Virgin Islands (British)",
              Order: "226",
            },
            {
              Value: "WF",
              Display: "Wallis and Futuna",
              Order: "227",
            },
            {
              Value: "WE",
              Display: "West Bank",
              Order: "228",
            },
            {
              Value: "EH",
              Display: "Western Sahara",
              Order: "229",
            },
            {
              Value: "YE",
              Display: "Yemen",
              Order: "230",
            },
            {
              Value: "ZM",
              Display: "Zambia",
              Order: "231",
            },
            {
              Value: "ZW",
              Display: "Zimbabwe",
              Order: "232",
            },
          ],
        },
        {
          Display: "State",
          Name: "State",
          DictItems: [
            {
              Value: "AL",
              Display: "Alabama",
              Order: "001",
            },
            {
              Value: "AK",
              Display: "Alaska",
              Order: "002",
            },
            {
              Value: "AB",
              Display: "Alberta",
              Order: "003",
            },
            {
              Value: "AS",
              Display: "American Samoa",
              Order: "004",
            },
            {
              Value: "AZ",
              Display: "Arizona",
              Order: "005",
            },
            {
              Value: "AR",
              Display: "Arkansas",
              Order: "006",
            },
            {
              Value: "AA",
              Display: "Armed Forces Americas",
              Order: "007",
            },
            {
              Value: "AE",
              Display: "Armed Forces Europe, Canada, M.E.",
              Order: "008",
            },
            {
              Value: "AP",
              Display: "Armed Forces Pacific",
              Order: "009",
            },
            {
              Value: "BC",
              Display: "British Columbia",
              Order: "010",
            },
            {
              Value: "CA",
              Display: "California",
              Order: "011",
            },
            {
              Value: "CO",
              Display: "Colorado",
              Order: "013",
            },
            {
              Value: "CT",
              Display: "Connecticut",
              Order: "014",
            },
            {
              Value: "DE",
              Display: "Delaware",
              Order: "015",
            },
            {
              Value: "DC",
              Display: "District of Columbia",
              Order: "016",
            },
            {
              Value: "FM",
              Display: "Federated States of Micronesia",
              Order: "017",
            },
            {
              Value: "FL",
              Display: "Florida",
              Order: "018",
            },
            {
              Value: "GA",
              Display: "Georgia",
              Order: "020",
            },
            {
              Value: "GU",
              Display: "Guam",
              Order: "021",
            },
            {
              Value: "HI",
              Display: "Hawaii",
              Order: "022",
            },
            {
              Value: "ID",
              Display: "Idaho",
              Order: "023",
            },
            {
              Value: "IL",
              Display: "Illinois",
              Order: "024",
            },
            {
              Value: "IN",
              Display: "Indiana",
              Order: "025",
            },
            {
              Value: "IA",
              Display: "Iowa",
              Order: "026",
            },
            {
              Value: "KS",
              Display: "Kansas",
              Order: "027",
            },
            {
              Value: "KY",
              Display: "Kentucky",
              Order: "028",
            },
            {
              Value: "LA",
              Display: "Louisiana",
              Order: "029",
            },
            {
              Value: "ME",
              Display: "Maine",
              Order: "030",
            },
            {
              Value: "MB",
              Display: "Manitoba",
              Order: "031",
            },
            {
              Value: "MH",
              Display: "Marshall Islands",
              Order: "032",
            },
            {
              Value: "MD",
              Display: "Maryland",
              Order: "033",
            },
            {
              Value: "MA",
              Display: "Massachusetts",
              Order: "034",
            },
            {
              Value: "MI",
              Display: "Michigan",
              Order: "036",
            },
            {
              Value: "MN",
              Display: "Minnesota",
              Order: "037",
            },
            {
              Value: "MS",
              Display: "Mississippi",
              Order: "038",
            },
            {
              Value: "MO",
              Display: "Missouri",
              Order: "039",
            },
            {
              Value: "MT",
              Display: "Montana",
              Order: "040",
            },
            {
              Value: "NE",
              Display: "Nebraska",
              Order: "041",
            },
            {
              Value: "NV",
              Display: "Nevada",
              Order: "042",
            },
            {
              Value: "NB",
              Display: "New Brunswick",
              Order: "043",
            },
            {
              Value: "NH",
              Display: "New Hampshire",
              Order: "044",
            },
            {
              Value: "NJ",
              Display: "New Jersey",
              Order: "045",
            },
            {
              Value: "NM",
              Display: "New Mexico",
              Order: "046",
            },
            {
              Value: "NY",
              Display: "New York",
              Order: "047",
            },
            {
              Value: "NF",
              Display: "Newfoundland",
              Order: "048",
            },
            {
              Value: "NL",
              Display: "Newfoundland and Labrador",
              Order: "049",
            },
            {
              Value: "NC",
              Display: "North Carolina",
              Order: "050",
            },
            {
              Value: "ND",
              Display: "North Dakota",
              Order: "051",
            },
            {
              Value: "MP",
              Display: "Northern Mariana Islands",
              Order: "052",
            },
            {
              Value: "NT",
              Display: "Northwest Territories",
              Order: "053",
            },
            {
              Value: "NS",
              Display: "Nova Scotia",
              Order: "054",
            },
            {
              Value: "NU",
              Display: "Nunavut",
              Order: "055",
            },
            {
              Value: "OH",
              Display: "Ohio",
              Order: "056",
            },
            {
              Value: "OK",
              Display: "Oklahoma",
              Order: "057",
            },
            {
              Value: "ON",
              Display: "Ontario",
              Order: "058",
            },
            {
              Value: "OR",
              Display: "Oregon",
              Order: "059",
            },
            {
              Value: "PW",
              Display: "Palau",
              Order: "060",
            },
            {
              Value: "PA",
              Display: "Pennsylvania",
              Order: "061",
            },
            {
              Value: "PE",
              Display: "Prince Edward Island",
              Order: "062",
            },
            {
              Value: "PR",
              Display: "Puerto Rico",
              Order: "063",
            },
            {
              Value: "QC",
              Display: "Quebec",
              Order: "064",
            },
            {
              Value: "RI",
              Display: "Rhode Island",
              Order: "065",
            },
            {
              Value: "SK",
              Display: "Saskatchewan",
              Order: "066",
            },
            {
              Value: "SC",
              Display: "South Carolina",
              Order: "067",
            },
            {
              Value: "SD",
              Display: "South Dakota",
              Order: "068",
            },
            {
              Value: "TN",
              Display: "Tennessee",
              Order: "069",
            },
            {
              Value: "TX",
              Display: "Texas",
              Order: "070",
            },
            {
              Value: "UT",
              Display: "Utah",
              Order: "071",
            },
            {
              Value: "VT",
              Display: "Vermont",
              Order: "072",
            },
            {
              Value: "VI",
              Display: "Virgin Islands",
              Order: "073",
            },
            {
              Value: "VA",
              Display: "Virginia",
              Order: "074",
            },
            {
              Value: "WA",
              Display: "Washington",
              Order: "075",
            },
            {
              Value: "WV",
              Display: "West Virginia",
              Order: "076",
            },
            {
              Value: "WI",
              Display: "Wisconsin",
              Order: "077",
            },
            {
              Value: "WY",
              Display: "Wyoming",
              Order: "078",
            },
            {
              Value: "YT",
              Display: "Yukon",
              Order: "079",
            },
            {
              Value: "FC",
              Display: "None of the above",
              Order: "080",
            },
          ],
        },
      ],
      Count: 0,
    },
  },
  {
    PageName: "ParentalRelationships",
    PageHeading: "Parent Relationships",
    NextPageName: "ParentalRelationships2",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent:
          "In this section we'll ask basic information about your parents. Please include all parents relevant to the student",
      },
    ],
    FWDictsList: {
      Dicts: [],
      Count: 0,
    },
  },
  {
    PageName: "ParentalRelationships5",
    PageHeading: "Parent Relationships",
    NextPageName: "",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent: "What are Joe's parental relationships?",
      },
      {
        itemType: "question",
        id: "f_stu_mother_name",
        questionValue: "Mary Poppins",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "card",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent: "Mary Poppins",
      },
      {
        itemType: "question",
        id: "f_stu_father_name",
        questionValue: "Jerry Smith",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "card",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg:
          "This parent is missing information. Please edit the parent's information or remove them",
        helpContent: "",
        textContent: "Jerry Smith",
      },
    ],
    FWDictsList: {
      Dicts: [],
      Count: 0,
    },
  },
  {
    PageName: "ParentalRelationships2",
    PageHeading: "Parent Relationships",
    NextPageName: "ParentalRelationships5",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent: "What are Joe's parental relationships?",
      },
      {
        itemType: "question",
        id: "f_stu_mother_name",
        questionValue: "Mary Poppins",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "card",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent: "Mary Poppins",
      },
      {
        itemType: "question",
        id: "f_stu_father_name",
        questionValue: "Jerry Smith",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "card",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent: "Jerry Smith",
      },
    ],
    FWDictsList: {
      Dicts: [],
      Count: 0,
    },
  },
  {
    PageName: "ParentInfo",
    PageHeading: "Tell us about Parent",
    NextPageName: "",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent: "",
      },
      {
        itemType: "question",
        id: "f_parent_first_name",
        questionValue: "Mary",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent: "Parent First name",
      },
      {
        itemType: "question",
        id: "f_parent_last_name",
        questionValue: "Smith",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent: "Parent Last name",
      },
      {
        itemType: "question",
        id: "f_stu_relationship_name",
        questionValue: "2",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "select",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "StudentMaritalStatus",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent: "Relationship",
      },
      {
        itemType: "question",
        id: "f_stu_dec_name",
        questionValue: "2",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "select",
        size: "40",
        minLength: "",
        maxLength: "35",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "ParentDecStatus",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent: "Deceased",
      },
    ],
    FWDictsList: {
      Dicts: [
        {
          Display: "StudentMaritalStatus",
          Name: "StudentMaritalStatus",
          DictItems: [
            {
              Value: "2",
              Display: "Mother",
              Order: "002",
            },
            {
              Value: "1",
              Display: "Father",
              Order: "001",
            },
            {
              Value: "4",
              Display: "Brother",
              Order: "004",
            },
          ],
        },
        {
          Display: "ParentDecStatus",
          Name: "ParentDecStatus",
          DictItems: [
            {
              Value: "1",
              Display: "Yes",
              Order: "001",
            },
            {
              Value: "2",
              Display: "No",
              Order: "002",
            },
            {
              Value: "4",
              Display: "Not Sure",
              Order: "004",
            },
          ],
        },
      ],
      Count: 0,
    },
  },
  {
    PageName: "YesNoButtons",
    PageHeading: "Business 1",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent:
          "Do other family members have ownership in this buisiness?",
      },
      {
        itemType: "question",
        id: "yes_no_button",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "yesnobutton",
        size: "15",
        minLength: "10",
        maxLength: "15",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "YesNo_YN",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "Ownership",
      },
    ],
    FWDictsList: {
      Dicts: [
        {
          Display: "YesNo_YN",
          Name: "YesNo_YN",
          DictItems: [
            {
              Value: "Y",
              Display: "Yes",
              Order: "001",
            },
            {
              Value: "N",
              Display: "No",
              Order: "002",
            },
          ],
        },
      ],
      Count: 0,
    },
  },
  {
    PageName: "CongratsGettingStarted",
    PageHeading: "Great Work!",
    PageText:
      "You finished the Getting Started section. <br /> <b>You've complete 3 out of 10 sections.</b>",
    FWFormItemList: [
      {
        itemType: "question",
        id: "stu_info_congrats",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "",
        size: "15",
        minLength: "10",
        maxLength: "15",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "Congrats",
        checkboxCheckedValue: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        helpStyle: "",
        helpTitle: "",
        textContent: "",
      },
    ],
    FWDictsList: {
      Dicts: [
        {
          Display: "Congrats",
          Name: "Congrats",
          DictItems: [
            {
              Value: "StudentIncomeResources",
              Display: "Start Next Section",
              Order: "001",
            },
            {
              Value: "ApplicationChecklistCompleted",
              Display: "Back To Application Summary",
              Order: "002",
            },
          ],
        },
      ],
      Count: 0,
    },
  },
]

export const studentIncome = [
  {
    ReturnMessage: "",
    Username: "<EMAIL>",
    UserId: "iFimE3bkJ2ut1ARLKu83IHX44gYmPzYxaZwiXAlVXOenM9vCr@B73Q==",
    AwardYear: "2023",
    ApplicationId: "VSSEoWNmxzWPJc7yLdQSLoVbPROmwAmwTq5k6okmKwDTd2L1TvW4@g==",
    OrgFormId: "2nl2Wfz/cBFkq/wrgCw8Mp2vHYCfcF5DCKXalH@99Bt0dIUzE5@Ofw==",
    PageName: "StudentIncomeResources",
    FormId: "CB000000651",
    CBFinAidID: "ZAAAA1B",
    PageHeading: "Student Resources",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent:
          "How much does Mary expect to receive from the following sources to pay for educational expenses for the 2022-23 academic year?",
      },
      {
        itemType: "question",
        id: "q_stu_par_offer",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "16",
        minLength: "1",
        maxLength: "10",
        format: "money",
        displayFormat: "",
        rangeHigh: "9999999999999999",
        rangeLow: "0",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "VarOrphanWardOfCourtIndicator",
        condDsblParVal: "Y",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "VarOrphanWardOfCourtIndicator",
        condReqParVal: ";BLANK;N;",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Parents</b> - Provide the best estimate of what the student\\'s parents completing this application plan to pay from their income and assets for the student\\'s educational expenses (tuition and fees, books and supplies, room and board, transportation, and personal expenses). <br />  <br /> Do not include amounts they plan to borrow. <br />  <br /> Do not include amounts from family members other than the parents.  <br />",
        textContent: "Their parents",
      },
      {
        itemType: "question",
        id: "q_stu_expect_grants",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "16",
        minLength: "1",
        maxLength: "10",
        format: "money",
        displayFormat: "",
        rangeHigh: "9999999999999999",
        rangeLow: "0",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "VarHHA",
        condDsblParVal: ";BLANK;N;",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "VarHHA",
        condReqParVal: ";Y;",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Scholarships/grants</b> - Report private or local grants, scholarships, and other gift aid that the student has received or will receive for 2022-23, including AmeriCorp benefits and competitively awarded tuition exchange scholarships.<br />  <br /> Do not report federal or state government grants such as the Pell Grant.<br />  <br />Only include money the student has not yet received if you are certain the student will receive the award.<br />  <br />Do not include money the student earned from work or has to repay, such as loans or money earned through work-study.<br />  <br />List the sources in the special circumstances section at the end of this application. <br />",
        textContent:
          "Scholarships/grants from sources other than the colleges or universities to which they are applying",
      },
      {
        itemType: "question",
        id: "q_stu_tuition_ben",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "16",
        minLength: "1",
        maxLength: "10",
        format: "money",
        displayFormat: "",
        rangeHigh: "9999999999999999",
        rangeLow: "0",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Employer Benefits </b> - Tuition benefits are a type of employee benefit in which an employer reimburses employees or pays for costs associated with college,such as tuition, fees, and books.<br />  <br />If the student received a competitively-awarded employer-sponsored scholarship, report it in the question above (scholarships/grants), not here. <br />  <br /> If these benefits can be used only at certain schools or have other limitations, explain in the Special Circumstances section at the end of the application. <br />",
        textContent:
          "Employers, (i.e. tuition benefits) including their parents' employer(s) and/or their employer",
      },
      {
        itemType: "question",
        id: "q_stu_relative_supp",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "16",
        minLength: "1",
        maxLength: "10",
        format: "money",
        displayFormat: "",
        rangeHigh: "9999999999999999",
        rangeLow: "0",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent:
          "Relatives other than their parents and any other sources providing funds to help pay for college expenses",
      },
    ],
    FWDictsList: {
      Dicts: [],
      Count: 0,
    },
  },
  {
    ReturnMessage: "",
    Username: "<EMAIL>",
    UserId: "iFimE3bkJ2ut1ARLKu83IHX44gYmPzYxaZwiXAlVXOenM9vCr@B73Q==",
    AwardYear: "2023",
    ApplicationId: "VSSEoWNmxzWPJc7yLdQSLoVbPROmwAmwTq5k6okmKwDTd2L1TvW4@g==",
    OrgFormId: "2nl2Wfz/cBFkq/wrgCw8Mp2vHYCfcF5DCKXalH@99Bt0dIUzE5@Ofw==",
    PageName: "StudentIncomeBenefitsIntl",
    FormId: "CB000000651",
    CBFinAidID: "ZAAAA1B",
    PageHeading: "International Student Benefits",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent: "Answer the following questions about Mary.",
      },
      {
        itemType: "question",
        id: "q_stu_any_yr_gov_supp",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "select",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "YesNo_YN",
        questionMsg: "",
        condDsblChild: "Y",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent:
          "Will they receive funds from the government for college expenses at any point during attendance?   ",
      },
      {
        itemType: "question",
        id: "q_stu_any_yr_gov_supp2",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "text",
        size: "50",
        minLength: "",
        maxLength: "50",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "q_stu_any_yr_gov_supp",
        condDsblParVal: ";BLANK;N;",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "Y",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent:
          "Detail the amount and the years they expect to receive this benefit.",
      },
      {
        itemType: "question",
        id: "q_stu_any_yr_agency_supp",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "select",
        size: "",
        minLength: "",
        maxLength: "",
        format: "select",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "YesNo_YN",
        questionMsg: "",
        condDsblChild: "Y",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent:
          "Will they receive funds from any agencies or foundations for college expenses at any point during attendance? ",
      },
      {
        itemType: "question",
        id: "q_stu_any_yr_agency_supp2",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "text",
        size: "50",
        minLength: "",
        maxLength: "50",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "q_stu_any_yr_agency_supp",
        condDsblParVal: ";BLANK;N;",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "Y",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent:
          "Detail the amount and the years they expect to receive this benefit.",
      },
      {
        itemType: "question",
        id: "q_stu_any_yr_tuition_ben",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "select",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "YesNo_YN",
        questionMsg: "",
        condDsblChild: "Y",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Tuition Benefits</b> - Include all benefits the student expects to receive, including from the student\\'s employer, the student\\'s parent\\'s employers, and/or the student\\'s spouse\\'s employer. <br /><br /> If the student received a competitively-awarded employer-sponsored scholarship, do not report it here.<br /><br /> If these benefits can be used only at certain schools or have other limitations, explain in the Special Circumstance section at the end of the application. <br />",
        textContent:
          "Will they receive tuition benefits during any year of attendance?",
      },
      {
        itemType: "question",
        id: "q_stu_any_yr_tuition_ben2",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "text",
        size: "50",
        minLength: "",
        maxLength: "50",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "q_stu_any_yr_tuition_ben",
        condDsblParVal: ";BLANK;N;",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "Y",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent:
          "Detail the amount and the years they expect to receive this benefit.",
      },
      {
        itemType: "question",
        id: "q_par_travel_payment_plan",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "select",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "PayTransport",
        questionMsg: "",
        condDsblChild: "Y",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Transportation to U.S.</b> - If the student will use money from more than one source, select the one that will cover the largest portion of the expense. <br />",
        textContent: "How will they pay for transportation to the U.S.?",
      },
      {
        itemType: "question",
        id: "TravelPlanDetail",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "text",
        size: "50",
        minLength: "",
        maxLength: "50",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "q_par_travel_payment_plan",
        condDsblParVal: ";BLANK;1;2;3;4;5;6;7;",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "Y",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Transportation to U.S. Details</b> - If you need additional space, provide the information in the special circumstances section at the end of the application. <br />",
        textContent: "Explain how they will pay for transportation to the U.S.",
      },
    ],
    FWDictsList: {
      Dicts: [
        {
          Display: "YesNo_YN",
          Name: "YesNo_YN",
          DictItems: [
            {
              Value: "Y",
              Display: "Yes",
              Order: "001",
            },
            {
              Value: "N",
              Display: "No",
              Order: "002",
            },
          ],
        },
        {
          Display: "PayTransport",
          Name: "PayTransport",
          DictItems: [
            {
              Value: "1",
              Display: "Parents' income/savings",
              Order: "001",
            },
            {
              Value: "2",
              Display: "Student's income/savings",
              Order: "002",
            },
            {
              Value: "3",
              Display: "Gift from relatives other than parents",
              Order: "003",
            },
            {
              Value: "4",
              Display: "Scholarship or grant",
              Order: "004",
            },
            {
              Value: "5",
              Display: "Sponsor funds",
              Order: "005",
            },
            {
              Value: "6",
              Display: "Government funds",
              Order: "006",
            },
            {
              Value: "7",
              Display: "Agency/foundation funds",
              Order: "007",
            },
            {
              Value: "8",
              Display: "Other",
              Order: "008",
            },
          ],
        },
      ],
      Count: 0,
    },
  },
  {
    ReturnMessage: "",
    Username: "<EMAIL>",
    UserId: "iFimE3bkJ2ut1ARLKu83IHX44gYmPzYxaZwiXAlVXOenM9vCr@B73Q==",
    AwardYear: "2023",
    ApplicationId: "VSSEoWNmxzWPJc7yLdQSLoVbPROmwAmwTq5k6okmKwDTd2L1TvW4@g==",
    OrgFormId: "2nl2Wfz/cBFkq/wrgCw8Mp2vHYCfcF5DCKXalH@99Bt0dIUzE5@Ofw==",
    PageName: "StudentIncomeBenefitsExpected",
    FormId: "CB000000651",
    CBFinAidID: "ZAAAA1B",
    PageHeading: "Student Expected Income and Benefits",
    FWFormItemList: [
      {
        itemType: "text",
        id: "Text0",
        questionValue: "",
        required: "",
        requiredErrMsg: "",
        fieldType: "",
        size: "",
        minLength: "",
        maxLength: "",
        format: "",
        displayFormat: "",
        rangeHigh: "",
        rangeLow: "",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent: "",
        textContent:
          "How much does Mary expect to receive from the following sources to pay for educational expenses for the 2022-23 academic year?",
      },
      {
        itemType: "question",
        id: "q_stu_summer_earnings",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "16",
        minLength: "1",
        maxLength: "10",
        format: "money",
        displayFormat: "",
        rangeHigh: "9999999999999999",
        rangeLow: "0",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Student Expected Earnings - Summer</b> - Estimate as best as you can based upon previous employment and current plans. This amount should include salary, bonuses, commissions, and vacation pay-out.  <br />  <br />  Estimate for a 3-month period.    <br />  <br />  Do not include earnings from Federal Work Study. <br />",
        textContent: "Mary's expected earnings - summer 2022",
      },
      {
        itemType: "question",
        id: "q_stu_sch_yr_earnings",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "16",
        minLength: "1",
        maxLength: "10",
        format: "money",
        displayFormat: "",
        rangeHigh: "9999999999999999",
        rangeLow: "0",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Student Expected Earnings - School Year</b> - Estimate as best as you can based upon previous employment and current plans. This amount should include salary, bonuses, commissions, and vacation pay-out.  <br />  <br />  Estimate for a 9-month period. <br />  <br />  Do not include earnings from Federal Work Study. <br />",
        textContent: "Mary's expected earnings - school year 2022-23",
      },
      {
        itemType: "question",
        id: "StuSpouseExpectedEarnings",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "text",
        size: "16",
        minLength: "1",
        maxLength: "10",
        format: "money",
        displayFormat: "",
        rangeHigh: "9999999999999999",
        rangeLow: "0",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "VarStuSpouse",
        condDsblParVal: ";BLANK;N;",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "Y",
        condReqChild: "",
        condReqParFld: "VarStuSpouse",
        condReqParVal: "Y",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Spouse Expected Earnings</b> - Estimate as best as you can based upon previous employment and current plans. This amount should include salary, bonuses, commissions, and vacation pay-out.  <br />  <br />  Estimate for a 12-month period. <br />  <br />  Do not include earnings from Federal Work Study. <br />",
        textContent:
          "student's spouse's expected earnings - June 2022-May 2023",
      },
      {
        itemType: "question",
        id: "q_stu_summer_oth_tax_inc",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "16",
        minLength: "1",
        maxLength: "10",
        format: "money",
        displayFormat: "",
        rangeHigh: "9999999999999999",
        rangeLow: "0",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Student Other Taxable income - Summer</b> - Estimate as best as you can for a 3-month period.  <br />  <br />Include the following:  interest and dividend income, capital gains, business income, farm income, alimony received, pensions, and annuities.   <br />",
        textContent: "Mary's expected other taxed income - summer 2022",
      },
      {
        itemType: "question",
        id: "q_stu_sch_yr_oth_tax_inc",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "16",
        minLength: "1",
        maxLength: "10",
        format: "money",
        displayFormat: "",
        rangeHigh: "9999999999999999",
        rangeLow: "0",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Student Other Taxable income - School Year</b> - Estimate as best as you can for a 9-month period. <br /> <br />Include the following:  interest and dividend income, capital gains, business income, farm income, alimony received, pensions, and annuities.  <br />",
        textContent: "Mary's expected other taxed income - school year 2022-23",
      },
      {
        itemType: "question",
        id: "StuSpouseExpectedOtherTaxedIncome",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "text",
        size: "16",
        minLength: "1",
        maxLength: "10",
        format: "money",
        displayFormat: "",
        rangeHigh: "9999999999999999",
        rangeLow: "0",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "VarStuSpouse",
        condDsblParVal: ";BLANK;N;",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "Y",
        condReqChild: "",
        condReqParFld: "VarStuSpouse",
        condReqParVal: "Y",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Spouse Expected Other Taxable Income</b> - Estimate as best as you can for a 12-month period. <br /> <br />Include the following:  interest and dividend income, capital gains, business income, farm income, alimony received, pensions, and annuities. <br />",
        textContent:
          "student's spouse's expected other taxed income - June 2022-May 2023",
      },
      {
        itemType: "question",
        id: "q_stu_summer_untax_inc",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "16",
        minLength: "1",
        maxLength: "10",
        format: "money",
        displayFormat: "",
        rangeHigh: "9999999999999999",
        rangeLow: "0",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Student Untaxed Income - Summer</b> - Estimate as best as you can for a 3-month period. <br />  <br /> Include the following:  untaxed social security benefits, TANF, child support received, untaxed IRA/retirement/pension payments and receipts, tax credits, tax deductions, living allowances, and workers\\' compensation.  <br />",
        textContent:
          "Mary's expected total untaxed income and benefits - summer 2022",
      },
      {
        itemType: "question",
        id: "q_stu_sch_yr_untax_inc",
        questionValue: "",
        required: "Y",
        requiredErrMsg: "",
        fieldType: "text",
        size: "16",
        minLength: "1",
        maxLength: "10",
        format: "money",
        displayFormat: "",
        rangeHigh: "9999999999999999",
        rangeLow: "0",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "",
        condDsblParVal: "",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "N",
        condReqChild: "",
        condReqParFld: "",
        condReqParVal: "",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Student Untaxed Income - School Year</b> - Estimate as best as you can for a 9-month period. <br /><br /> Include the following:  untaxed social security benefits, TANF, child support received, untaxed IRA/retirement/pension payments and receipts, tax credits, tax deductions, living allowances, and workers\\' compensation. <br/>",
        textContent:
          "Mary's expected total untaxed income and benefits - school year 2022-23",
      },
      {
        itemType: "question",
        id: "StuSpouseExpectedUntaxedIncome",
        questionValue: "",
        required: "N",
        requiredErrMsg: "",
        fieldType: "text",
        size: "16",
        minLength: "1",
        maxLength: "10",
        format: "money",
        displayFormat: "",
        rangeHigh: "9999999999999999",
        rangeLow: "0",
        dict: "",
        questionMsg: "",
        condDsblChild: "",
        condDsblParFld: "VarStuSpouse",
        condDsblParVal: ";BLANK;N;",
        condDsblParValNOTEQ: "",
        condDisableQuestion: "Y",
        condReqChild: "",
        condReqParFld: "VarStuSpouse",
        condReqParVal: "Y",
        condReqOperator: "",
        condReqParValNOTEQ: "",
        validateFldCompare: "",
        validateOperator: "",
        validateVal: "",
        validateRGEX: "",
        validateErrMsg: "",
        helpContent:
          "<b>Spouse Expected Untaxed Income</b> - Estimate as best as you can for a 12-month period. <br /><br />Include the following:  untaxed social security benefits, TANF, child support received, untaxed IRA/retirement/pension payments and receipts, tax credits, tax deductions, living allowances, and workers\\' compensation. <br/>",
        textContent:
          "student's spouse's expected total untaxed income and benefits - June 2022-May 2023",
      },
    ],
    FWDictsList: {
      Dicts: [],
      Count: 0,
    },
  },
]
